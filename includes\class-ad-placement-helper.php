<?php

/**

 * Ad Placement Helper Class for Epic Membership Plugin

 * Provides template functions and hooks for ad placement

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Ad_Placement_Helper {

    

    /**

     * Ad unit manager instance

     */

    private $ad_unit_manager;

    

    /**

     * Ad integration instance

     */

    private $ad_integration;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->ad_unit_manager = new Epic_Membership_Ad_Unit_Manager();

        $this->ad_integration = new Epic_Membership_Ad_Integration();

        

        $this->init_hooks();

    }

    

    /**

     * Initialize placement hooks

     */

    private function init_hooks() {

        // Custom placement hooks

        add_action('epic_membership_before_post_content', array($this, 'display_before_content_ads'));

        add_action('epic_membership_after_post_content', array($this, 'display_after_content_ads'));

        add_action('epic_membership_sidebar_ads', array($this, 'display_sidebar_ads'));

        add_action('epic_membership_body_end_ads', array($this, 'display_body_end_ads'));

        

        // Widget for ad placement

        add_action('widgets_init', array($this, 'register_ad_widget'));

    }

    

    /**

     * Display ads for specific placement

     */

    public function display_ads_by_placement($placement) {

        if (!$this->ad_integration->should_show_ads()) {

            return;

        }

        

        $ad_units = $this->ad_unit_manager->get_ad_units_by_placement($placement, true);

        

        if (empty($ad_units)) {

            return;

        }

        

        foreach ($ad_units as $ad_unit) {

            $this->render_ad_unit($ad_unit);

        }

    }

    

    /**

     * Display before content ads

     */

    public function display_before_content_ads() {

        $this->display_ads_by_placement('before_content');

    }

    

    /**

     * Display after content ads

     */

    public function display_after_content_ads() {

        $this->display_ads_by_placement('after_content');

    }

    

    /**

     * Display sidebar ads

     */

    public function display_sidebar_ads() {

        $this->display_ads_by_placement('sidebar');

    }



    /**

     * Display body-end ads (before closing </body> tag)

     */

    public function display_body_end_ads() {

        $this->display_ads_by_placement('body_end');

    }

    

    /**

     * Render individual ad unit

     */

    private function render_ad_unit($ad_unit) {

        // Check placement conditions if any

        if (!empty($ad_unit->placement_conditions)) {

            $conditions = json_decode($ad_unit->placement_conditions, true);

            if (!$this->check_placement_conditions($conditions)) {

                return;

            }

        }

        

        echo "\n<!-- Epic Membership - {$ad_unit->name} ({$ad_unit->ad_type}) -->\n";

        echo '<div class="epic-membership-ad-unit" data-ad-type="' . esc_attr($ad_unit->ad_type) . '" data-placement="' . esc_attr($ad_unit->placement) . '">';

        echo $ad_unit->ad_code;

        echo '</div>';

        echo "\n<!-- /Epic Membership - {$ad_unit->name} -->\n";

    }

    

    /**

     * Check placement conditions

     */

    private function check_placement_conditions($conditions) {

        if (empty($conditions)) {

            return true;

        }

        

        // Check post types

        if (isset($conditions['post_types']) && !empty($conditions['post_types'])) {

            $current_post_type = get_post_type();

            if (!in_array($current_post_type, $conditions['post_types'])) {

                return false;

            }

        }

        

        // Check specific pages

        if (isset($conditions['specific_pages']) && !empty($conditions['specific_pages'])) {

            $current_page_id = get_the_ID();

            if (!in_array($current_page_id, $conditions['specific_pages'])) {

                return false;

            }

        }

        

        // Check categories

        if (isset($conditions['categories']) && !empty($conditions['categories'])) {

            $post_categories = wp_get_post_categories(get_the_ID());

            if (empty(array_intersect($post_categories, $conditions['categories']))) {

                return false;

            }

        }

        

        // Check if homepage

        if (isset($conditions['show_on_homepage']) && !$conditions['show_on_homepage'] && is_home()) {

            return false;

        }

        

        // Check if archive pages

        if (isset($conditions['show_on_archives']) && !$conditions['show_on_archives'] && is_archive()) {

            return false;

        }

        

        return true;

    }

    

    /**

     * Register ad widget

     */

    public function register_ad_widget() {

        register_widget('Epic_Membership_Ad_Widget');

    }

    

    /**

     * Template function: Display ads by placement

     */

    public static function show_ads($placement) {

        $instance = new self();

        $instance->display_ads_by_placement($placement);

    }

    

    /**

     * Template function: Check if ads should be shown

     */

    public static function should_show_ads() {

        $ad_integration = new Epic_Membership_Ad_Integration();

        return $ad_integration->should_show_ads();

    }

    

    /**

     * Template function: Get ad units for placement

     */

    public static function get_ads_for_placement($placement) {

        if (!self::should_show_ads()) {

            return array();

        }

        

        $ad_unit_manager = new Epic_Membership_Ad_Unit_Manager();

        return $ad_unit_manager->get_ad_units_by_placement($placement, true);

    }

}



/**

 * Ad Widget Class

 */

class Epic_Membership_Ad_Widget extends WP_Widget {

    

    public function __construct() {

        parent::__construct(

            'epic_membership_ad_widget',

            __('Epic Membership Ads', 'epic-membership'),

            array('description' => __('Display Adsterra ads from Epic Membership plugin', 'epic-membership'))

        );

    }

    

    public function widget($args, $instance) {

        $placement = isset($instance['placement']) ? $instance['placement'] : 'sidebar';

        

        echo $args['before_widget'];

        

        if (!empty($instance['title'])) {

            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];

        }

        

        Epic_Membership_Ad_Placement_Helper::show_ads($placement);

        

        echo $args['after_widget'];

    }

    

    public function form($instance) {

        $title = !empty($instance['title']) ? $instance['title'] : '';

        $placement = !empty($instance['placement']) ? $instance['placement'] : 'sidebar';

        

        $ad_unit_manager = new Epic_Membership_Ad_Unit_Manager();

        $placements = $ad_unit_manager->get_supported_placements();

        ?>

        <p>

            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Title:', 'epic-membership'); ?></label>

            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">

        </p>

        <p>

            <label for="<?php echo esc_attr($this->get_field_id('placement')); ?>"><?php _e('Ad Placement:', 'epic-membership'); ?></label>

            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('placement')); ?>" name="<?php echo esc_attr($this->get_field_name('placement')); ?>">

                <?php foreach ($placements as $key => $label): ?>

                    <option value="<?php echo esc_attr($key); ?>" <?php selected($placement, $key); ?>><?php echo esc_html($label); ?></option>

                <?php endforeach; ?>

            </select>

        </p>

        <?php

    }

    

    public function update($new_instance, $old_instance) {

        $instance = array();

        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';

        $instance['placement'] = (!empty($new_instance['placement'])) ? sanitize_text_field($new_instance['placement']) : 'sidebar';

        

        return $instance;

    }

}



// Template functions for themes

if (!function_exists('epic_membership_show_ads')) {

    function epic_membership_show_ads($placement) {

        Epic_Membership_Ad_Placement_Helper::show_ads($placement);

    }

}



if (!function_exists('epic_membership_should_show_ads')) {

    function epic_membership_should_show_ads() {

        return Epic_Membership_Ad_Placement_Helper::should_show_ads();

    }

}



if (!function_exists('epic_membership_get_ads')) {

    function epic_membership_get_ads($placement) {

        return Epic_Membership_Ad_Placement_Helper::get_ads_for_placement($placement);

    }

}



if (!function_exists('epic_membership_show_body_end_ads')) {

    function epic_membership_show_body_end_ads() {

        Epic_Membership_Ad_Placement_Helper::show_ads('body_end');

    }

}

