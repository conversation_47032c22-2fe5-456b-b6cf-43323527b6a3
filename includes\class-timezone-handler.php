<?php

/**

 * Timezone Handler for Epic Membership Plugin

 * Handles timezone-aware scheduling and conversions

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Timezone_Handler {

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // AJAX handlers for timezone operations

        add_action('wp_ajax_epic_membership_update_timezone', array($this, 'ajax_update_user_timezone'));

        add_action('wp_ajax_nopriv_epic_membership_update_timezone', array($this, 'ajax_update_user_timezone'));

        add_action('wp_ajax_epic_membership_convert_timezone', array($this, 'ajax_convert_timezone'));

        add_action('wp_ajax_nopriv_epic_membership_convert_timezone', array($this, 'ajax_convert_timezone'));

        

        // Add timezone info to localized script data

        add_filter('epic_membership_localize_script_data', array($this, 'add_timezone_data'));

    }

    

    /**

     * Get WordPress site timezone

     */

    public function get_site_timezone() {

        return wp_timezone_string();

    }

    

    /**

     * Get user's stored timezone preference

     */

    public function get_user_timezone($user_id = null) {

        if (!$user_id) {

            $user_id = get_current_user_id();

        }

        

        if (!$user_id) {

            // For non-logged-in users, try to get from session

            if (session_status() === PHP_SESSION_NONE) {

                session_start();

            }

            return isset($_SESSION['epic_membership_timezone']) ? $_SESSION['epic_membership_timezone'] : $this->get_site_timezone();

        }

        

        $user_timezone = get_user_meta($user_id, 'epic_membership_timezone', true);

        return $user_timezone ?: $this->get_site_timezone();

    }

    

    /**

     * Set user's timezone preference

     */

    public function set_user_timezone($timezone, $user_id = null) {

        if (!$this->is_valid_timezone($timezone)) {

            return false;

        }

        

        if (!$user_id) {

            $user_id = get_current_user_id();

        }

        

        if (!$user_id) {

            // For non-logged-in users, store in session

            if (session_status() === PHP_SESSION_NONE) {

                session_start();

            }

            $_SESSION['epic_membership_timezone'] = $timezone;

            return true;

        }

        

        return update_user_meta($user_id, 'epic_membership_timezone', $timezone);

    }

    

    /**

     * Validate timezone string

     */

    public function is_valid_timezone($timezone) {

        return in_array($timezone, timezone_identifiers_list());

    }

    

    /**

     * Convert datetime from one timezone to another

     */

    public function convert_datetime($datetime, $from_timezone, $to_timezone) {

        try {

            $from_tz = new DateTimeZone($from_timezone);

            $to_tz = new DateTimeZone($to_timezone);

            

            $dt = new DateTime($datetime, $from_tz);

            $dt->setTimezone($to_tz);

            

            return $dt->format('Y-m-d H:i:s');

        } catch (Exception $e) {

            error_log('Epic Membership Timezone Conversion Error: ' . $e->getMessage());

            return $datetime; // Return original on error

        }

    }

    

    /**

     * Convert datetime to user's timezone

     */

    public function convert_to_user_timezone($datetime, $user_id = null) {

        $site_timezone = $this->get_site_timezone();

        $user_timezone = $this->get_user_timezone($user_id);

        

        if ($site_timezone === $user_timezone) {

            return $datetime;

        }

        

        return $this->convert_datetime($datetime, $site_timezone, $user_timezone);

    }

    

    /**

     * Convert datetime from user's timezone to site timezone

     */

    public function convert_from_user_timezone($datetime, $user_id = null) {

        $site_timezone = $this->get_site_timezone();

        $user_timezone = $this->get_user_timezone($user_id);

        

        if ($site_timezone === $user_timezone) {

            return $datetime;

        }

        

        return $this->convert_datetime($datetime, $user_timezone, $site_timezone);

    }

    

    /**

     * Check if content should be released based on scheduled time

     */

    public function is_content_released($scheduled_release, $release_timezone = null) {

        if (!$scheduled_release) {

            return true; // No schedule means always released

        }



        $release_timezone = $release_timezone ?: $this->get_site_timezone();



        // Get current time in UTC for accurate comparison

        $current_utc = gmdate('Y-m-d H:i:s');



        // Convert scheduled release time to UTC

        $scheduled_utc = $this->convert_datetime($scheduled_release, $release_timezone, 'UTC');



        return strtotime($current_utc) >= strtotime($scheduled_utc);

    }

    

    /**

     * Get time remaining until content release

     */

    public function get_time_until_release($scheduled_release, $release_timezone = null) {

        if (!$scheduled_release) {

            return 0;

        }



        $release_timezone = $release_timezone ?: $this->get_site_timezone();



        // Get current time in UTC for accurate comparison

        $current_utc = gmdate('Y-m-d H:i:s');



        // Convert scheduled release time to UTC

        $scheduled_utc = $this->convert_datetime($scheduled_release, $release_timezone, 'UTC');



        $time_diff = strtotime($scheduled_utc) - strtotime($current_utc);

        return max(0, $time_diff); // Return 0 if already released

    }

    

    /**

     * Format datetime for display in user's timezone

     */

    public function format_datetime_for_user($datetime, $format = null, $user_id = null) {

        if (!$format) {

            $format = get_option('date_format') . ' ' . get_option('time_format');

        }

        

        $user_datetime = $this->convert_to_user_timezone($datetime, $user_id);

        return date_i18n($format, strtotime($user_datetime));

    }

    

    /**

     * Get timezone offset in hours

     */

    public function get_timezone_offset($timezone) {

        try {

            $tz = new DateTimeZone($timezone);

            $now = new DateTime('now', $tz);

            return $now->getOffset() / 3600; // Convert seconds to hours

        } catch (Exception $e) {

            return 0;

        }

    }

    

    /**

     * Get timezone display name

     */

    public function get_timezone_display_name($timezone) {

        try {

            $tz = new DateTimeZone($timezone);

            $now = new DateTime('now', $tz);

            $offset = $now->getOffset();

            $offset_hours = $offset / 3600;

            

            $sign = $offset >= 0 ? '+' : '-';

            $hours = abs(floor($offset_hours));

            $minutes = abs(($offset_hours - floor($offset_hours)) * 60);

            

            return sprintf('UTC%s%02d:%02d (%s)', $sign, $hours, $minutes, $timezone);

        } catch (Exception $e) {

            return $timezone;

        }

    }

    

    /**

     * Get common timezones for selection

     */

    public function get_common_timezones() {

        return array(

            'UTC' => 'UTC',

            'America/New_York' => 'Eastern Time (US & Canada)',

            'America/Chicago' => 'Central Time (US & Canada)',

            'America/Denver' => 'Mountain Time (US & Canada)',

            'America/Los_Angeles' => 'Pacific Time (US & Canada)',

            'Europe/London' => 'London',

            'Europe/Paris' => 'Paris',

            'Europe/Berlin' => 'Berlin',

            'Asia/Tokyo' => 'Tokyo',

            'Asia/Shanghai' => 'Shanghai',

            'Asia/Kolkata' => 'Mumbai',

            'Australia/Sydney' => 'Sydney',

            'Pacific/Auckland' => 'Auckland'

        );

    }

    

    /**

     * AJAX handler for updating user timezone

     */

    public function ajax_update_user_timezone() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        $timezone = sanitize_text_field($_POST['timezone'] ?? '');

        

        if (!$this->is_valid_timezone($timezone)) {

            wp_send_json_error('Invalid timezone');

        }

        

        $result = $this->set_user_timezone($timezone);

        

        if ($result) {

            wp_send_json_success('Timezone updated');

        } else {

            wp_send_json_error('Failed to update timezone');

        }

    }

    

    /**

     * AJAX handler for timezone conversion

     */

    public function ajax_convert_timezone() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        $datetime = sanitize_text_field($_POST['datetime'] ?? '');

        $from_timezone = sanitize_text_field($_POST['from_timezone'] ?? '');

        $to_timezone = sanitize_text_field($_POST['to_timezone'] ?? '');

        

        if (!$datetime || !$from_timezone || !$to_timezone) {

            wp_send_json_error('Missing parameters');

        }

        

        if (!$this->is_valid_timezone($from_timezone) || !$this->is_valid_timezone($to_timezone)) {

            wp_send_json_error('Invalid timezone');

        }

        

        $converted = $this->convert_datetime($datetime, $from_timezone, $to_timezone);

        

        wp_send_json_success(array(

            'converted_datetime' => $converted,

            'formatted' => $this->format_datetime_for_user($converted)

        ));

    }

    

    /**

     * Add timezone data to localized script

     */

    public function add_timezone_data($data) {

        $data['timezone'] = array(

            'site' => $this->get_site_timezone(),

            'user' => $this->get_user_timezone(),

            'offset' => $this->get_timezone_offset($this->get_user_timezone()),

            'display_name' => $this->get_timezone_display_name($this->get_user_timezone())

        );

        

        return $data;

    }

    

    /**

     * Generate JavaScript for countdown timer

     */

    public function generate_countdown_js($release_datetime, $release_timezone = null) {

        $release_timezone = $release_timezone ?: $this->get_site_timezone();

        $user_timezone = $this->get_user_timezone();



        // Convert release time to UTC for consistent JavaScript handling

        $release_utc = $this->convert_datetime($release_datetime, $release_timezone, 'UTC');



        // Format as ISO 8601 for JavaScript Date constructor

        $release_iso = date('c', strtotime($release_utc . ' UTC'));



        return array(

            'release_time' => $release_iso, // ISO format for JavaScript

            'release_time_utc' => $release_utc, // UTC time for calculations

            'server_timezone' => $release_timezone,

            'user_timezone' => $user_timezone,

            'time_remaining' => $this->get_time_until_release($release_datetime, $release_timezone)

        );

    }

}

