<?php
/**
 * Test file for the new upgrade tier modal functionality
 * 
 * This file can be used to test the new Ko-fi style upgrade modal
 * across different WordPress themes.
 * 
 * Usage: Place this file in your WordPress root directory and access it via browser
 * Example: http://yoursite.com/test-upgrade-modal.php
 */

// Load WordPress
require_once('wp-config.php');
require_once(ABSPATH . 'wp-load.php');

// Check if Epic Membership plugin is active
if (!class_exists('Epic_Membership_Plugin')) {
    die('Epic Membership Plugin is not active. Please activate the plugin first.');
}

// Get current theme info
$theme = wp_get_theme();
$theme_name = $theme->get('Name');
$theme_version = $theme->get('Version');

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Epic Membership - Upgrade Modal Test</title>
    <?php wp_head(); ?>
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007cba;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #005a87;
            transform: translateY(-2px);
        }
        .test-button.free-tier {
            background: #28a745;
        }
        .test-button.free-tier:hover {
            background: #218838;
        }
        .theme-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .test-results {
            background: #f1f8e9;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            border-left: 4px solid #4caf50;
        }
        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body <?php body_class(); ?>>

<div class="test-container epic-membership-status-page">
    <div class="test-header">
        <h1>🚀 Epic Membership - Upgrade Modal Test</h1>
        <p>This page tests the new Ko-fi style upgrade tier modal functionality across different WordPress themes.</p>
    </div>

    <div class="theme-info">
        <h3>📋 Current Theme Information</h3>
        <p><strong>Theme:</strong> <?php echo esc_html($theme_name); ?></p>
        <p><strong>Version:</strong> <?php echo esc_html($theme_version); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
    </div>

    <div class="instructions">
        <h3>📝 Test Instructions</h3>
        <ol>
            <li>Click each test button below to open the upgrade modal</li>
            <li>Verify the modal displays correctly and is centered</li>
            <li>Test closing the modal using the X button, Cancel button, and ESC key</li>
            <li>For free tiers, test the activation process</li>
            <li>For paid tiers, verify PayPal integration opens correctly</li>
            <li>Test on different screen sizes (desktop, tablet, mobile)</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🆓 Free Tier Tests</h3>
        <p>These buttons simulate free tier upgrades that should activate immediately:</p>
        
        <button type="button" class="test-button free-tier epic-membership-upgrade-button"
                data-tier-id="1"
                data-tier-name="Basic Free">
            Test Free Tier - Basic
        </button>

        <button type="button" class="test-button free-tier epic-membership-upgrade-button"
                data-tier-id="2"
                data-tier-name="Premium Free">
            Test Free Tier - Premium
        </button>
    </div>

    <div class="test-section">
        <h3>💳 Paid Tier Tests</h3>
        <p>These buttons simulate paid tier upgrades that should redirect to PayPal:</p>
        
        <button type="button" class="test-button epic-membership-upgrade-button"
                data-tier-id="3"
                data-tier-name="Pro">
            Test Paid Tier - Pro ($9.99)
        </button>

        <button type="button" class="test-button epic-membership-upgrade-button"
                data-tier-id="4"
                data-tier-name="Enterprise">
            Test Paid Tier - Enterprise ($19.99)
        </button>
    </div>

    <div class="test-section">
        <h3>🎨 Theme Compatibility Tests</h3>
        <p>Test the modal with various theme-specific scenarios:</p>

        <button type="button" class="test-button epic-membership-upgrade-button"
                data-tier-id="5"
                data-tier-name="Theme Test">
            Test Theme Compatibility
        </button>

        <div style="margin-top: 15px;">
            <p><strong>Common themes to test:</strong></p>
            <ul>
                <li>Twenty Twenty-Four</li>
                <li>Astra</li>
                <li>GeneratePress</li>
                <li>OceanWP</li>
                <li>Elementor Hello</li>
                <li>Kadence</li>
                <li>Neve</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>⚖️ Ko-fi vs Upgrade Modal Comparison</h3>
        <p>Compare the working Ko-fi modal with the upgrade tier modal:</p>

        <?php if (class_exists('Epic_Membership_Kofi_Gateway')): ?>
            <button type="button" class="test-button epic-membership-kofi-button"
                    data-tier-id="6"
                    data-tier-name="Ko-fi Test"
                    data-tier-price="5.00"
                    style="background: #ff5f5f;">
                Test Ko-fi Modal (Working)
            </button>
        <?php else: ?>
            <p style="color: #666; font-style: italic;">Ko-fi integration not available for comparison</p>
        <?php endif; ?>

        <button type="button" class="test-button epic-membership-upgrade-button"
                data-tier-id="7"
                data-tier-name="Comparison Test">
            Test Upgrade Modal (Fixed)
        </button>

        <div style="background: #e3f2fd; padding: 15px; border-radius: 6px; margin-top: 15px;">
            <h4>Expected Behavior Comparison:</h4>
            <ul>
                <li><strong>Both modals should:</strong> Open without page reloads</li>
                <li><strong>Both modals should:</strong> Use similar event handling patterns</li>
                <li><strong>Both modals should:</strong> Have dedicated localization objects</li>
                <li><strong>Both modals should:</strong> Work consistently across themes</li>
            </ul>
        </div>
    </div>

    <div class="test-results">
        <h3>✅ Expected Results</h3>
        <ul>
            <li><strong>🔗 NO URL CHANGES:</strong> URL should remain exactly the same when clicking upgrade buttons (no hash symbols added)</li>
            <li><strong>Modal Display:</strong> Modal should appear centered on screen with proper overlay</li>
            <li><strong>Responsive Design:</strong> Modal should adapt to different screen sizes</li>
            <li><strong>Theme Compatibility:</strong> Modal should display correctly regardless of theme</li>
            <li><strong>Free Tier Activation:</strong> Should show success message and refresh page</li>
            <li><strong>Paid Tier Redirect:</strong> Should close modal and open PayPal payment options</li>
            <li><strong>Accessibility:</strong> Modal should be keyboard accessible (ESC to close)</li>
            <li><strong>Loading States:</strong> Should show loading spinner during processing</li>
        </ul>

        <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin-top: 15px; border-left: 4px solid #ffc107;">
            <h4>🔍 URL Hash Issue Testing</h4>
            <p><strong>Watch the browser address bar:</strong> When you click any upgrade button, the URL should NOT change at all. If you see a hash symbol (#) added to the end of the URL, the issue is not fixed.</p>
            <p><strong>Console Monitoring:</strong> Open browser console (F12) to see real-time URL monitoring. Any URL changes will be logged and alerted.</p>
            <p><strong>Current URL:</strong> <code id="current-url-display"></code></p>
        </div>
    </div>

    <div class="test-section">
        <h3>🐛 Troubleshooting</h3>
        <p>If the modal doesn't work as expected:</p>
        <ol>
            <li>Check browser console for JavaScript errors</li>
            <li>Verify Epic Membership plugin is active and up to date</li>
            <li>Ensure jQuery is loaded properly</li>
            <li>Check for theme conflicts by switching to a default WordPress theme</li>
            <li>Verify AJAX endpoints are accessible</li>
        </ol>

        <p><strong>Browser Console:</strong> Press F12 and check the Console tab for any error messages.</p>

        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 15px;">
            <h4>🔧 Debug Information</h4>
            <button type="button" onclick="showDebugInfo()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                Show Debug Info
            </button>
            <button type="button" onclick="testEventHandler()" style="background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                Test Event Handler
            </button>
            <div id="debug-output" style="margin-top: 10px; font-family: monospace; font-size: 12px; background: white; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>
    </div>
</div>

<script>
// Add some debugging information
jQuery(document).ready(function($) {
    console.log('Epic Membership Modal Test Page Loaded');
    console.log('Theme:', '<?php echo esc_js($theme_name); ?>');
    console.log('jQuery Version:', $.fn.jquery);
    
    // Check if epicMembershipUpgrade object is available (dedicated object like Ko-fi)
    if (typeof window.epicMembershipUpgrade !== 'undefined') {
        console.log('✅ epicMembershipUpgrade object available:', window.epicMembershipUpgrade);
        if (window.epicMembershipUpgrade.ajaxUrl) {
            console.log('✅ AJAX URL available:', window.epicMembershipUpgrade.ajaxUrl);
        } else {
            console.error('❌ AJAX URL not available in epicMembershipUpgrade object');
        }
        if (window.epicMembershipUpgrade.nonce) {
            console.log('✅ Nonce available');
        } else {
            console.error('❌ Nonce not available in epicMembershipUpgrade object');
        }
    } else {
        console.error('❌ epicMembershipUpgrade object not found! This will cause page reloads.');
    }

    // Also check the old epicMembership object for comparison
    if (typeof window.epicMembership !== 'undefined') {
        console.log('ℹ️ epicMembership object also available (for other features)');
    } else {
        console.warn('⚠️ epicMembership object not found');
    }

    // Check if our modal script is loaded
    if (typeof window.EpicMembershipUpgradeTierModal !== 'undefined') {
        console.log('✅ Upgrade Tier Modal script loaded successfully');

        // Test if event handlers are bound
        setTimeout(function() {
            var events = $._data(document, 'events');
            if (events && events.click) {
                var upgradeEvents = events.click.filter(function(event) {
                    return event.namespace === 'upgrade';
                });
                if (upgradeEvents.length > 0) {
                    console.log('✅ Upgrade event handlers bound:', upgradeEvents.length);
                } else {
                    console.error('❌ No upgrade event handlers found!');
                }
            } else {
                console.warn('⚠️ No click events found on document');
            }
        }, 500);
    } else {
        console.error('❌ Upgrade Tier Modal script not found');
    }

    // Check if PayPal integration is available
    if (typeof window.EpicMembershipPayPal !== 'undefined') {
        console.log('✅ PayPal integration available');
    } else {
        console.warn('⚠️ PayPal integration not available');
    }
    
    // Monitor URL changes to detect hash issues
    var originalUrl = window.location.href;
    console.log('Original URL:', originalUrl);

    // Display current URL in the test page
    $('#current-url-display').text(originalUrl);

    // Log when modal buttons are clicked
    $('.epic-membership-upgrade-button').on('click', function() {
        var tierId = $(this).data('tier-id');
        var tierName = $(this).data('tier-name');
        console.log('Modal test button clicked:', {tierId: tierId, tierName: tierName});

        // Check for URL changes after a short delay
        setTimeout(function() {
            var currentUrl = window.location.href;
            if (currentUrl !== originalUrl) {
                console.error('❌ URL CHANGED! Original:', originalUrl, 'Current:', currentUrl);
                alert('URL Hash Issue Detected!\nOriginal: ' + originalUrl + '\nCurrent: ' + currentUrl);
            } else {
                console.log('✅ URL remained unchanged:', currentUrl);
            }
        }, 100);
    });

    // Monitor for any URL hash changes
    window.addEventListener('hashchange', function() {
        console.error('❌ Hash change detected! New URL:', window.location.href);
        alert('Hash change detected! URL: ' + window.location.href);
    });

    // Monitor for page reloads/navigation
    window.addEventListener('beforeunload', function(e) {
        console.error('❌ Page is about to reload/navigate! This should not happen when clicking upgrade buttons.');
        // Don't show confirmation dialog in test environment, just log
    });

    // Add visual indicator when buttons are clicked
    $('.epic-membership-upgrade-button').on('click', function() {
        var $button = $(this);
        $button.css('background-color', '#ff6b6b');
        $button.text('Processing...');

        // Reset button after a delay if no modal appears
        setTimeout(function() {
            if (!$('#epic-membership-upgrade-modal').is(':visible')) {
                $button.css('background-color', '');
                $button.text($button.data('original-text') || 'Upgrade Button');
                console.error('❌ Modal did not appear - this indicates a JavaScript error or missing dependencies');
            }
        }, 1000);
    });

    // Store original button text
    $('.epic-membership-upgrade-button').each(function() {
        $(this).data('original-text', $(this).text());
    });
});

// Debug function
function showDebugInfo() {
    var debugInfo = [];

    debugInfo.push('=== Epic Membership Debug Information ===');
    debugInfo.push('Current URL: ' + window.location.href);
    debugInfo.push('jQuery Version: ' + (window.jQuery ? jQuery.fn.jquery : 'Not loaded'));
    debugInfo.push('');

    debugInfo.push('=== epicMembershipUpgrade Object (Dedicated) ===');
    if (typeof window.epicMembershipUpgrade !== 'undefined') {
        debugInfo.push('✅ epicMembershipUpgrade object exists');
        debugInfo.push('AJAX URL: ' + (window.epicMembershipUpgrade.ajaxUrl || 'NOT SET'));
        debugInfo.push('Nonce: ' + (window.epicMembershipUpgrade.nonce ? 'SET' : 'NOT SET'));
        debugInfo.push('Strings: ' + (window.epicMembershipUpgrade.strings ? Object.keys(window.epicMembershipUpgrade.strings).length + ' strings' : 'NOT SET'));
    } else {
        debugInfo.push('❌ epicMembershipUpgrade object NOT FOUND');
    }
    debugInfo.push('');

    debugInfo.push('=== epicMembership Object (Shared) ===');
    if (typeof window.epicMembership !== 'undefined') {
        debugInfo.push('✅ epicMembership object exists');
        debugInfo.push('AJAX URL: ' + (window.epicMembership.ajaxUrl || 'NOT SET'));
        debugInfo.push('Nonce: ' + (window.epicMembership.nonce ? 'SET' : 'NOT SET'));
        debugInfo.push('Strings: ' + (window.epicMembership.strings ? Object.keys(window.epicMembership.strings).length + ' strings' : 'NOT SET'));
    } else {
        debugInfo.push('❌ epicMembership object NOT FOUND');
    }
    debugInfo.push('');

    debugInfo.push('=== Modal Scripts ===');
    debugInfo.push('EpicMembershipUpgradeTierModal: ' + (typeof window.EpicMembershipUpgradeTierModal !== 'undefined' ? '✅ Loaded' : '❌ Not loaded'));
    debugInfo.push('EpicMembershipPayPal: ' + (typeof window.EpicMembershipPayPal !== 'undefined' ? '✅ Loaded' : '❌ Not loaded'));
    debugInfo.push('EpicMembership: ' + (typeof window.EpicMembership !== 'undefined' ? '✅ Loaded' : '❌ Not loaded'));
    debugInfo.push('');

    debugInfo.push('=== Upgrade Buttons ===');
    var buttons = $('.epic-membership-upgrade-button');
    debugInfo.push('Found ' + buttons.length + ' upgrade buttons');
    buttons.each(function(index) {
        var $btn = $(this);
        debugInfo.push('Button ' + (index + 1) + ':');
        debugInfo.push('  - Tier ID: ' + ($btn.data('tier-id') || 'NOT SET'));
        debugInfo.push('  - Tier Name: ' + ($btn.data('tier-name') || 'NOT SET'));
        debugInfo.push('  - Element Type: ' + $btn.prop('tagName'));
        debugInfo.push('  - Has epic-membership-status-page parent: ' + ($btn.closest('.epic-membership-status-page').length > 0 ? 'Yes' : 'No'));
    });

    $('#debug-output').html('<pre>' + debugInfo.join('\n') + '</pre>').show();
}

// Test event handler function
function testEventHandler() {
    console.log('=== Testing Event Handler ===');

    // Create a test button
    var testButton = $('<button type="button" class="epic-membership-upgrade-button" data-tier-id="999" data-tier-name="Test Tier" style="background: red; color: white; padding: 10px;">TEST BUTTON</button>');
    $('body').append(testButton);

    console.log('Test button created and added to page');

    // Try to trigger click
    setTimeout(function() {
        console.log('Triggering click on test button...');
        testButton.trigger('click');

        // Remove test button after 3 seconds
        setTimeout(function() {
            testButton.remove();
            console.log('Test button removed');
        }, 3000);
    }, 500);
}
</script>

<?php wp_footer(); ?>
</body>
</html>
