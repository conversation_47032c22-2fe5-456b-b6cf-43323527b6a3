# Epic Membership Plugin

A comprehensive WordPress membership plugin with tier-based access control, timezone-aware scheduling, ad-free premium experience, integrated float-over promotional system, and complete Ko-fi payment integration with automated membership upgrades.

## Features

### 🎯 Core Features
- **Custom Membership Tiers**: Create unlimited membership levels with hierarchical access
- **Ko-fi Payment Integration**: Complete Ko-fi payment processing with automated membership upgrades and smart tier matching
- **Enhanced Scheduled Releases**: Tier-based early access control for scheduled content with multiple tier selection
- **Granular Access Control**: Restrict content based on membership tiers with permanent tier restrictions
- **Timezone-Aware Scheduling**: Content releases with automatic timezone conversion and tier-based early access
- **User Management**: Admin interface for managing user memberships and durations
- **Multi-Provider Ad Integration**: Support for Google AdSense and Adsterra with automatic ad-free experience for premium members
- **Float-Over Promotional System**: Configurable modal overlays on posts with random link rotation and IP-based usage tracking
- **User Dashboard**: Member portal showing status, expiration, and upgrade options with Ko-fi payment integration

### 🔧 Technical Features
- **WordPress Integration**: Seamless integration with WordPress core
- **Security First**: Proper nonce verification, data sanitization, and capability checks
- **Performance Optimized**: Efficient database queries and caching
- **Mobile Responsive**: Works perfectly on all devices
- **Developer Friendly**: Extensive hooks and filters for customization

## Installation

### Automatic Installation
1. Download the plugin zip file
2. Go to WordPress Admin → Plugins → Add New
3. Click "Upload Plugin" and select the zip file
4. Click "Install Now" and then "Activate"

### Manual Installation
1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin

### Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Quick Start Guide

### 1. Initial Setup
After activation, the plugin will:
- Create necessary database tables
- Set up default membership tiers (Free, Premium, VIP)
- Schedule automatic membership expiration checks

### 2. Configure Membership Tiers
1. Go to **Membership → Tiers**
2. Edit existing tiers or create new ones
3. Set access levels (0 = Free, 10 = Premium, 20 = VIP)
4. Configure pricing and duration
5. Select capabilities for each tier

### 3. Protect Content
1. Edit any post or page
2. Find the "Membership Access Control" meta box
3. Choose access type:
   - **Public**: Everyone can access
   - **Members Only**: Requires specific tier
   - **Scheduled**: Released at specific date/time with optional tier-based early access
4. For scheduled content:
   - Set release date and time
   - Select which membership tiers get early access (optional)
   - Preview shows which tiers have early access
5. Set teaser content for restricted posts

### 4. Configure Ko-fi Payment Integration
1. Go to **Membership → Settings → Ko-fi**
2. Enable Ko-fi payment gateway
3. Enter your Ko-fi page URL (e.g., `https://ko-fi.com/yourusername`)
4. Set up webhook in your Ko-fi account:
   - Add webhook URL: `https://yoursite.com/wp-admin/admin-ajax.php?action=epic_membership_kofi_webhook`
   - Copy verification token from Ko-fi
5. Paste verification token in WordPress settings
6. Enable automatic upgrades
7. Test integration using provided testing tools

### 5. Manage Users
1. Go to **Membership → Users**
2. Search and filter users
3. Assign or upgrade memberships
4. Set custom durations
5. Use bulk actions for multiple users

## Usage Examples

### Shortcodes

#### Membership Dashboard
```php
[epic_membership_dashboard]
```

#### Conditional Content
```php
[epic_membership tier="premium"]
This content is only visible to premium members.
[/epic_membership]

[epic_membership level="10"]
This content requires level 10 or higher.
[/epic_membership]

[epic_membership capability="early_access"]
This content is only for members with early access capability.
[/epic_membership]

[epic_membership capability="ad_free_experience"]
This content is only for members with ad-free experience.
[/epic_membership]

[epic_membership logged_in="true"]
This content is only for logged-in users.
[/epic_membership]
```

#### Ko-fi Instructions
```php
[kofi_instructions]
```

#### Ko-fi Quick Guide
```php
[kofi_quick_guide style="compact"]
```

### Template Functions

#### Check if user should see ads
```php
if (Epic_Membership_Ad_Integration::show_ads()) {
    // Show advertisement
    echo '<div class="ad">Advertisement here</div>';
}
```

#### Conditional ad display
```php
Epic_Membership_Ad_Integration::conditional_ad(
    '<div class="ad">Your ad code here</div>',
    '<div class="ad-free-message">Enjoying ad-free experience!</div>'
);
```

#### Check user membership
```php
$database = new Epic_Membership_Database();
$membership = $database->get_user_membership(get_current_user_id());

if ($membership && $membership->tier_level >= 10) {
    // User has premium access
}
```

### Hooks and Filters

#### Content Protection
```php
// Modify protected content message
add_filter('epic_membership_protected_content_message', function($message, $post_id, $access_result) {
    // Customize the message
    return $message;
}, 10, 3);

// Add custom ad patterns for filtering
add_filter('epic_membership_ad_patterns', function($patterns) {
    $patterns[] = '/\[custom_ad[^\]]*\]/i';
    return $patterns;
});
```

#### Membership Events
```php
// Hook into membership updates
add_action('epic_membership_user_upgraded', function($user_id, $old_tier, $new_tier) {
    // Send notification email
    // Log the upgrade
    // Trigger other actions
}, 10, 3);
```

## Configuration

### Settings
Access plugin settings at **Membership → Settings**:

- **Timezone Settings**: Configure server timezone
- **Advertisement Settings**: Configure Google AdSense and Adsterra integration
- **Float Over Settings**: Configure promotional overlay system
- **Content Settings**: Default teaser length and behavior
- **Logging**: Enable access logging for analytics
- **Cron Settings**: Configure automatic expiration checks

### Advertisement Integration
The plugin supports multiple advertisement providers with automatic premium user protection:

#### Supported Providers
- **Google AdSense**: Auto ads with automatic placement
- **Adsterra**: Popunders, social bars, banners, and native ads

#### Features
1. **Multi-Provider Support**: Enable both AdSense and Adsterra simultaneously
2. **Automatic Head Insertion**: Ad codes inserted into HTML head section
3. **Premium Protection**: Ads automatically hidden for premium members
4. **Content Filtering**: Removes ad content from posts and widgets for premium users
5. **Template Functions**: Check ad display status in themes

#### Setup Guides
- **Google AdSense Setup**: Configure AdSense integration through plugin settings
- **Adsterra Setup**: Configure Adsterra integration through plugin settings

### Float Over Promotional System
The plugin includes an integrated float-over system for displaying promotional content to visitors:

#### Features
1. **Modal Overlay**: Full-screen overlay that appears on single posts
2. **Random Link Rotation**: Display random promotional links from your configured lists
3. **Flexible View Limits**: Configurable limits per IP address (1x, 5x, 10x, unlimited, etc.)
4. **Per-Link Custom Limits**: Set different view limits for individual links
5. **Unlimited Links**: Special links that can be shown without any restrictions
6. **IP-Based Usage Tracking**: Advanced tracking with flexible limit enforcement
7. **Configurable Delay**: Set custom delay before showing continue button
8. **Premium User Exclusion**: Optionally hide from premium members
9. **Click Statistics**: Track total clicks and unique visitors

#### Configuration
Access Float Over settings at **Membership → Float Over**:

- **Enable/Disable**: Toggle the float-over system
- **Default View Limit**: Global limit for how many times each link can be shown per IP
- **Limited Links**: Comma-separated list of promotional URLs with configurable limits
- **Per-Link Custom Limits**: Set specific limits for individual links (format: URL=LIMIT)
- **Unlimited Links**: Links that can be shown repeatedly without restrictions
- **Delay Time**: Seconds to wait before showing continue button (1-60)
- **Custom Text**: Message displayed in the overlay
- **Free Users Only**: Option to show only to non-premium users

#### How It Works
1. Visitor opens a single post/article
2. Float-over appears immediately as modal overlay
3. After configured delay, continue button becomes available
4. System selects a random link from available options (limited + unlimited)
5. Clicking continue opens the selected promotional link in new tab
6. System tracks usage based on configured limits:
   - **Limited links**: Tracked until they reach their view limit per IP
   - **Unlimited links**: Always available, no tracking restrictions
   - **Custom limits**: Individual links can have specific view limits
7. When all limited links reach their limits, system resets limited links only
8. Unlimited links remain available throughout the entire process

#### Link Types and Configuration Examples

**1. Default View Limit**
Set a global default for all limited links (e.g., 5 views per IP address).

**2. Limited Links with Default Limit**
```
https://promo1.com, https://promo2.com, https://promo3.com
```
Each link will use the default view limit.

**3. Per-Link Custom Limits**
```
https://special-offer.com=10
https://flash-sale.com=3
https://premium-content.com=15
```
Override the default limit for specific links.

**4. Unlimited Links**
```
https://evergreen-content.com, https://main-landing-page.com
```
These links can be shown repeatedly without any view count restrictions.

**5. Mixed Configuration Example**
- **Default Limit**: 5 views per IP
- **Limited Links**: `https://promo1.com, https://promo2.com` (5 views each)
- **Custom Limits**: `https://special.com=10` (10 views)
- **Unlimited**: `https://main-offer.com` (unlimited views)

This gives you maximum flexibility in controlling link exposure based on your promotional strategy.

## Customization

### Custom Capabilities
Add custom capabilities to membership tiers:

```php
add_filter('epic_membership_available_capabilities', function($capabilities) {
    $capabilities['custom_feature'] = 'Custom Feature Access';
    return $capabilities;
});
```

### Custom Access Types
Extend content access types:

```php
add_filter('epic_membership_access_types', function($types) {
    $types['custom_type'] = 'Custom Access Type';
    return $types;
});
```

### Styling
Override default styles by adding CSS to your theme:

```css
/* Customize protected content appearance */
.epic-membership-protected-content {
    background: #f8f9fa;
    border: 2px solid #007cba;
    border-radius: 8px;
}

/* Style membership badges */
.epic-membership-tier-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
```

## Troubleshooting

### Common Issues

#### Membership Not Expiring
- Check if WordPress cron is working: `wp cron event list`
- Verify cron job is scheduled: Look for `epic_membership_check_expired_memberships`
- Test manual expiration: Go to Membership → Users and check expiring memberships

#### Content Still Showing Ads
- Verify user has ad-free capability in their tier
- Check if ad patterns are correctly configured
- Test with different ad types and placements

#### Timezone Issues
- Ensure WordPress timezone is set correctly
- Check user's browser timezone detection
- Verify scheduled content release times

#### Database Errors
- Check database table creation during activation
- Verify user permissions for database operations
- Look for conflicts with other plugins

#### Modal Display Issues
- Test modal functionality: Open browser console and run `EpicMembershipTester.testModal()`
- Check theme compatibility: Run `EpicMembershipTester.analyzeTheme()`
- View debug information: Run `EpicMembershipTester.getDebugInfo()`
- Check plugin settings for theme compatibility options

### Debug Mode
Enable WordPress debug mode to see detailed error messages:

```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Theme Compatibility
The plugin includes extensive theme compatibility features:

- **Universal Modal System**: Multiple fallback strategies for modal display
- **Automatic Theme Detection**: Handles popular themes like Elementor, Divi, Astra
- **CSS Protection**: Maximum specificity rules to override theme conflicts
- **Testing Tools**: Built-in tools for debugging modal issues

For theme compatibility issues, check the plugin settings and troubleshooting section above.

### Support
For technical support:
1. Check the troubleshooting section above
2. Review the testing guide for common scenarios
3. Enable debug logging to identify specific issues
4. Check for plugin conflicts by deactivating other plugins

## Development

### File Structure
```
membership-plugin/
├── membership-plugin.php          # Main plugin file
├── includes/
│   ├── class-database.php         # Database operations
│   ├── class-timezone-handler.php # Timezone management
│   ├── class-ad-integration.php   # Ad blocking features
│   ├── admin/                     # Admin interface
│   └── frontend/                  # Frontend functionality
├── assets/
│   ├── css/                       # Stylesheets
│   └── js/                        # JavaScript files
├── languages/                     # Translation files
└── README.md                      # This file
```

### Contributing
1. Follow WordPress coding standards
2. Add proper documentation for new features
3. Include unit tests for new functionality
4. Test across different WordPress versions
5. Ensure backward compatibility

## License
This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.3.0 (Latest)
- **🔧 Fixed AdSense Integration**: Resolved critical AdSense implementation issues that prevented ads from displaying
- **✨ Added Debug Tools**: New diagnostic and testing tools for AdSense troubleshooting (`debug-adsense.php`, `test-adsense.php`)
- **🚀 Improved Ad Blocking**: More intelligent selective ad blocking system that preserves AdSense for non-premium users
- **📚 Enhanced Documentation**: Comprehensive troubleshooting guides and step-by-step instructions
- **🔒 Security Maintained**: All existing security measures preserved with no regressions
- **🐛 Fixed PHP Syntax Error**: Resolved parse error in ad integration class (line 383)

### Version 1.2.0
- **🚀 Early Access Functionality**: Fixed early access capability for scheduled content - paid members with early access can now access content before release time
- **📊 Hierarchical Content Access**: Premium and VIP members can now access content restricted to lower tiers (e.g., Premium members can access Free-tier content)
- **🔧 Enhanced Capability System**: Added general capability checking methods for better tier feature management
- **📝 Improved Shortcodes**: Added `capability` parameter to `[epic_membership]` shortcode for capability-based content restrictions
- **🧪 Testing Framework**: Added comprehensive test files for verifying early access and tier functionality
- **🗂️ Code Cleanup**: Removed unnecessary documentation files to keep codebase clean
- **⚡ Database Migration**: Automatic update of default tiers to include early access capability for Premium and VIP tiers
- **🔄 Backward Compatibility**: All existing functionality preserved while adding new hierarchical access features

### Version 1.1.0
- **🎯 Enhanced Scheduled Release with Tier Access Control**: Added granular membership tier access control to scheduled releases
- **🔧 Database Schema Update**: Added `allowed_tier_ids` field to support multiple tier access for scheduled content
- **🎨 Enhanced Admin Interface**: New tier selection interface in scheduled release settings with real-time preview
- **🚀 Improved Access Logic**: Restructured access control logic for better tier-based restrictions
- **🔒 Fixed Tier Restriction Issues**: Resolved issues where Free tier users could access Premium content
- **📱 Consistent User Experience**: Unified behavior between members_only and scheduled content types
- **🎛️ Better Content Management**: Enhanced admin views showing tier restrictions for scheduled content
- **🔄 Automatic Database Migration**: Seamless upgrade from v1.0.x with backward compatibility
- **🧪 Comprehensive Testing**: Enhanced validation and debugging capabilities
- **📚 Enhanced Documentation**: Improved documentation for new tier-based scheduling features

### Version 1.0.6
- **Fixed tier styling issue**: Tier badges now maintain background colors and icons when tier names are changed
- **Enhanced tier badge system**: Styling now based on tier level and price instead of slug
- **Improved backward compatibility**: Legacy tier names (Premium, VIP) continue to work
- **Added flexible tier styling**: Any paid tier automatically gets appropriate styling based on level
- **Updated database queries**: Now include tier_price for proper styling determination
- **Enhanced testing capabilities**: Improved validation for styling changes

### Version 1.0.5
- Removed upgrade buttons from Available Membership Tiers section for better UX
- Added guidance text directing users to Upgrade Options section for membership upgrades
- Improved user flow by separating tier information display from upgrade actions

### Version 1.0.4
- Added free tier display in Available Membership Tiers section on membership status page
- Improved tier display logic to show all available tiers including free tier
- Enhanced user experience by providing complete tier visibility

### Version 1.0.3
- Modified "Upgrade Now" button functionality to redirect to membership status page
- Enhanced user experience by providing direct access to membership dashboard
- Improved upgrade flow consistency across all protected content areas

### Version 1.0.0
- Initial release
- Core membership functionality
- Timezone-aware scheduling
- Ad-free experience
- User dashboard
- Admin interface
- Content protection system

## Ko-fi Integration Guide

### Overview
The Ko-fi integration allows users to upgrade their membership by making donations through Ko-fi. The system automatically processes payments and assigns appropriate membership tiers based on the donation amount.

### How It Works
1. **User clicks Ko-fi button** → Enhanced modal with instructions opens
2. **User redirected to Ko-fi** → Ko-fi page opens with suggested amount
3. **User adjusts amount** → Changes from Ko-fi's default $2 to tier amount
4. **User completes payment** → Uses same email as WordPress account
5. **Webhook processes payment** → Automatic membership activation
6. **User receives confirmation** → Email notification and immediate access

### Ko-fi Amount Issue Solution
**Problem**: Ko-fi shows $2 by default regardless of URL parameters
**Solution**: Clear user instructions and automatic processing
- Modal provides step-by-step guidance
- Users manually adjust amount to match tier price
- Webhook processes actual payment amount
- Smart tier matching based on payment received

### Tier Matching Logic
- **Exact Match**: Payment matches tier price → User gets that tier
- **Overpayment**: Payment exceeds tier price → User gets highest affordable tier
- **Underpayment**: Payment below tier price → User gets highest affordable tier
- **Fraud Protection**: Users cannot get tiers they haven't paid for

### Testing Tools
- `test-complete-kofi-integration.php` - Complete integration test
- `configure-kofi-settings.php` - Easy configuration interface
- `demo-tier-matching.php` - Interactive tier matching demonstration
- `test-webhook.php` - Webhook accessibility testing

### User Instructions
Use shortcodes to add Ko-fi instructions to your pages:
- `[kofi_instructions]` - Complete payment guide with FAQ
- `[kofi_quick_guide]` - Compact payment instructions

### Support
For Ko-fi integration support, use the provided testing tools and documentation. The system includes comprehensive error handling and debugging capabilities.
