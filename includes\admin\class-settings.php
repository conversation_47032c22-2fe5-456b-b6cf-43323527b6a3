<?php

/**

 * Settings Management Class for Epic Membership Plugin

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Settings {

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->init_hooks();

        // Handle currency fix if requested
        add_action('admin_init', array($this, 'handle_currency_fix'));

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Register settings

        add_action('admin_init', array($this, 'register_settings'));

        

        // Handle settings export/import

        add_action('admin_post_epic_membership_export_settings', array($this, 'export_settings'));

        add_action('admin_post_epic_membership_import_settings', array($this, 'import_settings'));

        

        // AJAX handlers

        add_action('wp_ajax_epic_membership_reset_settings', array($this, 'ajax_reset_settings'));

        add_action('wp_ajax_epic_membership_check_expired_now', array($this, 'ajax_check_expired_now'));



        // Handle settings form submission

        add_action('admin_post_epic_membership_save_settings', array($this, 'handle_settings_save'));

    }

    

    /**

     * Register plugin settings

     */

    public function register_settings() {

        // General settings

        register_setting('epic_membership_general', 'epic_membership_timezone');

        register_setting('epic_membership_general', 'epic_membership_enable_logging');

        register_setting('epic_membership_general', 'epic_membership_auto_expire_check');

        

        // Content settings

        register_setting('epic_membership_content', 'epic_membership_content_teaser_length');

        register_setting('epic_membership_content', 'epic_membership_default_access_type');

        

        // Ad settings

        register_setting('epic_membership_ads', 'epic_membership_adsense_enabled');

        register_setting('epic_membership_ads', 'epic_membership_adsense_head_code');

        register_setting('epic_membership_ads', 'epic_membership_adsterra_enabled');

        register_setting('epic_membership_ads', 'epic_membership_adsterra_head_code');



        // Keep legacy setting for backward compatibility

        register_setting('epic_membership_ads', 'epic_membership_ad_integration');



        // PayPal settings

        register_setting('epic_membership_paypal', 'epic_membership_paypal_enabled');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_sandbox_mode');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_client_id');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_client_secret');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_sandbox_client_id');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_sandbox_client_secret');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_webhook_id');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_currency');

        register_setting('epic_membership_paypal', 'epic_membership_paypal_debug_mode');



        // Ko-fi settings

        register_setting('epic_membership_kofi', 'epic_membership_kofi_enabled');

        register_setting('epic_membership_kofi', 'epic_membership_kofi_page_url');

        register_setting('epic_membership_kofi', 'epic_membership_kofi_verification_token');

        register_setting('epic_membership_kofi', 'epic_membership_kofi_currency');

        register_setting('epic_membership_kofi', 'epic_membership_kofi_auto_upgrade');

        register_setting('epic_membership_kofi', 'epic_membership_kofi_debug_mode');



        // Trakteer settings

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_enabled');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_page_url');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_verification_token');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_api_key');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_currency');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_auto_upgrade');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_debug_mode');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_sandbox_mode');

        register_setting('epic_membership_trakteer', 'epic_membership_trakteer_webhook_secret');



        // Advanced settings

        register_setting('epic_membership_advanced', 'epic_membership_cache_duration');

        register_setting('epic_membership_advanced', 'epic_membership_debug_mode');



        // Float Over settings

        register_setting('epic_membership_float_over', 'epic_membership_float_over_enabled');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_links');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_unlimited_links');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_default_limit');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_per_link_limits');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_delay');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_custom_text');

        register_setting('epic_membership_float_over', 'epic_membership_float_over_premium_only');

    }

    

    /**

     * Get default settings

     */

    public function get_default_settings() {

        return array(

            'epic_membership_timezone' => wp_timezone_string(),

            'epic_membership_enable_logging' => true,

            'epic_membership_auto_expire_check' => true,

            'epic_membership_content_teaser_length' => 150,

            'epic_membership_default_access_type' => 'public',

            'epic_membership_adsense_enabled' => false,

            'epic_membership_adsense_head_code' => '',

            'epic_membership_adsterra_enabled' => false,

            'epic_membership_adsterra_head_code' => '',

            'epic_membership_ad_integration' => 'adsense', // Legacy setting

            'epic_membership_paypal_enabled' => false,

            'epic_membership_paypal_sandbox_mode' => true,

            'epic_membership_paypal_client_id' => '',

            'epic_membership_paypal_client_secret' => '',

            'epic_membership_paypal_sandbox_client_id' => '',

            'epic_membership_paypal_sandbox_client_secret' => '',

            'epic_membership_paypal_webhook_id' => '',

            'epic_membership_paypal_currency' => 'USD',

            'epic_membership_paypal_debug_mode' => false,

            'epic_membership_kofi_enabled' => false,

            'epic_membership_kofi_page_url' => '',

            'epic_membership_kofi_verification_token' => '',

            'epic_membership_kofi_currency' => 'USD',

            'epic_membership_kofi_auto_upgrade' => true,

            'epic_membership_kofi_debug_mode' => false,

            'epic_membership_trakteer_enabled' => false,

            'epic_membership_trakteer_page_url' => '',

            'epic_membership_trakteer_verification_token' => '',

            'epic_membership_trakteer_api_key' => '',

            'epic_membership_trakteer_currency' => 'IDR',

            'epic_membership_trakteer_auto_upgrade' => true,

            'epic_membership_trakteer_debug_mode' => false,

            'epic_membership_trakteer_sandbox_mode' => true,

            'epic_membership_trakteer_webhook_secret' => '',

            'epic_membership_cache_duration' => 3600,

            'epic_membership_float_over_enabled' => false,

            'epic_membership_float_over_links' => '',

            'epic_membership_float_over_unlimited_links' => '',

            'epic_membership_float_over_default_limit' => 5,

            'epic_membership_float_over_per_link_limits' => '',

            'epic_membership_float_over_delay' => 5,

            'epic_membership_float_over_custom_text' => 'Click the button below to continue',

            'epic_membership_float_over_premium_only' => false,

            'epic_membership_debug_mode' => false

        );

    }

    

    /**

     * Get all plugin settings

     */

    public function get_all_settings() {

        $defaults = $this->get_default_settings();

        $settings = array();

        

        foreach ($defaults as $key => $default_value) {

            $settings[$key] = get_option($key, $default_value);

        }

        

        return $settings;

    }

    

    /**

     * Update multiple settings

     */

    public function update_settings($settings) {

        $updated = 0;

        

        foreach ($settings as $key => $value) {

            if (update_option($key, $value)) {

                $updated++;

            }

        }

        

        return $updated;

    }

    

    /**

     * Reset settings to defaults

     */

    public function reset_to_defaults() {

        $defaults = $this->get_default_settings();

        return $this->update_settings($defaults);

    }

    

    /**

     * Export settings

     */

    public function export_settings() {

        // Verify nonce

        if (!wp_verify_nonce($_GET['nonce'], 'epic_membership_export')) {

            wp_die(__('Security check failed.', 'epic-membership'));

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }

        

        $settings = $this->get_all_settings();

        

        // Add metadata

        $export_data = array(

            'epic_membership_export' => true,

            'version' => EPIC_MEMBERSHIP_VERSION,

            'export_date' => current_time('mysql'),

            'site_url' => get_site_url(),

            'settings' => $settings

        );

        

        $filename = 'epic-membership-settings-' . date('Y-m-d-H-i-s') . '.json';

        

        header('Content-Type: application/json');

        header('Content-Disposition: attachment; filename="' . $filename . '"');

        header('Cache-Control: no-cache, must-revalidate');

        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        

        echo json_encode($export_data, JSON_PRETTY_PRINT);

        exit;

    }

    

    /**

     * Import settings

     */

    public function import_settings() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_import')) {

            wp_die(__('Security check failed.', 'epic-membership'));

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }

        

        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-settings',

                'epic_membership_message' => 'import_error'

            ), admin_url('admin.php')));

            exit;

        }

        

        $file_content = file_get_contents($_FILES['import_file']['tmp_name']);

        $import_data = json_decode($file_content, true);

        

        if (!$import_data || !isset($import_data['epic_membership_export'])) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-settings',

                'epic_membership_message' => 'invalid_file'

            ), admin_url('admin.php')));

            exit;

        }

        

        $updated = $this->update_settings($import_data['settings']);

        

        wp_redirect(add_query_arg(array(

            'page' => 'epic-membership-settings',

            'epic_membership_message' => 'import_success',

            'updated_count' => $updated

        ), admin_url('admin.php')));

        exit;

    }

    

    /**

     * Get system information

     */

    public function get_system_info() {

        global $wpdb;

        

        return array(

            'plugin_version' => EPIC_MEMBERSHIP_VERSION,

            'wordpress_version' => get_bloginfo('version'),

            'php_version' => PHP_VERSION,

            'mysql_version' => $wpdb->db_version(),

            'server_timezone' => wp_timezone_string(),

            'memory_limit' => ini_get('memory_limit'),

            'max_execution_time' => ini_get('max_execution_time'),

            'upload_max_filesize' => ini_get('upload_max_filesize'),

            'cron_status' => wp_next_scheduled('epic_membership_check_expired_memberships') ? 'active' : 'inactive',

            'debug_mode' => defined('WP_DEBUG') && WP_DEBUG ? 'enabled' : 'disabled'

        );

    }

    

    /**

     * AJAX: Reset settings to defaults

     */

    public function ajax_reset_settings() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $updated = $this->reset_to_defaults();

        

        if ($updated > 0) {

            wp_send_json_success('Settings reset to defaults');

        } else {

            wp_send_json_error('Failed to reset settings');

        }

    }

    

    /**

     * AJAX: Check expired memberships now

     */

    public function ajax_check_expired_now() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        // Trigger the cron job manually

        do_action('epic_membership_check_expired_memberships');

        

        wp_send_json_success('Expired memberships checked');

    }

    

    /**

     * Validate setting value

     */

    public function validate_setting($key, $value) {

        switch ($key) {

            case 'epic_membership_timezone':

                return in_array($value, timezone_identifiers_list()) ? $value : wp_timezone_string();



            case 'epic_membership_content_teaser_length':

                return max(50, min(500, intval($value)));



            case 'epic_membership_cache_duration':

                return max(300, intval($value)); // Minimum 5 minutes



            case 'epic_membership_enable_logging':

            case 'epic_membership_auto_expire_check':

            case 'epic_membership_debug_mode':

            case 'epic_membership_adsense_enabled':

            case 'epic_membership_adsterra_enabled':

                return (bool) $value;



            case 'epic_membership_ad_integration':

                return in_array($value, array('adsense', 'disabled')) ? $value : 'adsense';



            case 'epic_membership_adsense_head_code':

            case 'epic_membership_adsterra_head_code':

                return $this->sanitize_html_code($value);



            case 'epic_membership_default_access_type':

                return in_array($value, array('public', 'members_only', 'scheduled')) ? $value : 'public';

                

            default:

                return sanitize_text_field($value);

        }

    }

    

    /**

     * Get setting with validation

     */

    public function get_setting($key, $default = null) {

        $defaults = $this->get_default_settings();

        $default = $default !== null ? $default : ($defaults[$key] ?? '');

        

        $value = get_option($key, $default);

        return $this->validate_setting($key, $value);

    }

    

    /**

     * Update setting with validation

     */

    public function update_setting($key, $value) {

        $validated_value = $this->validate_setting($key, $value);

        return update_option($key, $validated_value);

    }



    /**

     * Handle settings form submission

     */

    public function handle_settings_save() {

        // Verify nonce

        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_settings')) {

            wp_die(__('Security check failed', 'epic-membership'));

        }



        // Check user permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions to access this page.', 'epic-membership'));

        }



        // Define all settings that can be saved

        $settings_map = array(

            // General settings

            'epic_membership_timezone' => 'sanitize_text_field',

            'epic_membership_enable_logging' => 'checkbox',

            'epic_membership_auto_expire_check' => 'checkbox',



            // Content settings

            'epic_membership_content_teaser_length' => 'intval',



            // Ad settings

            'epic_membership_ad_integration' => 'sanitize_text_field',

            'epic_membership_adsense_enabled' => 'checkbox',

            'epic_membership_adsense_head_code' => 'html_code',

            'epic_membership_adsterra_enabled' => 'checkbox',

            'epic_membership_adsterra_head_code' => 'html_code',



            // PayPal settings

            'epic_membership_paypal_enabled' => 'checkbox',

            'epic_membership_paypal_sandbox_mode' => 'checkbox',

            'epic_membership_paypal_client_id' => 'sanitize_text_field',

            'epic_membership_paypal_client_secret' => 'sanitize_text_field',

            'epic_membership_paypal_sandbox_client_id' => 'sanitize_text_field',

            'epic_membership_paypal_sandbox_client_secret' => 'sanitize_text_field',

            'epic_membership_paypal_webhook_id' => 'sanitize_text_field',

            'epic_membership_paypal_currency' => 'sanitize_text_field',

            'epic_membership_paypal_debug_mode' => 'checkbox',



            // Ko-fi settings

            'epic_membership_kofi_enabled' => 'checkbox',

            'epic_membership_kofi_page_url' => 'esc_url_raw',

            'epic_membership_kofi_verification_token' => 'sanitize_text_field',

            'epic_membership_kofi_currency' => 'sanitize_text_field',

            'epic_membership_kofi_auto_upgrade' => 'checkbox',

            'epic_membership_kofi_debug_mode' => 'checkbox',



            // Trakteer settings

            'epic_membership_trakteer_enabled' => 'checkbox',

            'epic_membership_trakteer_page_url' => 'esc_url_raw',

            'epic_membership_trakteer_verification_token' => 'sanitize_text_field',

            'epic_membership_trakteer_api_key' => 'sanitize_text_field',

            'epic_membership_trakteer_currency' => 'sanitize_text_field',

            'epic_membership_trakteer_auto_upgrade' => 'checkbox',

            'epic_membership_trakteer_debug_mode' => 'checkbox',

            'epic_membership_trakteer_sandbox_mode' => 'checkbox',

            'epic_membership_trakteer_webhook_secret' => 'sanitize_text_field',

        );



        $updated_count = 0;

        $errors = array();



        foreach ($settings_map as $setting_key => $sanitize_function) {

            try {

                if ($sanitize_function === 'checkbox') {

                    $value = isset($_POST[$setting_key]) ? 1 : 0;

                } else {

                    $value = isset($_POST[$setting_key]) ? $_POST[$setting_key] : '';



                    // Apply sanitization

                    switch ($sanitize_function) {

                        case 'sanitize_text_field':

                            $value = sanitize_text_field($value);

                            break;

                        case 'sanitize_textarea_field':

                            $value = sanitize_textarea_field($value);

                            break;

                        case 'html_code':

                            // Remove WordPress automatic slashes before sanitizing HTML code

                            $value = wp_unslash($value);

                            $value = $this->sanitize_html_code($value);

                            break;

                        case 'intval':

                            $value = intval($value);

                            break;

                        default:

                            $value = sanitize_text_field($value);

                    }

                }



                // Update the option

                $result = update_option($setting_key, $value);



                if ($result !== false) {

                    $updated_count++;

                }



                // Log for debugging

                error_log("Epic Membership Settings: Updated $setting_key = " . var_export($value, true) . " (Result: " . ($result ? 'SUCCESS' : 'NO_CHANGE') . ")");



            } catch (Exception $e) {

                $errors[] = "Failed to update $setting_key: " . $e->getMessage();

                error_log("Epic Membership Settings Error: " . $e->getMessage());

            }

        }



        // Prepare redirect URL with status

        $redirect_url = admin_url('admin.php?page=epic-membership-settings');



        if (empty($errors)) {

            $redirect_url = add_query_arg('settings-updated', 'true', $redirect_url);

        } else {

            $redirect_url = add_query_arg('settings-error', 'true', $redirect_url);

            // Store errors in transient for display

            set_transient('epic_membership_settings_errors', $errors, 30);

        }



        wp_redirect($redirect_url);

        exit;

    }



    /**

     * Sanitize HTML/JavaScript code safely

     *

     * This method allows trusted users to save HTML/JavaScript code (like AdSense)

     * while still providing some basic security measures.

     *

     * Security considerations:

     * - Users with 'unfiltered_html' capability can save most HTML/JS (administrators in single-site)

     * - Other users with 'manage_options' get filtered HTML through wp_kses with allowed ad-related tags

     * - This follows WordPress security best practices for handling user-submitted HTML content

     */

    private function sanitize_html_code($code) {

        // Empty code is always safe

        if (empty($code)) {

            return '';

        }



        // Check if user has unfiltered_html capability (administrators in single-site, super admins in multisite)

        if (current_user_can('unfiltered_html')) {

            // For users with unfiltered_html capability, allow most HTML but still do basic cleanup

            $code = wp_check_invalid_utf8($code);

            $code = trim($code);



            // Log for security audit purposes

            error_log("Epic Membership: User with unfiltered_html capability saved HTML code. User ID: " . get_current_user_id());



            return $code;

        }



        // For users without unfiltered_html capability, use wp_kses with allowed HTML tags

        // Define allowed HTML tags and attributes for ad codes

        $allowed_html = array(

            'script' => array(

                'type' => array(),

                'src' => array(),

                'async' => array(),

                'defer' => array(),

                'crossorigin' => array(),

                'integrity' => array(),

                'data-ad-client' => array(),

                'data-ad-slot' => array(),

                'data-ad-format' => array(),

                'data-full-width-responsive' => array(),

            ),

            'ins' => array(

                'class' => array(),

                'style' => array(),

                'data-ad-client' => array(),

                'data-ad-slot' => array(),

                'data-ad-format' => array(),

                'data-full-width-responsive' => array(),

            ),

            'div' => array(

                'id' => array(),

                'class' => array(),

                'style' => array(),

            ),

            'noscript' => array(),

        );



        // Use wp_kses to sanitize while preserving allowed HTML

        $code = wp_kses($code, $allowed_html);

        $code = trim($code);



        return $code;

    }

    /**
     * Handle currency fix request
     */
    public function handle_currency_fix() {
        if (isset($_GET['fix_currency']) && $_GET['fix_currency'] === '1' && current_user_can('manage_options')) {
            if (wp_verify_nonce($_GET['_wpnonce'], 'fix_currency_nonce')) {
                $this->fix_currency_column();
                wp_redirect(admin_url('admin.php?page=epic-membership-settings&currency_fixed=1'));
                exit;
            }
        }
    }

    /**
     * Fix currency column in tiers table
     */
    private function fix_currency_column() {
        global $wpdb;

        $database = new Epic_Membership_Database();
        $table_tiers = $database->get_table('tiers');

        // Check if currency column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_tiers LIKE 'currency'");

        if (empty($column_exists)) {
            // Add currency column
            $sql = "ALTER TABLE $table_tiers ADD COLUMN currency varchar(3) NOT NULL DEFAULT 'USD' AFTER price";
            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('Epic Membership: Failed to add currency column: ' . $wpdb->last_error);
            } else {
                error_log('Epic Membership: Successfully added currency column');
            }
        } else {
            // Modify existing column to ensure it's NOT NULL
            $sql = "ALTER TABLE $table_tiers MODIFY COLUMN currency varchar(3) NOT NULL DEFAULT 'USD'";
            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('Epic Membership: Failed to modify currency column: ' . $wpdb->last_error);
            } else {
                error_log('Epic Membership: Successfully modified currency column');
            }
        }

        // Update existing tiers with default currency
        $default_currency = get_option('epic_membership_default_currency', 'USD');
        $update_result = $wpdb->query($wpdb->prepare(
            "UPDATE $table_tiers SET currency = %s WHERE currency IS NULL OR currency = '' OR currency = '0'",
            $default_currency
        ));

        error_log("Epic Membership: Updated $update_result tiers with default currency: $default_currency");
    }

}

