# Panduan Setup Trakteer Payment Gateway

## Deskripsi
Integrasi Trakteer memungkinkan pengguna Indonesia untuk melakukan pembayaran membership menggunakan Indonesian Rupiah (IDR) melalui platform Trakteer.id.

## Fitur Utama
- ✅ Dukungan mata uang IDR (Indonesian Rupiah)
- ✅ Sistem Cendol Trakteer (1 cendol = Rp 5.000)
- ✅ Interface dalam Bahasa Indonesia
- ✅ Webhook otomatis untuk aktivasi membership
- ✅ Keamanan tinggi dengan verifikasi signature
- ✅ Mode sandbox untuk testing
- ✅ Multi-currency support untuk tier
- ✅ Manajemen pembayaran yang tidak cocok (unmatched payments)
- ✅ Logging dan debugging yang komprehensif

## Langkah Setup

### 1. Persiapan Akun Trakteer
1. Daftar akun creator di [trakteer.id](https://trakteer.id)
2. Verifikasi akun dan setup halaman creator And<PERSON>
3. Catat URL halaman Trakteer Anda (contoh: `https://trakteer.id/namaanda`)

### 2. Konfigurasi Webhook di Trakteer
1. Login ke dashboard Trakteer
2. Masuk ke menu **Settings** → **Webhook**
3. Tambahkan webhook URL berikut:
   ```
   https://yoursite.com/wp-admin/admin-ajax.php?action=epic_membership_trakteer_webhook
   ```
4. Salin **Verification Token** yang diberikan
5. Jika tersedia, salin juga **Webhook Secret** untuk keamanan tambahan

### 3. Konfigurasi Plugin
1. Masuk ke **WordPress Admin** → **Epic Membership** → **Settings**
2. Klik tab **Trakteer**
3. Isi konfigurasi berikut:

#### Pengaturan Dasar
- **Enable Trakteer**: ✅ Centang untuk mengaktifkan
- **Trakteer Page URL**: Masukkan URL halaman Trakteer Anda
- **Webhook Verification Token**: Paste token dari dashboard Trakteer
- **Webhook Secret**: Paste secret untuk keamanan (opsional tapi direkomendasikan)

#### Pengaturan Mata Uang
- **Currency**: Pilih **IDR (Indonesian Rupiah)**

#### Pengaturan Mode
- **Sandbox Mode**: ✅ Aktifkan untuk testing, nonaktifkan untuk production
- **Automatic Upgrades**: ✅ Aktifkan untuk upgrade otomatis
- **Debug Mode**: ✅ Aktifkan untuk troubleshooting

### 4. Testing
1. Pastikan **Sandbox Mode** aktif
2. Buat test payment dengan jumlah kecil
3. Periksa log di **WordPress Admin** → **Epic Membership** → **Trakteer Payments**
4. Verifikasi bahwa membership teraktivasi otomatis

### 5. Go Live
1. Pastikan semua test berhasil
2. Nonaktifkan **Sandbox Mode**
3. Nonaktifkan **Debug Mode** (opsional)
4. Plugin siap untuk production

## Sistem Cendol Trakteer

### Apa itu Cendol?
Cendol adalah satuan donasi di platform Trakteer.id:
- **1 Cendol = Rp 5.000**
- Supporter dapat memilih berapa cendol yang ingin mereka berikan
- Plugin otomatis menghitung jumlah cendol yang dibutuhkan untuk setiap tier

### Contoh Perhitungan:
- **Tier Bronze (Rp 25.000)** = 5 Cendol
- **Tier Silver (Rp 50.000)** = 10 Cendol
- **Tier Gold (Rp 100.000)** = 20 Cendol

### Interface Bahasa Indonesia:
- Modal pembayaran menggunakan bahasa Indonesia
- Instruksi donasi yang jelas dengan istilah "cendol"
- Tombol "Dukung via Trakteer" dengan emoji cendol 🥤

## Format Webhook Trakteer

Plugin ini mendukung format webhook Trakteer yang standar:

```json
{
   "created_at": "2023-07-14T14:50:10+07:00",
   "transaction_id": "test-xxxxxxxx-xxxx-xxxxxxx-xxxxxxxxx",
   "type": "tip",
   "supporter_name": "Nama Supporter",
   "supporter_avatar": "https://trakteer.id/images/avatar.png",
   "supporter_message": "Pesan dari supporter",
   "unit": "Kopi",
   "quantity": 1,
   "price": 5000,
   "net_amount": 4750
}
```

## Troubleshooting

### Masalah: Tombol Trakteer tidak muncul
**Solusi:**
1. Pastikan Trakteer sudah diaktifkan di settings
2. Periksa apakah user sudah login
3. Pastikan tier memiliki harga > 0

### Masalah: Pembayaran tidak otomatis teraktivasi
**Solusi:**
1. Periksa webhook URL sudah benar di Trakteer dashboard
2. Periksa verification token sudah benar
3. Lihat log error di **Epic Membership** → **Trakteer Payments**
4. Pastikan supporter menggunakan nama yang bisa dicocokkan dengan user WordPress

### Masalah: Currency tidak tersimpan di tier
**Solusi:**
1. Masuk ke **Epic Membership** → **Trakteer Test**
2. Klik "Force Database Migration" untuk memaksa update database
3. Periksa apakah kolom currency sudah ada di tabel tiers
4. Coba edit tier lagi dan simpan

### Masalah: User tidak ditemukan untuk pembayaran
**Solusi:**
1. Masuk ke **Epic Membership** → **Trakteer Payments**
2. Lihat daftar "Unmatched Payments"
3. Cocokkan pembayaran dengan user secara manual
4. Klik "Process" untuk mengaktifkan membership

### Masalah: Webhook signature tidak valid
**Solusi:**
1. Periksa webhook secret di settings
2. Pastikan webhook URL benar
3. Hubungi support Trakteer jika masalah berlanjut

## Keamanan

### Verifikasi Webhook
Plugin menggunakan beberapa lapisan keamanan:
1. **HMAC SHA256 Signature**: Verifikasi menggunakan webhook secret
2. **Verification Token**: Token unik dari Trakteer
3. **Input Sanitization**: Semua data webhook disanitasi
4. **Error Logging**: Log semua aktivitas untuk audit

### Best Practices
1. Selalu gunakan **Webhook Secret** untuk production
2. Aktifkan **Debug Mode** hanya saat troubleshooting
3. Monitor log secara berkala
4. Backup database sebelum update plugin

## Support

### Log Files
Debug log tersimpan di:
- WordPress error log (jika debug mode aktif)
- **Epic Membership** → **Trakteer Payments** (unmatched payments)

### Informasi Kontak
Jika mengalami masalah:
1. Periksa log error terlebih dahulu
2. Pastikan konfigurasi sudah benar
3. Test dengan sandbox mode
4. Hubungi developer plugin untuk bantuan lebih lanjut

## Changelog

### Version 1.0.0
- ✅ Initial release
- ✅ Basic Trakteer integration
- ✅ IDR currency support
- ✅ Webhook handling
- ✅ Security implementation
- ✅ Unmatched payments management
- ✅ Admin interface

---

**Catatan**: Plugin ini dikembangkan khusus untuk pasar Indonesia dengan dukungan penuh mata uang IDR dan integrasi Trakteer.id.
