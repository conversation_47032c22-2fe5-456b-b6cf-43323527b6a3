<?php
/**
 * Ko-fi Payment Gateway for Epic Membership Plugin
 * Handles Ko-fi webhook integration for automatic membership upgrades
 */

if (!defined('ABSPATH')) {
    exit;
}

class Epic_Membership_Kofi_Gateway {
    
    /**
     * Gateway ID
     */
    const GATEWAY_ID = 'kofi';
    
    /**
     * Ko-fi webhook verification token
     */
    private $verification_token;
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Gateway settings
     */
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Epic_Membership_Database();
        $this->load_settings();
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // AJAX handlers for webhook processing
        add_action('wp_ajax_nopriv_epic_membership_kofi_webhook', array($this, 'handle_webhook'));
        add_action('wp_ajax_epic_membership_kofi_webhook', array($this, 'handle_webhook'));
        
        // AJAX handlers for Ko-fi payment processing
        add_action('wp_ajax_epic_membership_create_kofi_payment', array($this, 'ajax_create_kofi_payment'));
        add_action('wp_ajax_nopriv_epic_membership_create_kofi_payment', array($this, 'ajax_create_kofi_payment'));
        
        // Enqueue Ko-fi integration scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_kofi_scripts'));
    }
    
    /**
     * Load gateway settings
     */
    private function load_settings() {
        $this->settings = array(
            'enabled' => get_option('epic_membership_kofi_enabled', false),
            'webhook_url' => get_option('epic_membership_kofi_webhook_url', ''),
            'verification_token' => get_option('epic_membership_kofi_verification_token', ''),
            'currency' => get_option('epic_membership_kofi_currency', 'USD'),
            'debug_mode' => get_option('epic_membership_kofi_debug_mode', false),
            'page_url' => get_option('epic_membership_kofi_page_url', ''),
            'auto_upgrade' => get_option('epic_membership_kofi_auto_upgrade', true)
        );
        
        $this->verification_token = $this->settings['verification_token'];
    }
    
    /**
     * Check if gateway is enabled
     */
    public function is_enabled() {
        return $this->settings['enabled'] && !empty($this->settings['verification_token']);
    }
    
    /**
     * Get gateway settings
     */
    public function get_settings() {
        return $this->settings;
    }
    
    /**
     * Enqueue Ko-fi integration scripts
     */
    public function enqueue_kofi_scripts() {
        if (!$this->is_enabled()) {
            return;
        }
        
        wp_enqueue_script(
            'epic-membership-kofi',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/kofi-integration.js',
            array('jquery', 'epic-membership-frontend'),
            '1.0.0',
            true
        );
        
        wp_localize_script('epic-membership-kofi', 'epicMembershipKofi', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('epic_membership_nonce'),
            'pageUrl' => $this->settings['page_url'],
            'currency' => $this->settings['currency'],
            'strings' => array(
                'processing' => __('Processing payment...', 'epic-membership'),
                'error' => __('Payment processing failed. Please try again.', 'epic-membership'),
                'success' => __('Payment successful! Your membership has been activated.', 'epic-membership'),
                'redirect' => __('Redirecting to Ko-fi...', 'epic-membership')
            )
        ));
    }
    
    /**
     * AJAX handler for creating Ko-fi payment
     */
    public function ajax_create_kofi_payment() {
        try {
            // Debug logging
            if ($this->settings['debug_mode']) {
                error_log('Ko-fi payment creation started');
                error_log('POST data: ' . print_r($_POST, true));
            }

            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {
                throw new Exception(__('Security check failed', 'epic-membership'));
            }

            $tier_id = intval($_POST['tier_id']);
            $user_id = get_current_user_id();

            if (!$tier_id || !$user_id) {
                throw new Exception(__('Invalid tier or user', 'epic-membership'));
            }

            // Check if Ko-fi is enabled and configured
            if (!$this->is_enabled()) {
                throw new Exception(__('Ko-fi payment gateway is not enabled or configured', 'epic-membership'));
            }

            // Get tier information
            $tier = $this->get_tier($tier_id);
            if (!$tier) {
                throw new Exception(__('Membership tier not found', 'epic-membership'));
            }

            if ($this->settings['debug_mode']) {
                error_log('Tier found: ' . print_r($tier, true));
            }

            // Create pending transaction record
            $transaction_id = $this->create_pending_transaction($user_id, $tier_id, $tier);

            // Generate Ko-fi payment URL
            $payment_url = $this->generate_kofi_payment_url($tier, $transaction_id);

            if ($this->settings['debug_mode']) {
                error_log('Ko-fi payment URL generated: ' . $payment_url);
            }

            wp_send_json_success(array(
                'payment_url' => $payment_url,
                'transaction_id' => $transaction_id
            ));

        } catch (Exception $e) {
            if ($this->settings['debug_mode']) {
                error_log('Ko-fi payment creation error: ' . $e->getMessage());
            }
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Get tier information
     */
    private function get_tier($tier_id) {
        global $wpdb;
        
        $tiers_table = $this->database->get_table('tiers');
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tiers_table WHERE id = %d AND is_active = 1",
            $tier_id
        ));
    }
    
    /**
     * Create pending transaction record
     */
    private function create_pending_transaction($user_id, $tier_id, $tier) {
        global $wpdb;

        $transactions_table = $this->database->get_table('payment_transactions');

        // Generate unique transaction ID for Ko-fi
        $transaction_id = 'kofi_' . time() . '_' . $user_id . '_' . $tier_id . '_' . wp_generate_password(8, false);

        $transaction_data = array(
            'user_id' => $user_id,
            'tier_id' => $tier_id,
            'payment_gateway' => self::GATEWAY_ID,
            'transaction_id' => $transaction_id,
            'amount' => $tier->price,
            'currency' => $this->settings['currency'],
            'payment_status' => 'pending',
            'gateway_status' => 'pending',
            'transaction_data' => json_encode(array(
                'tier_name' => $tier->name,
                'created_via' => 'kofi_payment_request'
            )),
            'created_at' => current_time('mysql')
        );

        if ($this->settings['debug_mode']) {
            error_log('Creating Ko-fi transaction with data: ' . print_r($transaction_data, true));
        }

        $result = $wpdb->insert($transactions_table, $transaction_data);

        if ($result === false) {
            $error_message = 'Failed to create transaction record. Database error: ' . $wpdb->last_error;
            if ($this->settings['debug_mode']) {
                error_log($error_message);
            }
            throw new Exception(__('Failed to create transaction record', 'epic-membership'));
        }

        if ($this->settings['debug_mode']) {
            error_log('Ko-fi transaction created successfully with ID: ' . $wpdb->insert_id);
        }

        return $wpdb->insert_id;
    }
    
    /**
     * Generate Ko-fi payment URL
     */
    private function generate_kofi_payment_url($tier, $transaction_id) {
        $base_url = $this->settings['page_url'];

        if (empty($base_url)) {
            throw new Exception(__('Ko-fi page URL not configured', 'epic-membership'));
        }

        // Ensure URL starts with https://ko-fi.com/
        if (strpos($base_url, 'ko-fi.com/') === false) {
            $base_url = 'https://ko-fi.com/' . ltrim($base_url, '/');
        }

        // Ko-fi URL parameters for better amount suggestion
        $params = array(
            'ref' => 'membership_' . $transaction_id
        );

        // Add amount parameter - Ko-fi will suggest this amount but user can change it
        if ($tier->price > 0) {
            $params['amount'] = number_format($tier->price, 2, '.', '');
        }

        $payment_url = add_query_arg($params, $base_url);

        return $payment_url;
    }
    
    /**
     * Handle Ko-fi webhook
     */
    public function handle_webhook() {
        try {
            $raw_body = file_get_contents('php://input');
            $webhook_data = json_decode($raw_body, true);
            
            if (!$webhook_data) {
                throw new Exception('Invalid webhook data');
            }
            
            // Verify webhook authenticity
            $this->verify_webhook($webhook_data);
            
            // Process webhook event
            $this->process_webhook_event($webhook_data);
            
            wp_send_json_success('Webhook processed');
            
        } catch (Exception $e) {
            $this->log_error('Ko-fi Webhook error: ' . $e->getMessage());
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Verify webhook authenticity
     */
    private function verify_webhook($webhook_data) {
        // Ko-fi sends verification token in the data
        if (empty($webhook_data['verification_token'])) {
            throw new Exception('Missing verification token');
        }
        
        if ($webhook_data['verification_token'] !== $this->verification_token) {
            throw new Exception('Invalid verification token');
        }
        
        return true;
    }
    
    /**
     * Process webhook event
     */
    private function process_webhook_event($webhook_data) {
        // Log webhook data for debugging
        $this->log_debug('Ko-fi webhook received: ' . json_encode($webhook_data));
        
        // Extract payment information
        $payment_amount = floatval($webhook_data['amount'] ?? 0);
        $supporter_email = sanitize_email($webhook_data['email'] ?? '');
        $supporter_name = sanitize_text_field($webhook_data['from_name'] ?? '');
        $message_id = sanitize_text_field($webhook_data['message_id'] ?? '');
        $type = sanitize_text_field($webhook_data['type'] ?? '');
        
        if ($payment_amount <= 0) {
            throw new Exception('Invalid payment amount');
        }
        
        // Find matching user by email
        $user = get_user_by('email', $supporter_email);
        if (!$user) {
            $this->log_error('Ko-fi payment received but no matching user found for email: ' . $supporter_email);
            return;
        }
        
        // Determine appropriate membership tier based on payment amount
        $tier = $this->get_tier_by_amount($payment_amount);
        if (!$tier) {
            $this->log_error('Ko-fi payment received but no matching tier found for amount: ' . $payment_amount);
            return;
        }
        
        // Create transaction record
        $transaction_id = $this->create_kofi_transaction($user->ID, $tier->id, $webhook_data);
        
        // Process membership activation
        $this->process_successful_payment($transaction_id, $webhook_data);
    }

    /**
     * Get tier by payment amount
     */
    private function get_tier_by_amount($amount) {
        global $wpdb;

        $tiers_table = $this->database->get_table('tiers');

        // Find tier with exact price match first
        $tier = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tiers_table WHERE price = %f AND is_active = 1 ORDER BY level DESC LIMIT 1",
            $amount
        ));

        if ($tier) {
            return $tier;
        }

        // If no exact match, find the highest tier that the payment amount can afford
        $tier = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tiers_table WHERE price <= %f AND is_active = 1 ORDER BY level DESC LIMIT 1",
            $amount
        ));

        return $tier;
    }

    /**
     * Create Ko-fi transaction record
     */
    private function create_kofi_transaction($user_id, $tier_id, $webhook_data) {
        global $wpdb;

        $transactions_table = $this->database->get_table('payment_transactions');

        // Generate unique transaction ID for Ko-fi webhook
        $transaction_id = 'kofi_webhook_' . time() . '_' . $user_id . '_' . $tier_id . '_' . wp_generate_password(8, false);

        $transaction_data = array(
            'user_id' => $user_id,
            'tier_id' => $tier_id,
            'payment_gateway' => self::GATEWAY_ID,
            'transaction_id' => $transaction_id,
            'amount' => floatval($webhook_data['amount']),
            'currency' => $this->settings['currency'],
            'payment_status' => 'completed',
            'gateway_status' => 'completed',
            'payer_email' => sanitize_email($webhook_data['email'] ?? ''),
            'transaction_data' => json_encode($webhook_data),
            'processed_at' => current_time('mysql'),
            'created_at' => current_time('mysql')
        );

        $result = $wpdb->insert($transactions_table, $transaction_data);

        if ($result === false) {
            throw new Exception(__('Failed to create Ko-fi transaction record', 'epic-membership'));
        }

        return $wpdb->insert_id;
    }

    /**
     * Process successful payment
     */
    private function process_successful_payment($transaction_id, $webhook_data) {
        global $wpdb;

        $transactions_table = $this->database->get_table('payment_transactions');

        // Get transaction record
        $transaction = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $transactions_table WHERE id = %d",
            $transaction_id
        ));

        if (!$transaction) {
            throw new Exception(__('Transaction record not found', 'epic-membership'));
        }

        // Get tier information
        $tiers_table = $this->database->get_table('tiers');
        $tier = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $tiers_table WHERE id = %d",
            $transaction->tier_id
        ));

        if (!$tier) {
            throw new Exception(__('Membership tier not found', 'epic-membership'));
        }

        // Create or update membership
        $membership_id = $this->create_membership($transaction->user_id, $tier);

        // Update transaction with membership ID
        $wpdb->update(
            $transactions_table,
            array(
                'membership_id' => $membership_id,
                'payment_status' => 'completed'
            ),
            array('id' => $transaction->id)
        );

        // Send confirmation email (if enabled)
        $this->send_payment_confirmation($transaction, $tier);

        return $membership_id;
    }

    /**
     * Create membership for user
     */
    private function create_membership($user_id, $tier) {
        global $wpdb;

        $memberships_table = $this->database->get_table('user_memberships');

        // Deactivate existing memberships
        $wpdb->update(
            $memberships_table,
            array('is_active' => 0),
            array('user_id' => $user_id, 'is_active' => 1)
        );

        // Calculate end date
        $start_date = current_time('mysql');
        $end_date = null;

        if ($tier->duration_days) {
            $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + {$tier->duration_days} days"));
        }

        // Create new membership
        $membership_data = array(
            'user_id' => $user_id,
            'tier_id' => $tier->id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'is_active' => 1,
            'payment_gateway' => self::GATEWAY_ID
        );

        $result = $wpdb->insert($memberships_table, $membership_data);

        if ($result === false) {
            throw new Exception(__('Failed to create membership', 'epic-membership'));
        }

        return $wpdb->insert_id;
    }

    /**
     * Send payment confirmation email
     */
    private function send_payment_confirmation($transaction, $tier) {
        $user = get_user_by('ID', $transaction->user_id);
        if (!$user) {
            return;
        }

        $subject = sprintf(__('Payment Confirmation - %s Membership', 'epic-membership'), $tier->name);

        $message = sprintf(
            __('Hello %s,

Thank you for your Ko-fi support! Your %s membership has been activated.

Membership Details:
- Tier: %s
- Amount: %s %s
- Payment Method: Ko-fi
- Transaction ID: %s

Your membership is now active and you can access all the benefits included with your tier.

Thank you for your support!

Best regards,
%s', 'epic-membership'),
            $user->display_name,
            $tier->name,
            $tier->name,
            $transaction->amount,
            $transaction->currency,
            $transaction->gateway_transaction_id,
            get_bloginfo('name')
        );

        wp_mail($user->user_email, $subject, $message);
    }

    /**
     * Log error message
     */
    private function log_error($message) {
        if (function_exists('error_log')) {
            error_log('[Epic Membership Ko-fi] ERROR: ' . $message);
        }

        // Also log to WordPress debug log if enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Epic Membership Ko-fi] ERROR: ' . $message);
        }
    }

    /**
     * Log debug message
     */
    private function log_debug($message) {
        if (!$this->settings['debug_mode']) {
            return;
        }

        if (function_exists('error_log')) {
            error_log('[Epic Membership Ko-fi] DEBUG: ' . $message);
        }

        // Also log to WordPress debug log if enabled
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Epic Membership Ko-fi] DEBUG: ' . $message);
        }
    }
}
