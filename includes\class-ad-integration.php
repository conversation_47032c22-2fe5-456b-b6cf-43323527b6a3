<?php

/**

 * Ad Integration Class for Epic Membership Plugin

 * Handles ad-free experience for premium members

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Ad_Integration {

    

    /**

     * Database instance

     */

    private $database;



    /**

     * Ad unit manager instance

     */

    private $ad_unit_manager;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->ad_unit_manager = new Epic_Membership_Ad_Unit_Manager();

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Multiple ad placement hooks

        add_action('wp_head', array($this, 'insert_header_ads'), 1);

        add_action('wp_footer', array($this, 'insert_footer_ads'), 999);

        add_action('wp_footer', array($this, 'insert_body_end_ads'), 9999); // Very high priority for body end

        add_filter('the_content', array($this, 'insert_content_ads'), 10);



        // Legacy single ad support (for backward compatibility)

        add_action('wp_head', array($this, 'insert_legacy_ad_codes'), 2);



        // Content filtering for ads (for premium users)

        add_filter('the_content', array($this, 'filter_ads_from_content'), 999);

        add_filter('the_excerpt', array($this, 'filter_ads_from_excerpt'), 999);



        // Widget filtering

        add_filter('widget_display_callback', array($this, 'filter_ad_widgets'), 10, 3);



        // Shortcode filtering

        add_filter('do_shortcode_tag', array($this, 'filter_ad_shortcodes'), 10, 4);



        // JavaScript ad blocking for premium users

        add_action('wp_enqueue_scripts', array($this, 'enqueue_ad_blocking_script'));



        // Additional ad blocking hooks for membership pages

        add_action('wp_head', array($this, 'add_membership_page_ad_blocking'), 1);

        add_action('wp_footer', array($this, 'add_membership_page_ad_blocking_footer'), 999);



        // Force ad blocking on membership pages with high priority

        add_action('template_redirect', array($this, 'force_membership_page_ad_blocking'), 1);

        add_action('wp', array($this, 'force_membership_page_ad_blocking'), 1);



        // Admin settings for ad integration

        add_action('admin_init', array($this, 'register_ad_settings'));



        // Migration hook

        add_action('admin_init', array($this, 'maybe_migrate_legacy_ads'));

    }

    

    /**

     * Insert header ads (multiple ad units)

     */

    public function insert_header_ads() {

        // Only check for custom ad units, not AdSense

        if ($this->should_show_ads()) {

            $this->insert_ads_by_placement('header');

        }

    }



    /**

     * Insert footer ads (multiple ad units)

     */

    public function insert_footer_ads() {

        if (!$this->should_show_ads()) {

            return;

        }



        $this->insert_ads_by_placement('footer');

    }



    /**

     * Insert body-end ads (right before closing </body> tag)

     * This is specifically for ads like Adsterra social bars that need to be placed at the very end

     */

    public function insert_body_end_ads() {

        if (!$this->should_show_ads()) {

            return;

        }



        $this->insert_ads_by_placement('body_end');

    }



    /**

     * Insert content ads (before/after content)

     */

    public function insert_content_ads($content) {

        if (!$this->should_show_ads()) {

            return $content;

        }



        // Insert before content ads

        $before_ads = $this->get_ads_html_by_placement('before_content');



        // Insert after content ads

        $after_ads = $this->get_ads_html_by_placement('after_content');



        return $before_ads . $content . $after_ads;

    }



    /**

     * Insert ads by placement

     */

    private function insert_ads_by_placement($placement) {

        $ad_units = $this->ad_unit_manager->get_ad_units_by_placement($placement, true);



        if (empty($ad_units)) {

            return;

        }



        foreach ($ad_units as $ad_unit) {

            echo "\n<!-- Epic Membership - {$ad_unit->name} ({$ad_unit->ad_type}) -->\n";

            echo $ad_unit->ad_code;

            echo "\n<!-- /Epic Membership - {$ad_unit->name} -->\n";

        }

    }



    /**

     * Get ads HTML by placement

     */

    private function get_ads_html_by_placement($placement) {

        $ad_units = $this->ad_unit_manager->get_ad_units_by_placement($placement, true);



        if (empty($ad_units)) {

            return '';

        }



        $html = '';

        foreach ($ad_units as $ad_unit) {

            $html .= "\n<!-- Epic Membership - {$ad_unit->name} ({$ad_unit->ad_type}) -->\n";

            $html .= $ad_unit->ad_code;

            $html .= "\n<!-- /Epic Membership - {$ad_unit->name} -->\n";

        }



        return $html;

    }



    /**

     * Legacy ad insertion (for backward compatibility)

     */

    public function insert_legacy_ad_codes() {

        // Always insert AdSense code - blocking will be handled by CSS/JS

        $this->insert_adsense_head_code();



        // Insert legacy Adsterra code if enabled and not migrated

        if (!get_option('epic_membership_adsterra_migrated', false)) {

            $this->insert_adsterra_head_code();

        }

    }



    /**

     * Insert AdSense head code for auto ads

     */

    public function insert_adsense_head_code() {

        $adsense_enabled = get_option('epic_membership_adsense_enabled', false);

        if (!$adsense_enabled) {

            return;

        }



        $adsense_code = get_option('epic_membership_adsense_head_code', '');

        if (empty($adsense_code)) {

            return;

        }



        // Always output AdSense code in head - blocking will be handled by CSS/JS for premium users

        echo "\n<!-- Epic Membership - Google AdSense Auto Ads -->\n";

        echo $adsense_code;

        echo "\n<!-- /Epic Membership - Google AdSense Auto Ads -->\n";



        // Add AdSense configuration for proper ad display

        $should_show_ads = $this->should_show_ads();

        $is_status_page = $this->is_membership_status_page();



        echo "\n<script type='text/javascript'>\n";

        echo "window.epicMembershipAdSenseConfig = {\n";

        echo "    enabled: true,\n";

        echo "    shouldShowAds: " . ($should_show_ads ? 'true' : 'false') . ",\n";

        echo "    isStatusPage: " . ($is_status_page ? 'true' : 'false') . ",\n";

        echo "    debug: true\n";

        echo "};\n";

        echo "console.log('Epic Membership AdSense Config:', window.epicMembershipAdSenseConfig);\n";

        echo "</script>\n";

    }



    /**

     * Insert Adsterra head code for ads

     */

    public function insert_adsterra_head_code() {

        $adsterra_enabled = get_option('epic_membership_adsterra_enabled', false);

        if (!$adsterra_enabled) {

            return;

        }



        $adsterra_code = get_option('epic_membership_adsterra_head_code', '');

        if (empty($adsterra_code)) {

            return;

        }



        // Output the Adsterra code in the head

        echo "\n<!-- Epic Membership - Adsterra Ads -->\n";

        echo $adsterra_code;

        echo "\n<!-- /Epic Membership - Adsterra Ads -->\n";

    }



    /**

     * Check if user should see ads

     */

    public function should_show_ads($user_id = null) {

        if (!$user_id) {

            $user_id = get_current_user_id();

        }



        // Always hide ads on membership status page for clean user experience

        if ($this->is_membership_status_page()) {

            return apply_filters('epic_membership_should_show_ads', false, null, 'membership_status_page');

        }



        // Non-logged-in users see ads

        if (!$user_id) {

            return apply_filters('epic_membership_should_show_ads', true, null);

        }



        // Get user membership

        $membership = $this->database->get_user_membership($user_id);



        // No membership = show ads

        if (!$membership) {

            return apply_filters('epic_membership_should_show_ads', true, $membership);

        }



        // Check if membership is active

        if (!$membership->is_active ||

            ($membership->end_date && strtotime($membership->end_date) <= time())) {

            return apply_filters('epic_membership_should_show_ads', true, $membership);

        }



        // Check if tier has ad-free capability

        $capabilities = json_decode($membership->capabilities, true);

        $has_ad_free = is_array($capabilities) && in_array('ad_free_experience', $capabilities);



        return apply_filters('epic_membership_should_show_ads', !$has_ad_free, $membership);

    }



    /**

     * Check if current page is the membership status page

     */

    private function is_membership_status_page() {

        // Check if we're on the membership status page

        $page_id = get_option('epic_membership_status_page_id');



        if ($page_id && is_page($page_id)) {

            return true;

        }



        // Check if current page has membership status shortcode

        global $post;

        if ($post && has_shortcode($post->post_content, 'epic_membership_dashboard')) {

            return true;

        }



        // Check for membership status page body class

        if (function_exists('get_body_class')) {

            $body_classes = get_body_class();

            if (in_array('epic-membership-status-page', $body_classes)) {

                return true;

            }

        }



        // Enhanced URL pattern checking

        $current_url = $_SERVER['REQUEST_URI'] ?? '';

        $current_url = strtolower(trim($current_url, '/'));



        $membership_url_patterns = array(

            'membership-status',

            'member-dashboard',

            'membership-dashboard',

            'my-membership',

            'membership',

            'member-area',

            'user-dashboard'

        );



        foreach ($membership_url_patterns as $pattern) {

            if (strpos($current_url, $pattern) !== false) {

                return true;

            }

        }



        // Check page slug

        global $wp_query;

        if (isset($wp_query->query_vars['pagename'])) {

            $pagename = $wp_query->query_vars['pagename'];

            foreach ($membership_url_patterns as $pattern) {

                if (strpos($pagename, $pattern) !== false) {

                    return true;

                }

            }

        }



        // Check if page title contains membership keywords

        if ($post) {

            $title = strtolower($post->post_title);

            $title_keywords = array('membership', 'member', 'dashboard', 'status');

            foreach ($title_keywords as $keyword) {

                if (strpos($title, $keyword) !== false) {

                    return true;

                }

            }

        }



        return false;

    }



    /**

     * Add selective ad blocking for membership pages in head

     */

    public function add_membership_page_ad_blocking() {

        if ($this->is_membership_status_page()) {

            echo '<style type="text/css">

                /* SELECTIVE AD BLOCKING FOR MEMBERSHIP PAGES */

                /* Always hide ads on membership status pages for clean UX */

                .adsbygoogle,

                ins.adsbygoogle,

                [data-ad-client],

                [data-ad-slot],

                .google-ad,

                .adsense,

                .adsterra,

                .adsterra-banner,

                [data-adsterra],

                .advertisement,

                .ad-banner,

                .ad-container,

                .ad-wrapper,

                .banner-ad,

                .sidebar-ad,

                .header-ad,

                .footer-ad,

                .content-ad,

                [class*="ad-"]:not(.epic-membership):not(.admin):not(.add):not(.address):not(.advance):not(.advanced),

                [id*="ad-"]:not(.epic-membership):not(.admin):not(.add):not(.address):not(.advance):not(.advanced),

                [class*="ads-"]:not(.epic-membership),

                [id*="ads-"]:not(.epic-membership),

                .epic-membership-ad-unit,

                iframe[src*="googlesyndication"],

                iframe[src*="adsterra"],

                iframe[src*="doubleclick"] {

                    display: none !important;

                    visibility: hidden !important;

                    opacity: 0 !important;

                    height: 0 !important;

                    width: 0 !important;

                    margin: 0 !important;

                    padding: 0 !important;

                    overflow: hidden !important;

                    position: absolute !important;

                    left: -9999px !important;

                    top: -9999px !important;

                }



                /* Do not block AdSense scripts - let JavaScript handle selective blocking */

                script[src*="googlesyndication"] {

                    display: block !important;

                    visibility: visible !important;

                    opacity: 1 !important;

                    height: auto !important;

                    width: auto !important;

                    position: static !important;

                    left: auto !important;

                    top: auto !important;

                }

            </style>';

        } else if (!$this->should_show_ads()) {

            // For premium users on non-membership pages, use JavaScript to hide ads instead of CSS

            // This allows AdSense to load properly and be hidden selectively

            echo '<script type="text/javascript">

                document.addEventListener("DOMContentLoaded", function() {

                    if (window.epicMembershipAdSenseConfig && !window.epicMembershipAdSenseConfig.shouldShowAds) {

                        console.log("Epic Membership: Hiding ads for premium user");

                        var adElements = document.querySelectorAll(".adsbygoogle, ins.adsbygoogle, [data-ad-client], [data-ad-slot], .google-ad, .adsense");

                        adElements.forEach(function(el) {

                            el.style.display = "none";

                            el.style.visibility = "hidden";

                        });

                    }

                });

            </script>';

        }

    }



    /**

     * Add aggressive ad blocking JavaScript for membership pages in footer

     */

    public function add_membership_page_ad_blocking_footer() {

        if ($this->is_membership_status_page()) {

            echo '<script type="text/javascript">

                (function() {

                    console.log("Epic Membership: Aggressive ad blocking active for membership page");



                    // Comprehensive ad selectors

                    var adSelectors = [

                        ".adsbygoogle",

                        "ins.adsbygoogle",

                        "[data-ad-client]",

                        "[data-ad-slot]",

                        ".google-ad",

                        ".adsense",

                        ".adsterra",

                        ".adsterra-banner",

                        "[data-adsterra]",

                        ".advertisement",

                        ".ad-banner",

                        ".ad-container",

                        ".ad-wrapper",

                        ".banner-ad",

                        ".sidebar-ad",

                        ".header-ad",

                        ".footer-ad",

                        ".content-ad",

                        ".epic-membership-ad-unit",

                        "iframe[src*=\"googlesyndication\"]",

                        "iframe[src*=\"adsterra\"]",

                        "iframe[src*=\"doubleclick\"]"

                    ];



                    // Function to aggressively hide ads

                    function hideAds() {

                        adSelectors.forEach(function(selector) {

                            try {

                                var elements = document.querySelectorAll(selector);

                                elements.forEach(function(el) {

                                    if (el) {

                                        el.style.display = "none";

                                        el.style.visibility = "hidden";

                                        el.style.opacity = "0";

                                        el.style.height = "0";

                                        el.style.width = "0";

                                        el.style.margin = "0";

                                        el.style.padding = "0";

                                        el.style.overflow = "hidden";

                                        el.style.position = "absolute";

                                        el.style.left = "-9999px";

                                        el.style.top = "-9999px";

                                        el.remove();

                                    }

                                });

                            } catch(e) {

                                console.log("Epic Membership: Error hiding ads with selector " + selector, e);

                            }

                        });



                        // Also hide elements with ad-related classes/IDs

                        var allElements = document.querySelectorAll("*");

                        allElements.forEach(function(el) {

                            var className = el.className || "";

                            var id = el.id || "";



                            if ((className.includes("ad-") || className.includes("ads-") ||

                                 id.includes("ad-") || id.includes("ads-")) &&

                                !className.includes("epic-membership") &&

                                !className.includes("admin") &&

                                !className.includes("add") &&

                                !className.includes("address") &&

                                !className.includes("advance")) {

                                el.style.display = "none";

                                el.style.visibility = "hidden";

                            }

                        });

                    }



                    // Hide ads immediately

                    hideAds();



                    // Hide ads when DOM is ready

                    if (document.readyState === "loading") {

                        document.addEventListener("DOMContentLoaded", hideAds);

                    }



                    // Hide ads after window load

                    window.addEventListener("load", hideAds);



                    // Monitor for new ads being added

                    var observer = new MutationObserver(function(mutations) {

                        var shouldHide = false;

                        mutations.forEach(function(mutation) {

                            if (mutation.addedNodes.length > 0) {

                                shouldHide = true;

                            }

                        });

                        if (shouldHide) {

                            setTimeout(hideAds, 100);

                        }

                    });



                    observer.observe(document.body, {

                        childList: true,

                        subtree: true

                    });



                    // Periodically check for new ads

                    setInterval(hideAds, 2000);



                })();

            </script>';

        }

    }



    /**

     * Force ad blocking on membership pages with high priority

     */

    public function force_membership_page_ad_blocking() {

        // Only apply on membership pages

        if (!$this->is_membership_status_page()) {

            return;

        }



        // Add targeted ad blocking only for membership pages

        add_filter('the_content', array($this, 'strip_ads_from_content'), 999);

        add_action('wp_print_scripts', array($this, 'block_ad_scripts'), 1);

    }



    /**

     * Strip ads from content aggressively

     */

    public function strip_ads_from_content($content) {

        if (!$this->is_membership_status_page()) {

            return $content;

        }



        // Remove script tags with ad domains

        $content = preg_replace('/<script[^>]*src[^>]*googlesyndication[^>]*>.*?<\/script>/is', '', $content);

        $content = preg_replace('/<script[^>]*src[^>]*adsterra[^>]*>.*?<\/script>/is', '', $content);

        $content = preg_replace('/<script[^>]*src[^>]*doubleclick[^>]*>.*?<\/script>/is', '', $content);



        // Remove iframe ads

        $content = preg_replace('/<iframe[^>]*src[^>]*googlesyndication[^>]*>.*?<\/iframe>/is', '', $content);

        $content = preg_replace('/<iframe[^>]*src[^>]*adsterra[^>]*>.*?<\/iframe>/is', '', $content);

        $content = preg_replace('/<iframe[^>]*src[^>]*doubleclick[^>]*>.*?<\/iframe>/is', '', $content);



        // Remove div elements with ad classes

        $content = preg_replace('/<div[^>]*class[^>]*adsbygoogle[^>]*>.*?<\/div>/is', '', $content);

        $content = preg_replace('/<div[^>]*class[^>]*advertisement[^>]*>.*?<\/div>/is', '', $content);

        $content = preg_replace('/<div[^>]*class[^>]*ad-banner[^>]*>.*?<\/div>/is', '', $content);



        // Remove ins elements (AdSense)

        $content = preg_replace('/<ins[^>]*class[^>]*adsbygoogle[^>]*>.*?<\/ins>/is', '', $content);



        return $content;

    }



    /**

     * Block ad scripts from loading

     */

    public function block_ad_scripts() {

        if (!$this->is_membership_status_page()) {

            return;

        }



        global $wp_scripts;



        if (!$wp_scripts) {

            return;

        }



        $ad_domains = array(

            'googlesyndication.com',

            'adsterra.com',

            'doubleclick.net',

            'googletagmanager.com'

        );



        foreach ($wp_scripts->registered as $handle => $script) {

            if (isset($script->src)) {

                foreach ($ad_domains as $domain) {

                    if (strpos($script->src, $domain) !== false) {

                        wp_dequeue_script($handle);

                        wp_deregister_script($handle);

                    }

                }

            }

        }

    }



    /**

     * Filter ads from content

     */

    public function filter_ads_from_content($content) {

        if ($this->should_show_ads()) {

            return $content;

        }

        

        // Remove common ad patterns

        $ad_patterns = $this->get_ad_patterns();

        

        foreach ($ad_patterns as $pattern) {

            $content = preg_replace($pattern, '', $content);

        }

        

        return $content;

    }

    

    /**

     * Filter ads from excerpt

     */

    public function filter_ads_from_excerpt($excerpt) {

        if ($this->should_show_ads()) {

            return $excerpt;

        }

        

        // Remove ads from excerpt

        $ad_patterns = $this->get_ad_patterns();

        

        foreach ($ad_patterns as $pattern) {

            $excerpt = preg_replace($pattern, '', $excerpt);

        }

        

        return $excerpt;

    }

    

    /**

     * Get common ad patterns for filtering

     */

    private function get_ad_patterns() {

        $patterns = array(

            // Google AdSense

            '/<script[^>]*googlesyndication\.com[^>]*>.*?<\/script>/is',

            '/<ins[^>]*adsbygoogle[^>]*>.*?<\/ins>/is',



            // Adsterra - keyword-based patterns

            '/<script[^>]*adsterra[^>]*>.*?<\/script>/is',

            '/<script[^>]*adsterranet[^>]*>.*?<\/script>/is',

            '/<div[^>]*class="[^"]*\b(adsterra|adsterranet)\b[^"]*"[^>]*>.*?<\/div>/is',

            '/<div[^>]*id="[^"]*\b(adsterra|adsterranet)\b[^"]*"[^>]*>.*?<\/div>/is',



            // Adsterra - domain-specific patterns (common Adsterra domains)

            '/<script[^>]*src=[\'"][^\'"]*(breedsmuteexams\.com|adsterra\.com|adsterranet\.com)[^\'\"]*[\'"][^>]*>.*?<\/script>/is',

            '/<script[^>]*src=[\'"][^\'"]*(displaycontentnetwork\.com|adsystem\.com)[^\'\"]*[\'"][^>]*>.*?<\/script>/is',



            // Epic Membership ad unit containers

            '/<div[^>]*class="[^"]*epic-membership-ad-unit[^"]*"[^>]*>.*?<\/div>/is',



            // Generic ad containers

            '/<div[^>]*class="[^"]*\b(ad|ads|advertisement|adsense|adblock|banner)\b[^"]*"[^>]*>.*?<\/div>/is',

            '/<div[^>]*id="[^"]*\b(ad|ads|advertisement|adsense|adblock|banner)\b[^"]*"[^>]*>.*?<\/div>/is',



            // Common ad networks

            '/<script[^>]*doubleclick\.net[^>]*>.*?<\/script>/is',

            '/<script[^>]*amazon-adsystem\.com[^>]*>.*?<\/script>/is',

            '/<script[^>]*media\.net[^>]*>.*?<\/script>/is',



            // Ad shortcodes (customize based on your theme/plugins)

            '/\[ad[^\]]*\]/i',

            '/\[adsense[^\]]*\]/i',

            '/\[adsterra[^\]]*\]/i',

            '/\[advertisement[^\]]*\]/i',

        );



        return apply_filters('epic_membership_ad_patterns', $patterns);

    }

    

    /**

     * Filter ad widgets

     */

    public function filter_ad_widgets($instance, $widget, $args) {

        if ($this->should_show_ads()) {

            return $instance;

        }

        

        // List of ad widget classes to hide

        $ad_widget_classes = array(

            'WP_Widget_Text', // If it contains ads

            'Adsense_Widget',

            'Ad_Widget',

            'Advertisement_Widget',

            'Banner_Widget'

        );

        

        $ad_widget_classes = apply_filters('epic_membership_ad_widget_classes', $ad_widget_classes);

        

        if (in_array(get_class($widget), $ad_widget_classes)) {

            return false; // Hide the widget

        }

        

        // Check widget content for ad patterns

        if (isset($instance['text'])) {

            $ad_patterns = $this->get_ad_patterns();

            foreach ($ad_patterns as $pattern) {

                if (preg_match($pattern, $instance['text'])) {

                    return false; // Hide widget with ad content

                }

            }

        }

        

        return $instance;

    }

    

    /**

     * Filter ad shortcodes

     */

    public function filter_ad_shortcodes($output, $tag, $attr, $m) {

        if ($this->should_show_ads()) {

            return $output;

        }

        

        // List of ad shortcodes to filter

        $ad_shortcodes = array(

            'ad',

            'ads',

            'adsense',

            'adsterra',

            'advertisement',

            'banner',

            'google_ad'

        );

        

        $ad_shortcodes = apply_filters('epic_membership_ad_shortcodes', $ad_shortcodes);

        

        if (in_array($tag, $ad_shortcodes)) {

            return ''; // Remove ad shortcode output

        }

        

        return $output;

    }

    



    

    /**

     * Enqueue ad blocking script for premium users

     */

    public function enqueue_ad_blocking_script() {

        // Always enqueue the script, but let it decide whether to block ads

        wp_add_inline_script('epic-membership-frontend', $this->get_ad_blocking_js());

    }

    

    /**

     * Get JavaScript for blocking ads

     */

    private function get_ad_blocking_js() {

        $is_status_page = $this->is_membership_status_page();

        $extra_selectors = $is_status_page ? "

                // Additional selectors for membership status page

                '.advertisement',

                '.ad-banner',

                '.ad-container',

                '.ad-wrapper',

                '.banner-ad',

                '.sidebar-ad',

                '.header-ad',

                '.footer-ad',

                '.content-ad',

                '.widget_text .textwidget iframe',

                '.widget_custom_html iframe',

                '[class*=\"ad-\"]:not([class*=\"adsense\"]):not([class*=\"adsbygoogle\"])',

                '[id*=\"ad-\"]:not([id*=\"adsense\"]):not([id*=\"adsbygoogle\"])',

                '[class*=\"ads-\"]:not([class*=\"adsense\"]):not([class*=\"adsbygoogle\"])',

                '[id*=\"ads-\"]:not([id*=\"adsense\"]):not([id*=\"adsbygoogle\"])'," : "";



        return "

        (function() {

            console.log('Epic Membership: Ad blocking script loaded. Should show ads:', window.epicMembershipAdSenseConfig ? window.epicMembershipAdSenseConfig.shouldShowAds : 'unknown');



            // Check if we should show ads

            if (window.epicMembershipAdSenseConfig && window.epicMembershipAdSenseConfig.shouldShowAds) {

                console.log('Epic Membership: User should see ads, skipping ad blocking for AdSense');

                return; // Don't block ads for users who should see them

            }



            // Hide non-AdSense ads and AdSense for premium users

            var adSelectors = [

                'ins.adsbygoogle',

                '.adsbygoogle',

                '[data-ad-client]',

                '[data-ad-slot]',

                '.google-ad',

                '.adsense',

                '.adsterra',

                '.adsterra-banner',

                '[data-adsterra]'$extra_selectors

            ];



            // Function to hide ad elements

            function hideAdElements() {

                adSelectors.forEach(function(selector) {

                    var elements = document.querySelectorAll(selector);

                    elements.forEach(function(el) {

                        el.style.display = 'none !important';

                        el.style.visibility = 'hidden !important';

                        console.log('Epic Membership: Hiding ad element:', el);

                    });

                });

            }



            // Hide existing ads

            hideAdElements();



            // Block non-AdSense ad scripts from loading (but allow AdSense for non-premium users)

            var adDomains = [

                'breedsmuteexams.com',

                'adsterra.com',

                'adsterranet.com',

                'displaycontentnetwork.com'

            ];



            // Only block AdSense if user shouldn't see ads

            if (!window.epicMembershipAdSenseConfig || !window.epicMembershipAdSenseConfig.shouldShowAds) {

                adDomains.push('googlesyndication.com');

            }



            // Hide Epic Membership ad unit containers

            $('.epic-membership-ad-unit').remove();



            // Additional blocking for membership status page

            if (document.body.classList.contains('epic-membership-status-page') ||

                document.querySelector('.epic-membership-dashboard')) {



                // Hide common ad widgets and containers

                var statusPageAdSelectors = [

                    '.widget_text iframe',

                    '.widget_custom_html iframe',

                    '.textwidget script',

                    '.sidebar .advertisement',

                    '.sidebar .ad-banner',

                    '.content-area .advertisement',

                    '.content-area .ad-banner'

                ];



                statusPageAdSelectors.forEach(function(selector) {

                    var elements = document.querySelectorAll(selector);

                    elements.forEach(function(el) {

                        // Check if it's likely an ad

                        var src = el.src || '';

                        var content = el.innerHTML || '';

                        if (src.includes('googlesyndication') ||

                            src.includes('adsterra') ||

                            content.includes('adsbygoogle') ||

                            content.includes('adsterra')) {

                            el.style.display = 'none !important';

                            el.style.visibility = 'hidden !important';

                        }

                    });

                });

            }



            var observer = new MutationObserver(function(mutations) {

                mutations.forEach(function(mutation) {

                    mutation.addedNodes.forEach(function(node) {

                        if (node.tagName === 'SCRIPT' && node.src) {

                            var shouldBlock = adDomains.some(function(domain) {

                                return node.src.includes(domain);

                            });

                            if (shouldBlock) {

                                node.remove();

                            }

                        }

                        // Also hide any new ad elements

                        if (node.nodeType === 1) {

                            hideAdElements();

                        }

                    });

                });

            });



            observer.observe(document.head, { childList: true });

            observer.observe(document.body, { childList: true, subtree: true });



            // Periodically check for new ads (AdSense can be tricky)

            setInterval(hideAdElements, 1000);

        })();

        ";

    }

    

    /**

     * Register ad integration settings

     */

    public function register_ad_settings() {

        // Register settings for AdSense

        register_setting('epic_membership_ads', 'epic_membership_adsense_enabled');

        register_setting('epic_membership_ads', 'epic_membership_adsense_head_code');



        // Register settings for Adsterra

        register_setting('epic_membership_ads', 'epic_membership_adsterra_enabled');

        register_setting('epic_membership_ads', 'epic_membership_adsterra_head_code');



        // Keep legacy setting for backward compatibility

        register_setting('epic_membership_ads', 'epic_membership_ad_integration');

    }

    

    /**

     * Get ad-free message for display

     */

    public function get_ad_free_message() {

        if (!$this->should_show_ads()) {

            return '<div class="epic-membership-ad-free-message">' .

                   __('Enjoying an ad-free experience with your premium membership!', 'epic-membership') .

                   '</div>';

        }

        return '';

    }



    /**

     * Helper function for themes/plugins to check ad display

     */

    public static function show_ads() {

        static $instance = null;

        if (!$instance) {

            $instance = new self();

        }

        return $instance->should_show_ads();

    }



    /**

     * Template tag for conditional ad display

     * Note: With auto ads, this is mainly for custom ad placements

     */

    public static function conditional_ad($ad_code, $fallback = '') {

        if (self::show_ads()) {

            echo $ad_code;

        } else {

            echo $fallback;

        }

    }



    /**

     * Check if AdSense is enabled and configured

     */

    public function is_adsense_configured() {

        $adsense_enabled = get_option('epic_membership_adsense_enabled', false);

        $adsense_code = get_option('epic_membership_adsense_head_code', '');



        return ($adsense_enabled && !empty($adsense_code));

    }



    /**

     * Check if Adsterra is enabled and configured

     */

    public function is_adsterra_configured() {

        $adsterra_enabled = get_option('epic_membership_adsterra_enabled', false);

        $adsterra_code = get_option('epic_membership_adsterra_head_code', '');



        return ($adsterra_enabled && !empty($adsterra_code));

    }



    /**

     * Check if any ad provider is configured

     */

    public function is_any_ad_configured() {

        return $this->is_adsense_configured() || $this->is_adsterra_configured() || $this->has_active_ad_units();

    }



    /**

     * Check if there are active ad units

     */

    public function has_active_ad_units() {

        $ad_units = $this->ad_unit_manager->get_ad_units(true);

        return !empty($ad_units);

    }



    /**

     * Maybe migrate legacy ads

     */

    public function maybe_migrate_legacy_ads() {

        // Only run migration once

        if (get_option('epic_membership_adsterra_migrated', false)) {

            return;

        }



        // Run migration

        $this->ad_unit_manager->migrate_legacy_ad_unit();

    }

}

