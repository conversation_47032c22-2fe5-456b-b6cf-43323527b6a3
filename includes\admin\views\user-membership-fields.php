<?php

/**

 * User membership fields for user profile page

 */



if (!defined('ABSPATH')) {

    exit;

}

?>



<h3><?php _e('Epic Membership', 'epic-membership'); ?></h3>



<table class="form-table" role="presentation">

    <tbody>

        <tr>

            <th scope="row">

                <label for="epic_membership_current_status"><?php _e('Current Status', 'epic-membership'); ?></label>

            </th>

            <td>

                <?php if ($membership): ?>

                    <div class="membership-status-display">

                        <span class="membership-tier tier-level-<?php echo esc_attr($membership->tier_level); ?>">

                            <?php echo esc_html($membership->tier_name); ?>

                        </span>

                        <div class="membership-details">

                            <div>

                                <strong><?php _e('Start Date:', 'epic-membership'); ?></strong>

                                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($membership->start_date))); ?>

                            </div>

                            <?php if ($membership->end_date): ?>

                                <div>

                                    <strong><?php _e('End Date:', 'epic-membership'); ?></strong>

                                    <?php 

                                    $end_time = strtotime($membership->end_date);

                                    $is_expired = $end_time < time();

                                    $is_expiring = !$is_expired && $end_time < strtotime('+7 days');

                                    ?>

                                    <span class="<?php echo $is_expired ? 'expired' : ($is_expiring ? 'expiring-soon' : ''); ?>">

                                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $end_time)); ?>

                                        <?php if ($is_expired): ?>

                                            <span class="status-indicator expired"><?php _e('(Expired)', 'epic-membership'); ?></span>

                                        <?php elseif ($is_expiring): ?>

                                            <span class="status-indicator expiring"><?php _e('(Expiring Soon)', 'epic-membership'); ?></span>

                                        <?php endif; ?>

                                    </span>

                                </div>

                            <?php else: ?>

                                <div>

                                    <strong><?php _e('Duration:', 'epic-membership'); ?></strong>

                                    <span class="lifetime"><?php _e('Lifetime', 'epic-membership'); ?></span>

                                </div>

                            <?php endif; ?>

                            <div>

                                <strong><?php _e('Status:', 'epic-membership'); ?></strong>

                                <?php if ($membership->is_active && (!$membership->end_date || strtotime($membership->end_date) > time())): ?>

                                    <span class="status-active"><?php _e('Active', 'epic-membership'); ?></span>

                                <?php else: ?>

                                    <span class="status-inactive"><?php _e('Inactive/Expired', 'epic-membership'); ?></span>

                                <?php endif; ?>

                            </div>

                        </div>

                    </div>

                <?php else: ?>

                    <div class="no-membership">

                        <span class="status-none"><?php _e('No active membership', 'epic-membership'); ?></span>

                    </div>

                <?php endif; ?>

            </td>

        </tr>

        

        <tr>

            <th scope="row">

                <label for="epic_membership_tier_id"><?php _e('Update Membership Tier', 'epic-membership'); ?></label>

            </th>

            <td>

                <select name="epic_membership_tier_id" id="epic_membership_tier_id" class="regular-text">

                    <option value=""><?php _e('No Membership', 'epic-membership'); ?></option>

                    <?php foreach ($tiers as $tier): ?>

                        <option value="<?php echo esc_attr($tier->id); ?>" 

                                <?php selected($membership ? $membership->tier_id : '', $tier->id); ?>>

                            <?php echo esc_html($tier->name); ?> 

                            (Level <?php echo esc_html($tier->level); ?>)

                            <?php if ($tier->price > 0): ?>

                                - $<?php echo number_format($tier->price, 2); ?>

                            <?php endif; ?>

                        </option>

                    <?php endforeach; ?>

                </select>

                <p class="description">

                    <?php _e('Select a membership tier to assign to this user. This will replace any existing membership.', 'epic-membership'); ?>

                </p>

            </td>

        </tr>

        

        <tr>

            <th scope="row">

                <label for="epic_membership_duration_days"><?php _e('Custom Duration (Days)', 'epic-membership'); ?></label>

            </th>

            <td>

                <input type="number" 

                       name="epic_membership_duration_days" 

                       id="epic_membership_duration_days" 

                       min="1" 

                       class="small-text">

                <p class="description">

                    <?php _e('Override the default tier duration. Leave empty to use the tier\'s default duration.', 'epic-membership'); ?>

                </p>

            </td>

        </tr>

        

        <tr>

            <th scope="row">

                <label for="epic_membership_notes"><?php _e('Membership Notes', 'epic-membership'); ?></label>

            </th>

            <td>

                <textarea name="epic_membership_notes" 

                          id="epic_membership_notes" 

                          rows="3" 

                          class="large-text"><?php echo esc_textarea($membership->notes ?? ''); ?></textarea>

                <p class="description">

                    <?php _e('Internal notes about this user\'s membership (not visible to the user).', 'epic-membership'); ?>

                </p>

            </td>

        </tr>

        

        <?php if ($membership): ?>

        <tr>

            <th scope="row"><?php _e('Quick Actions', 'epic-membership'); ?></th>

            <td>

                <div class="membership-quick-actions">

                    <?php if ($membership->end_date && strtotime($membership->end_date) > time()): ?>

                        <button type="button" 

                                class="button button-secondary extend-membership-btn" 

                                data-user-id="<?php echo esc_attr($user->ID); ?>">

                            <?php _e('Extend by 30 Days', 'epic-membership'); ?>

                        </button>

                    <?php endif; ?>

                    

                    <?php if (!$membership->is_active || ($membership->end_date && strtotime($membership->end_date) <= time())): ?>

                        <button type="button" 

                                class="button button-secondary reactivate-membership-btn" 

                                data-user-id="<?php echo esc_attr($user->ID); ?>">

                            <?php _e('Reactivate Membership', 'epic-membership'); ?>

                        </button>

                    <?php endif; ?>

                </div>

                <p class="description">

                    <?php _e('Quick actions for common membership management tasks.', 'epic-membership'); ?>

                </p>

            </td>

        </tr>

        <?php endif; ?>

    </tbody>

</table>



<style>

.membership-status-display {

    background: #f9f9f9;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 15px;

    margin-bottom: 10px;

}



.membership-tier {

    display: inline-block;

    padding: 4px 12px;

    border-radius: 3px;

    font-size: 12px;

    font-weight: bold;

    text-transform: uppercase;

    margin-bottom: 10px;

}



.membership-tier.tier-level-0 {

    background: #e0e0e0;

    color: #666;

}



.membership-tier.tier-level-10 {

    background: #d4edda;

    color: #155724;

}



.membership-tier.tier-level-20 {

    background: #d1ecf1;

    color: #0c5460;

}



.membership-details {

    font-size: 13px;

    line-height: 1.5;

}



.membership-details div {

    margin: 5px 0;

}



.status-indicator.expired {

    color: #d63638;

    font-weight: bold;

}



.status-indicator.expiring {

    color: #dba617;

    font-weight: bold;

}



.expiring-soon {

    color: #dba617;

    font-weight: bold;

}



.expired {

    color: #d63638;

    font-weight: bold;

}



.lifetime {

    color: #0073aa;

    font-weight: bold;

}



.status-active {

    color: #00a32a;

    font-weight: bold;

}



.status-inactive {

    color: #d63638;

    font-weight: bold;

}



.status-none {

    color: #646970;

    font-style: italic;

}



.no-membership {

    background: #f9f9f9;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 15px;

    text-align: center;

    color: #646970;

}



.membership-quick-actions {

    margin-bottom: 10px;

}



.membership-quick-actions .button {

    margin-right: 10px;

    margin-bottom: 5px;

}

</style>



<script>

jQuery(document).ready(function($) {

    // Handle extend membership button

    $('.extend-membership-btn').on('click', function() {

        var userId = $(this).data('user-id');

        var days = prompt('<?php echo esc_js(__('Extend membership by how many days?', 'epic-membership')); ?>', '30');

        

        if (days && parseInt(days) > 0) {

            extendMembership(userId, parseInt(days));

        }

    });

    

    // Handle reactivate membership button

    $('.reactivate-membership-btn').on('click', function() {

        var userId = $(this).data('user-id');

        var days = prompt('<?php echo esc_js(__('Reactivate membership for how many days?', 'epic-membership')); ?>', '30');

        

        if (days && parseInt(days) > 0) {

            // Set the duration field and trigger a tier change to reactivate

            $('#epic_membership_duration_days').val(days);

            

            // If there's a current tier, select it to trigger reactivation

            var currentTier = $('#epic_membership_tier_id').val();

            if (currentTier) {

                alert('<?php echo esc_js(__('Duration set. Please save the user profile to reactivate the membership.', 'epic-membership')); ?>');

            } else {

                alert('<?php echo esc_js(__('Please select a membership tier and save the user profile.', 'epic-membership')); ?>');

            }

        }

    });

    

    function extendMembership(userId, days) {

        $.ajax({

            url: ajaxurl,

            type: 'POST',

            data: {

                action: 'epic_membership_extend_membership',

                user_id: userId,

                days: days,

                nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'

            },

            beforeSend: function() {

                $('.extend-membership-btn').prop('disabled', true).text('<?php echo esc_js(__('Extending...', 'epic-membership')); ?>');

            },

            success: function(response) {

                if (response.success) {

                    alert('<?php echo esc_js(__('Membership extended successfully!', 'epic-membership')); ?>');

                    location.reload();

                } else {

                    alert('<?php echo esc_js(__('Error:', 'epic-membership')); ?> ' + response.data);

                }

            },

            error: function() {

                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'epic-membership')); ?>');

            },

            complete: function() {

                $('.extend-membership-btn').prop('disabled', false).text('<?php echo esc_js(__('Extend by 30 Days', 'epic-membership')); ?>');

            }

        });

    }

    

    // Show/hide duration field based on tier selection

    $('#epic_membership_tier_id').on('change', function() {

        var tierId = $(this).val();

        if (tierId) {

            $('#epic_membership_duration_days').closest('tr').show();

        } else {

            $('#epic_membership_duration_days').closest('tr').hide();

            $('#epic_membership_duration_days').val('');

        }

    });

    

    // Initialize visibility

    $('#epic_membership_tier_id').trigger('change');

});

</script>

