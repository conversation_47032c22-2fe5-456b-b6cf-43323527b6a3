/**
 * Ko-fi Integration JavaScript for Epic Membership Plugin
 */

(function($) {
    'use strict';

    var EpicMembershipKofi = {
        
        /**
         * Initialize Ko-fi integration
         */
        init: function() {
            console.log('Epic Membership Ko-fi integration initialized');
            this.bindEvents();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            var self = this;

            // Handle Ko-fi payment button clicks with high priority
            $(document).on('click.kofi', '.epic-membership-kofi-button', function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation(); // Stop other handlers from running
                var $button = $(this);
                var tierId = $button.data('tier-id');

                console.log('Ko-fi button clicked, tier ID:', tierId); // Debug log

                if (tierId) {
                    self.showPaymentOptions(tierId, $button);
                }
            });
        },
        
        /**
         * Show payment options modal
         */
        showPaymentOptions: function(tierId, $triggerButton) {
            this.showEnhancedPaymentModal(tierId);
        },
        
        /**
         * Show enhanced payment modal with Ko-fi instructions
         */
        showEnhancedPaymentModal: function(tierId) {
            var self = this;

            // Get tier information from button data
            var $tierButton = $('.epic-membership-kofi-button[data-tier-id="' + tierId + '"]');
            var tierName = $tierButton.data('tier-name') || 'Premium';
            var tierPrice = $tierButton.data('tier-price') || '5.00';

            // Create modal HTML with Ko-fi instructions
            var modalHtml = `
                <div id="epic-membership-kofi-modal" class="epic-membership-modal-overlay">
                    <div class="epic-membership-modal">
                        <div class="epic-membership-modal-header">
                            <h3>Ko-fi Payment Instructions</h3>
                            <button type="button" class="epic-membership-modal-close">&times;</button>
                        </div>
                        <div class="epic-membership-modal-content">
                            <div class="epic-membership-kofi-instructions">
                                <div class="kofi-tier-info">
                                    <h4>Upgrading to: ${tierName} Membership</h4>
                                    <p class="tier-price">Amount: <strong>$${tierPrice}</strong></p>
                                </div>

                                <div class="kofi-payment-steps">
                                    <h4>📋 Payment Instructions:</h4>
                                    <ol>
                                        <li><strong>You'll be redirected to Ko-fi</strong> in a new tab</li>
                                        <li><strong>Important:</strong> Change the amount to <span class="highlight-amount">$${tierPrice}</span></li>
                                        <li><strong>Use the same email</strong> as your account here</li>
                                        <li><strong>Complete the payment</strong> on Ko-fi</li>
                                        <li><strong>Your membership will activate automatically</strong></li>
                                    </ol>
                                </div>

                                <div class="kofi-amount-notice">
                                    <p><strong>⚠️ Important:</strong> Ko-fi shows $2 by default. Please manually change it to <strong>$${tierPrice}</strong> for ${tierName} membership.</p>
                                </div>

                                <div class="epic-membership-loading" style="display: none;">
                                    <div class="epic-membership-spinner"></div>
                                    <p>Redirecting to Ko-fi...</p>
                                </div>

                                <div class="modal-actions">
                                    <button type="button" class="kofi-proceed-btn" data-tier-id="${tierId}">
                                        Proceed to Ko-fi ($${tierPrice})
                                    </button>
                                    <button type="button" class="kofi-cancel-btn">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            $('#epic-membership-kofi-modal').remove();

            // Add modal to page
            $('body').append(modalHtml);

            // Show modal
            $('#epic-membership-kofi-modal').fadeIn(300);

            // Bind events
            $('#epic-membership-kofi-modal .epic-membership-modal-close, #epic-membership-kofi-modal .kofi-cancel-btn').on('click', function(e) {
                self.closeModal();
            });

            $('#epic-membership-kofi-modal .kofi-proceed-btn').on('click', function(e) {
                e.preventDefault();
                var tierId = $(this).data('tier-id');

                // Show loading state
                $('.epic-membership-kofi-instructions').hide();
                $('.epic-membership-loading').show();

                // Create Ko-fi payment
                self.createKofiPayment(tierId);
            });

            // Close modal when clicking overlay
            $('#epic-membership-kofi-modal').on('click', function(e) {
                if (e.target === this) {
                    self.closeModal();
                }
            });
        },
        
        /**
         * Create Ko-fi payment
         */
        createKofiPayment: function(tierId) {
            var self = this;

            console.log('Creating Ko-fi payment for tier ID:', tierId);

            $.ajax({
                url: epicMembershipKofi.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'epic_membership_create_kofi_payment',
                    tier_id: tierId,
                    nonce: epicMembershipKofi.nonce
                },
                success: function(response) {
                    console.log('Ko-fi payment response:', response);

                    if (response.success) {
                        console.log('Ko-fi payment URL:', response.data.payment_url);

                        // Get tier info for success message
                        var $tierButton = $('.epic-membership-kofi-button[data-tier-id="' + tierId + '"]');
                        var tierName = $tierButton.data('tier-name') || 'Premium';
                        var tierPrice = $tierButton.data('tier-price') || '5.00';

                        // Redirect to Ko-fi payment page
                        window.open(response.data.payment_url, '_blank');

                        // Update modal content with detailed instructions
                        self.updateModalContent(
                            'Ko-fi Payment Opened',
                            `<div class="epic-membership-success">
                                <div class="success-icon">✅</div>
                                <h4>Ko-fi tab opened successfully!</h4>

                                <div class="payment-reminder">
                                    <h5>🔔 Important Reminders:</h5>
                                    <ul>
                                        <li><strong>Change amount to $${tierPrice}</strong> for ${tierName} membership</li>
                                        <li><strong>Use the same email address</strong> as this account</li>
                                        <li><strong>Complete the payment</strong> on Ko-fi</li>
                                    </ul>
                                </div>

                                <div class="auto-activation">
                                    <p><strong>✨ Your membership will activate automatically</strong> after payment!</p>
                                    <p>You'll receive a confirmation email once processed.</p>
                                </div>

                                <div class="help-links">
                                    <p><small>Having issues? <a href="#" onclick="alert('Contact support if payment doesn\\'t process within 5 minutes.')">Contact Support</a></small></p>
                                </div>
                            </div>`
                        );

                        // Auto-close modal after longer delay for instructions
                        setTimeout(function() {
                            self.closeModal();
                        }, 8000);

                    } else {
                        console.error('Ko-fi payment error:', response.data);
                        self.showError(response.data || epicMembershipKofi.strings.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Ko-fi AJAX error:', {xhr: xhr, status: status, error: error});
                    var errorMessage = epicMembershipKofi.strings.error;

                    // Try to get more specific error message
                    if (xhr.responseJSON && xhr.responseJSON.data) {
                        errorMessage = xhr.responseJSON.data;
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.data) {
                                errorMessage = response.data;
                            }
                        } catch (e) {
                            // Use default error message
                        }
                    }

                    self.showError(errorMessage);
                }
            });
        },
        
        /**
         * Update modal content
         */
        updateModalContent: function(title, content) {
            $('#epic-membership-kofi-modal .epic-membership-modal-header h3').text(title);
            $('#epic-membership-kofi-modal .epic-membership-modal-content').html(content);
        },
        
        /**
         * Close modal
         */
        closeModal: function() {
            $('#epic-membership-kofi-modal').fadeOut(300, function() {
                $(this).remove();
            });
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            // Provide more helpful error messages
            var helpText = '';
            if (message.includes('not enabled or configured')) {
                helpText = '<p><small>Please contact the site administrator to configure Ko-fi payments.</small></p>';
            } else if (message.includes('Ko-fi page URL not configured')) {
                helpText = '<p><small>Ko-fi payment gateway needs to be configured by the site administrator.</small></p>';
            } else if (message.includes('Failed to create transaction')) {
                helpText = '<p><small>There was a technical issue. Please try again or contact support.</small></p>';
            }

            this.updateModalContent(
                'Payment Error',
                `<div class="epic-membership-error">
                    <p>${message}</p>
                    ${helpText}
                    <button type="button" class="epic-membership-button epic-membership-modal-close">Close</button>
                </div>`
            );
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.updateModalContent(
                'Payment Successful',
                `<div class="epic-membership-success">
                    <p>${message}</p>
                    <button type="button" class="epic-membership-button epic-membership-modal-close">Close</button>
                </div>`
            );
        },
        
        /**
         * Show message with type
         */
        showMessage: function(message, type) {
            type = type || 'info';
            
            var messageClass = 'epic-membership-' + type;
            var messageHtml = `<div class="${messageClass}"><p>${message}</p></div>`;
            
            // Find or create message container
            var $container = $('.epic-membership-messages');
            if (!$container.length) {
                $container = $('<div class="epic-membership-messages"></div>');
                $('.epic-membership-tiers').prepend($container);
            }
            
            // Add message
            $container.html(messageHtml).show();
            
            // Auto-hide after delay
            setTimeout(function() {
                $container.fadeOut();
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        EpicMembershipKofi.init();
    });

    // Make available globally
    window.EpicMembershipKofi = EpicMembershipKofi;

})(jQuery);
