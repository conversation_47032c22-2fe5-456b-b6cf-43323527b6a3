<?php
/**
 * Ko-fi Instructions Shortcode and Widget
 */

if (!defined('ABSPATH')) {
    exit;
}

class Epic_Membership_Kofi_Instructions {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        add_shortcode('kofi_instructions', array($this, 'shortcode_kofi_instructions'));
        add_shortcode('kofi_quick_guide', array($this, 'shortcode_quick_guide'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_styles'));
    }
    
    /**
     * Enqueue styles
     */
    public function enqueue_styles() {
        wp_enqueue_style(
            'epic-membership-kofi-instructions',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/kofi-instructions.css',
            array(),
            '1.0.0'
        );
    }
    
    /**
     * Full Ko-fi instructions shortcode
     * Usage: [kofi_instructions]
     */
    public function shortcode_kofi_instructions($atts) {
        $atts = shortcode_atts(array(
            'show_pricing' => 'true',
            'show_faq' => 'true',
            'contact_email' => get_option('admin_email')
        ), $atts);
        
        ob_start();
        ?>
        <div class="epic-kofi-instructions-container">
            <div class="kofi-instructions-header">
                <div class="kofi-icon">☕</div>
                <h2>Ko-fi Payment Instructions</h2>
                <p>Follow these simple steps to upgrade your membership using Ko-fi</p>
            </div>
            
            <?php if ($atts['show_pricing'] === 'true'): ?>
            <div class="kofi-pricing-section">
                <h3>Membership Tiers & Pricing</h3>
                <?php echo $this->get_pricing_table(); ?>
            </div>
            <?php endif; ?>
            
            <div class="kofi-steps-section">
                <h3>Payment Steps</h3>
                <div class="kofi-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Click Ko-fi Button</h4>
                        <p>Choose your desired membership tier and click the "Support via Ko-fi" button.</p>
                    </div>
                </div>
                
                <div class="kofi-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Adjust Amount</h4>
                        <p><strong>Important:</strong> Ko-fi shows $2 by default. Change it to your tier amount (e.g., $5 for Premium).</p>
                    </div>
                </div>
                
                <div class="kofi-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Use Same Email</h4>
                        <p>Make sure to use the same email address as your account on this site.</p>
                    </div>
                </div>
                
                <div class="kofi-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Complete Payment</h4>
                        <p>Finish the payment on Ko-fi. Your membership will activate automatically!</p>
                    </div>
                </div>
            </div>
            
            <div class="kofi-important-notice">
                <div class="notice-icon">⚠️</div>
                <div class="notice-content">
                    <h4>Why Ko-fi Shows $2</h4>
                    <p>Ko-fi has a $2 minimum donation and shows this by default. The amount in the URL only suggests the value - you must manually adjust it to match your chosen membership tier.</p>
                </div>
            </div>
            
            <?php if ($atts['show_faq'] === 'true'): ?>
            <div class="kofi-faq-section">
                <h3>Common Questions</h3>
                <?php echo $this->get_faq_content(); ?>
            </div>
            <?php endif; ?>
            
            <div class="kofi-support-section">
                <p>Need help? <a href="mailto:<?php echo esc_attr($atts['contact_email']); ?>">Contact Support</a></p>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Quick guide shortcode
     * Usage: [kofi_quick_guide]
     */
    public function shortcode_quick_guide($atts) {
        $atts = shortcode_atts(array(
            'style' => 'compact'
        ), $atts);
        
        ob_start();
        ?>
        <div class="epic-kofi-quick-guide <?php echo esc_attr($atts['style']); ?>">
            <div class="quick-guide-header">
                <span class="kofi-icon">☕</span>
                <strong>Ko-fi Payment Guide</strong>
            </div>
            <ol class="quick-steps">
                <li>Click "Support via Ko-fi" button</li>
                <li><strong>Change amount</strong> from $2 to your tier price</li>
                <li>Use the <strong>same email</strong> as this account</li>
                <li>Complete payment - membership activates automatically!</li>
            </ol>
            <div class="quick-note">
                <small><strong>Note:</strong> Ko-fi shows $2 by default - you must adjust it manually.</small>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get pricing table HTML
     */
    private function get_pricing_table() {
        global $wpdb;
        
        $database = new Epic_Membership_Database();
        $tiers_table = $database->get_table('tiers');
        
        $tiers = $wpdb->get_results("SELECT * FROM $tiers_table WHERE is_active = 1 AND price > 0 ORDER BY price ASC");
        
        if (empty($tiers)) {
            return '<p>No paid tiers available.</p>';
        }
        
        ob_start();
        ?>
        <div class="kofi-pricing-table">
            <?php foreach ($tiers as $tier): ?>
            <div class="pricing-tier">
                <div class="tier-name"><?php echo esc_html($tier->name); ?></div>
                <div class="tier-price">$<?php echo number_format($tier->price, 2); ?></div>
                <div class="tier-description"><?php echo esc_html($tier->description); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Get FAQ content
     */
    private function get_faq_content() {
        $faqs = array(
            array(
                'question' => 'Why does Ko-fi show $2 instead of my tier price?',
                'answer' => 'Ko-fi has a $2 minimum donation and shows this by default. You need to manually adjust the amount to match your chosen membership tier.'
            ),
            array(
                'question' => 'What if I use a different email address?',
                'answer' => 'You must use the same email address for both Ko-fi and your account here. Otherwise, the system cannot match your payment to your account.'
            ),
            array(
                'question' => 'How long does activation take?',
                'answer' => 'Membership activation is usually instant but can take up to 5 minutes. If it takes longer, please contact support.'
            ),
            array(
                'question' => 'Can I pay more than the tier amount?',
                'answer' => 'Yes! If you pay more than a tier amount, you\'ll be upgraded to the highest tier that your payment covers.'
            )
        );
        
        ob_start();
        ?>
        <div class="kofi-faq-list">
            <?php foreach ($faqs as $faq): ?>
            <div class="faq-item">
                <div class="faq-question"><?php echo esc_html($faq['question']); ?></div>
                <div class="faq-answer"><?php echo esc_html($faq['answer']); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
}

// Initialize the class
new Epic_Membership_Kofi_Instructions();
