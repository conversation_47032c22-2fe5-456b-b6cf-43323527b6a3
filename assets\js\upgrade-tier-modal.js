/**
 * Epic Membership Upgrade Tier Modal
 * Clean implementation following Ko-fi modal patterns
 */

(function($) {
    'use strict';

    // Ensure EpicMembership object exists
    window.EpicMembership = window.EpicMembership || {};

    /**
     * Upgrade Tier Modal Handler
     */
    var UpgradeTierModal = {
        
        /**
         * Initialize the upgrade tier modal system
         */
        init: function() {
            console.log('Epic Membership: Starting upgrade tier modal initialization...');

            // Check if required dependencies are available (using dedicated object like Ko-fi)
            if (typeof window.epicMembershipUpgrade === 'undefined') {
                console.error('Epic Membership: epicMembershipUpgrade object not found! Modal functionality will not work.');
                console.log('Available objects:', Object.keys(window).filter(key => key.includes('epic')));
                return false;
            }

            console.log('Epic Membership: epicMembershipUpgrade object found:', window.epicMembershipUpgrade);

            if (!window.epicMembershipUpgrade.ajaxUrl) {
                console.error('Epic Membership: AJAX URL not available! Modal functionality will not work.');
                return false;
            }

            if (!window.epicMembershipUpgrade.nonce) {
                console.error('Epic Membership: Nonce not available! Modal functionality will not work.');
                return false;
            }

            console.log('Epic Membership: All dependencies available, binding events...');
            this.bindEvents();
            console.log('Epic Membership: Upgrade Tier Modal initialized successfully');
            return true;
        },

        /**
         * Bind upgrade button events (following Ko-fi pattern exactly)
         */
        bindEvents: function() {
            var self = this;

            console.log('Epic Membership: Binding upgrade button events');

            // Remove any existing handlers first to prevent conflicts
            $(document).off('click.upgrade', '.epic-membership-upgrade-button');

            // Also remove any non-namespaced handlers that might conflict
            $('.epic-membership-upgrade-button').off('click');

            // Handle upgrade button clicks with namespaced event (like Ko-fi)
            $(document).on('click.upgrade', '.epic-membership-upgrade-button', function(e) {
                console.log('Epic Membership: Upgrade button event triggered!');

                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation(); // Stop other handlers from running

                var $button = $(this);
                var tierId = $button.data('tier-id');
                var tierName = $button.data('tier-name') || 'Premium';

                console.log('Upgrade button clicked, tier ID:', tierId, 'tier name:', tierName);

                if (tierId) {
                    console.log('Epic Membership: Calling showUpgradeModal');
                    self.showUpgradeModal(tierId, tierName);
                } else {
                    console.error('Epic Membership: No tier ID found!');
                }

                return false; // Additional prevention
            });

            console.log('Epic Membership: Event binding complete');
        },

        /**
         * Show upgrade confirmation modal
         * Following Ko-fi modal structure and patterns
         */
        showUpgradeModal: function(tierId, tierName) {
            var self = this;
            
            console.log('Epic Membership: Showing upgrade modal for tier:', tierId, tierName);
            
            // Get tier information first
            this.getTierInfo(tierId).then(function(tierData) {
                self.createModal(tierData);
            }).catch(function(error) {
                console.error('Epic Membership: Failed to load tier information:', error);
                self.showError('Failed to load tier information. Please try again.');
            });
        },

        /**
         * Get tier information via AJAX (using dedicated object like Ko-fi)
         */
        getTierInfo: function(tierId) {
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: epicMembershipUpgrade.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'epic_membership_get_tier_info',
                        tier_id: tierId,
                        nonce: epicMembershipUpgrade.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            resolve(response.data);
                        } else {
                            reject(new Error(response.data || 'Failed to get tier information'));
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(new Error('AJAX request failed: ' + error));
                    }
                });
            });
        },

        /**
         * Create and display the modal
         * Following Ko-fi modal HTML structure exactly
         */
        createModal: function(tierData) {
            var self = this;
            
            // Get localized strings (using dedicated object like Ko-fi)
            var strings = epicMembershipUpgrade.strings || {};
            var upgradeTitle = strings.upgradeConfirmTitle || 'Confirm Membership Upgrade';
            var confirmText = strings.confirmUpgrade || 'Confirm Upgrade';
            var cancelText = strings.cancel || 'Cancel';
            
            // Create modal HTML following Ko-fi pattern exactly
            var modalHtml = `
                <div id="epic-membership-upgrade-modal" class="epic-membership-modal-overlay">
                    <div class="epic-membership-modal">
                        <div class="epic-membership-modal-header">
                            <h3>${upgradeTitle}</h3>
                            <button type="button" class="epic-membership-modal-close">&times;</button>
                        </div>
                        <div class="epic-membership-modal-content">
                            <div class="epic-membership-upgrade-instructions">
                                <div class="upgrade-tier-info">
                                    <h4>Upgrading to: ${tierData.name} Membership</h4>
                                    ${tierData.price > 0 ? `<p class="tier-price">Price: <strong>$${tierData.price}</strong></p>` : '<p class="tier-price"><strong>Free Tier</strong></p>'}
                                </div>
                                
                                ${tierData.description ? `<div class="tier-description"><p>${tierData.description}</p></div>` : ''}
                                
                                <div class="upgrade-confirmation">
                                    <p><strong>Are you sure you want to upgrade to ${tierData.name} membership?</strong></p>
                                    ${tierData.price > 0 ? '<p>You will be redirected to complete payment after confirmation.</p>' : '<p>This free tier will be activated immediately.</p>'}
                                </div>

                                <div class="epic-membership-loading" style="display: none;">
                                    <div class="epic-membership-spinner"></div>
                                    <p>Processing upgrade...</p>
                                </div>

                                <div class="modal-actions">
                                    <button type="button" class="upgrade-confirm-btn" data-tier-id="${tierData.id}">
                                        ${confirmText}
                                    </button>
                                    <button type="button" class="upgrade-cancel-btn">${cancelText}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal
            $('#epic-membership-upgrade-modal').remove();

            // Add modal to page
            $('body').append(modalHtml);

            // Show modal with fade effect (Ko-fi pattern)
            $('#epic-membership-upgrade-modal').fadeIn(300);

            // Bind modal events
            this.bindModalEvents(tierData);
        },

        /**
         * Bind modal events following Ko-fi patterns
         */
        bindModalEvents: function(tierData) {
            var self = this;
            var $modal = $('#epic-membership-upgrade-modal');

            // Close modal events
            $modal.find('.epic-membership-modal-close, .upgrade-cancel-btn').on('click', function(e) {
                e.preventDefault();
                self.closeModal();
            });

            // Close on overlay click
            $modal.on('click', function(e) {
                if (e.target === this) {
                    self.closeModal();
                }
            });

            // Close on escape key
            $(document).on('keydown.upgrade-modal', function(e) {
                if (e.keyCode === 27) {
                    self.closeModal();
                }
            });

            // Handle upgrade confirmation
            $modal.find('.upgrade-confirm-btn').on('click', function(e) {
                e.preventDefault();
                self.processUpgrade(tierData);
            });
        },

        /**
         * Process the upgrade based on tier type
         */
        processUpgrade: function(tierData) {
            var self = this;
            
            // Show loading state
            this.showLoading();
            
            if (parseFloat(tierData.price) === 0) {
                // Free tier - activate directly
                this.activateFreeTier(tierData.id);
            } else {
                // Paid tier - redirect to payment
                this.redirectToPayment(tierData);
            }
        },

        /**
         * Show loading state in modal
         */
        showLoading: function() {
            var $modal = $('#epic-membership-upgrade-modal');
            $modal.find('.epic-membership-upgrade-instructions > *:not(.epic-membership-loading)').hide();
            $modal.find('.epic-membership-loading').show();
        },

        /**
         * Activate free tier membership (using dedicated object like Ko-fi)
         */
        activateFreeTier: function(tierId) {
            var self = this;

            $.ajax({
                url: epicMembershipUpgrade.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'epic_membership_activate_free_tier',
                    tier_id: tierId,
                    nonce: epicMembershipUpgrade.nonce
                },
                success: function(response) {
                    if (response.success) {
                        self.closeModal();
                        self.showSuccess(response.data.message || 'Free tier activated successfully!');

                        // Refresh page after short delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        self.showError(response.data || 'Failed to activate free tier');
                        self.hideLoading();
                    }
                },
                error: function(xhr, status, error) {
                    self.showError('Failed to activate free tier: ' + error);
                    self.hideLoading();
                }
            });
        },

        /**
         * Redirect to payment for paid tiers
         */
        redirectToPayment: function(tierData) {
            var self = this;
            
            // Close the upgrade modal
            this.closeModal();
            
            // Check if PayPal integration is available
            if (typeof EpicMembershipPayPal !== 'undefined') {
                // Use PayPal payment
                EpicMembershipPayPal.showPaymentOptions(tierData.id);
            } else {
                // Fallback error
                this.showError('Payment system not available. Please contact support.');
            }
        },

        /**
         * Hide loading state
         */
        hideLoading: function() {
            var $modal = $('#epic-membership-upgrade-modal');
            $modal.find('.epic-membership-loading').hide();
            $modal.find('.epic-membership-upgrade-instructions > *:not(.epic-membership-loading)').show();
        },

        /**
         * Close modal following Ko-fi pattern
         */
        closeModal: function() {
            // Remove escape key handler
            $(document).off('keydown.upgrade-modal');
            
            // Fade out and remove
            $('#epic-membership-upgrade-modal').fadeOut(300, function() {
                $(this).remove();
            });
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showMessage(message, 'success');
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.showMessage(message, 'error');
        },

        /**
         * Show message notification
         */
        showMessage: function(message, type) {
            // Create message element
            var messageClass = 'epic-membership-message epic-membership-message-' + type;
            var $message = $('<div class="' + messageClass + '">' + message + '</div>');
            
            // Add to page
            $('body').append($message);
            
            // Show with animation
            $message.fadeIn(300);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut(300, function() {
                    $message.remove();
                });
            }, 5000);
        }
    };

    // Initialize when document is ready (following Ko-fi pattern exactly)
    $(document).ready(function() {
        console.log('Epic Membership: Document ready, initializing upgrade tier modal...');

        // Wait a bit to ensure other scripts are loaded, then initialize
        setTimeout(function() {
            console.log('Epic Membership: Starting delayed initialization...');
            UpgradeTierModal.init();
        }, 100);
    });

    // Make available globally
    window.EpicMembershipUpgradeTierModal = UpgradeTierModal;

    console.log('Epic Membership: Upgrade Tier Modal script loaded and available globally');

})(jQuery);
