/**

 * PayPal Integration JavaScript for Epic Membership Plugin

 */



(function($) {

    'use strict';



    var EpicMembershipPayPal = {

        

        /**

         * Initialize PayPal integration

         */

        init: function() {

            this.bindEvents();

            this.initPayPalButtons();

        },

        

        /**

         * Bind events

         */

        bindEvents: function() {

            var self = this;



            // Handle PayPal payment button clicks (only buttons with paypal-button class)

            $(document).on('click', '.epic-membership-paypal-button', function(e) {

                e.preventDefault();

                e.stopPropagation(); // Prevent event bubbling

                var $button = $(this);

                var tierId = $button.data('tier-id');



                if (tierId) {

                    self.showPaymentOptions(tierId, $button);

                }

            });

        },

        

        /**

         * Initialize PayPal buttons

         */

        initPayPalButtons: function() {

            var self = this;

            

            // Check if PayPal SDK is loaded

            if (typeof paypal === 'undefined') {

                console.warn('PayPal SDK not loaded');

                return;

            }

            

            // Initialize PayPal buttons for each tier

            $('.epic-membership-paypal-container').each(function() {

                var $container = $(this);

                var tierId = $container.data('tier-id');

                

                if (!tierId) {

                    return;

                }

                

                self.renderPayPalButton($container, tierId);

            });

        },

        

        /**

         * Render PayPal button

         */

        renderPayPalButton: function($container, tierId) {

            var self = this;

            

            paypal.Buttons({

                createOrder: function(data, actions) {

                    return self.createPayPalOrder(tierId);

                },

                

                onApprove: function(data, actions) {

                    return self.capturePayPalOrder(data.orderID);

                },

                

                onError: function(err) {

                    console.error('PayPal error:', err);

                    self.showError(epicMembershipPayPal.strings.error);

                },

                

                onCancel: function(data) {

                    self.showMessage('Payment cancelled', 'warning');

                },

                

                style: {

                    layout: 'vertical',

                    color: 'blue',

                    shape: 'rect',

                    label: 'paypal'

                }

            }).render($container[0]);

        },

        

        /**

         * Create PayPal order

         */

        createPayPalOrder: function(tierId) {

            var self = this;

            

            return new Promise(function(resolve, reject) {

                $.ajax({

                    url: epicMembershipPayPal.ajaxUrl,

                    type: 'POST',

                    data: {

                        action: 'epic_membership_create_paypal_order',

                        tier_id: tierId,

                        nonce: epicMembershipPayPal.nonce

                    },

                    success: function(response) {

                        if (response.success) {

                            resolve(response.data.order_id);

                        } else {

                            reject(new Error(response.data || 'Failed to create order'));

                        }

                    },

                    error: function() {

                        reject(new Error('Network error'));

                    }

                });

            });

        },

        

        /**

         * Capture PayPal order

         */

        capturePayPalOrder: function(orderId) {

            var self = this;

            

            return new Promise(function(resolve, reject) {

                self.showMessage(epicMembershipPayPal.strings.processing, 'info');

                

                $.ajax({

                    url: epicMembershipPayPal.ajaxUrl,

                    type: 'POST',

                    data: {

                        action: 'epic_membership_capture_paypal_order',

                        order_id: orderId,

                        nonce: epicMembershipPayPal.nonce

                    },

                    success: function(response) {

                        if (response.success) {

                            self.showMessage(epicMembershipPayPal.strings.success, 'success');

                            

                            // Redirect to success page or refresh dashboard

                            setTimeout(function() {

                                if (typeof epicMembership !== 'undefined' && epicMembership.refreshMembershipStatus) {

                                    epicMembership.refreshMembershipStatus();

                                } else {

                                    window.location.reload();

                                }

                            }, 2000);

                            

                            resolve(response.data);

                        } else {

                            reject(new Error(response.data || 'Payment capture failed'));

                        }

                    },

                    error: function() {

                        reject(new Error('Network error during payment capture'));

                    }

                });

            });

        },

        

        /**

         * Handle PayPal payment button click

         */

        handlePayPalPayment: function($button) {

            var tierId = $button.data('tier-id');

            

            if (!tierId) {

                this.showError('Invalid tier selection');

                return;

            }

            

            // Show PayPal container if hidden

            var $container = $('.epic-membership-paypal-container[data-tier-id="' + tierId + '"]');

            if ($container.length && $container.is(':hidden')) {

                $container.slideDown();

            }

        },

        

        /**

         * Show payment options modal

         */

        showPaymentOptions: function(tierId, $triggerButton) {

            this.showEnhancedPaymentModal(tierId);

        },

        

        /**

         * Create payment modal HTML

         */

        createPaymentModal: function(tierId) {

            return `

                <div class="epic-membership-payment-modal">

                    <div class="modal-overlay"></div>

                    <div class="modal-content">

                        <div class="modal-header">

                            <h3>Choose Payment Method</h3>

                            <button class="modal-close">&times;</button>

                        </div>

                        <div class="modal-body">

                            <div class="payment-methods">

                                <div class="payment-method paypal-method">

                                    <h4>Pay with PayPal</h4>

                                    <p>Secure payment with PayPal. You can use your PayPal account or credit card.</p>

                                    <div class="epic-membership-paypal-container" data-tier-id="${tierId}"></div>

                                </div>

                            </div>

                        </div>

                    </div>

                </div>

            `;

        },

        

        /**

         * Show message

         */

        showMessage: function(message, type) {

            type = type || 'info';

            

            // Remove existing messages

            $('.epic-membership-message').remove();

            

            var messageHtml = `

                <div class="epic-membership-message epic-membership-message-${type}">

                    <p>${message}</p>

                </div>

            `;

            

            // Add message to top of dashboard or body

            var $target = $('.epic-membership-dashboard, body').first();

            $target.prepend(messageHtml);

            

            // Auto-hide after 5 seconds for non-error messages

            if (type !== 'error') {

                setTimeout(function() {

                    $('.epic-membership-message').fadeOut();

                }, 5000);

            }

        },

        

        /**

         * Show error message

         */

        showError: function(message) {

            this.showMessage(message, 'error');

        },

        

        /**

         * Get tier information

         */

        getTierInfo: function(tierId) {

            var self = this;



            return new Promise(function(resolve, reject) {

                $.ajax({

                    url: epicMembershipPayPal.ajaxUrl,

                    type: 'POST',

                    data: {

                        action: 'epic_membership_get_tier_info',

                        tier_id: tierId,

                        nonce: epicMembershipPayPal.nonce

                    },

                    success: function(response) {

                        if (response.success) {

                            resolve(response.data);

                        } else {

                            reject(new Error(response.data || 'Failed to get tier info'));

                        }

                    },

                    error: function() {

                        reject(new Error('Network error'));

                    }

                });

            });

        },



        /**

         * Show payment confirmation

         */

        showPaymentConfirmation: function(tierData) {

            var confirmationHtml = `

                <div class="epic-membership-payment-confirmation">

                    <div class="confirmation-header">

                        <h3>Confirm Your Purchase</h3>

                    </div>

                    <div class="confirmation-details">

                        <div class="tier-info">

                            <h4>${tierData.name} Membership</h4>

                            <p>${tierData.description}</p>

                            <div class="price-info">

                                <span class="price">${tierData.currency} ${tierData.price}</span>

                                ${tierData.duration_days ? `<span class="duration">for ${tierData.duration_days} days</span>` : ''}

                            </div>

                        </div>

                        <div class="benefits-preview">

                            <h5>What you'll get:</h5>

                            <ul>

                                ${tierData.capabilities.map(cap => `<li>${this.formatCapability(cap)}</li>`).join('')}

                            </ul>

                        </div>

                    </div>

                </div>

            `;



            return confirmationHtml;

        },



        /**

         * Format capability for display

         */

        formatCapability: function(capability) {

            var capabilities = {

                'read_free_content': 'Access to free content',

                'read_premium_content': 'Access to premium content',

                'read_vip_content': 'Access to VIP content',

                'ad_free_experience': 'Ad-free browsing experience',

                'priority_support': 'Priority customer support'

            };



            return capabilities[capability] || capability.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        },



        /**

         * Enhanced payment options modal

         */

        showEnhancedPaymentModal: function(tierId) {

            var self = this;



            // Get tier information first

            this.getTierInfo(tierId).then(function(tierData) {

                var modalHtml = `

                    <div class="epic-membership-payment-modal enhanced">

                        <div class="modal-overlay"></div>

                        <div class="modal-content">

                            <div class="modal-header">

                                <h3>Complete Your Purchase</h3>

                                <button class="modal-close">&times;</button>

                            </div>

                            <div class="modal-body">

                                ${self.showPaymentConfirmation(tierData)}

                                <div class="payment-methods">

                                    <div class="payment-method paypal-method">

                                        <h4>Pay with PayPal</h4>

                                        <p>Secure payment with PayPal. You can use your PayPal account or credit card.</p>

                                        <div class="epic-membership-paypal-container" data-tier-id="${tierId}"></div>

                                    </div>

                                </div>

                            </div>

                        </div>

                    </div>

                `;



                // Remove existing modal

                $('.epic-membership-payment-modal').remove();



                // Add modal to page

                $('body').append(modalHtml);



                var $modal = $('.epic-membership-payment-modal');



                // Show modal

                $modal.fadeIn();



                // Initialize PayPal button in modal

                var $paypalContainer = $modal.find('.epic-membership-paypal-container');

                if ($paypalContainer.length) {

                    self.renderPayPalButton($paypalContainer, tierId);

                }



                // Bind close events

                $modal.find('.modal-close, .modal-overlay').on('click', function() {

                    $modal.fadeOut(function() {

                        $modal.remove();

                    });

                });



            }).catch(function(error) {

                self.showError('Failed to load tier information: ' + error.message);

            });

        }

    };

    

    // Initialize when document is ready

    $(document).ready(function() {

        EpicMembershipPayPal.init();

    });

    

    // Make available globally

    window.EpicMembershipPayPal = EpicMembershipPayPal;

    

})(jQuery);

