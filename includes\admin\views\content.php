<?php

/**

 * Admin page for managing protected content

 */



if (!defined('ABSPATH')) {

    exit;

}



// Initialize database

$database = new Epic_Membership_Database();



// Get protected content

global $wpdb;

$content_access_table = $database->get_table('content_access');



$protected_content = $wpdb->get_results("

    SELECT ca.*, p.post_title, p.post_type, p.post_status, t.name as tier_name, t.level as tier_level

    FROM $content_access_table ca

    LEFT JOIN {$wpdb->posts} p ON ca.post_id = p.ID

    LEFT JOIN {$database->get_table('tiers')} t ON ca.required_tier_id = t.id

    WHERE ca.is_active = 1

    ORDER BY ca.created_at DESC

");



// Process allowed tiers for scheduled content

foreach ($protected_content as $content) {

    if ($content->access_type === 'scheduled' && $content->allowed_tier_ids) {

        $allowed_tier_ids = json_decode($content->allowed_tier_ids, true);

        if (is_array($allowed_tier_ids) && !empty($allowed_tier_ids)) {

            $placeholders = implode(',', array_fill(0, count($allowed_tier_ids), '%d'));

            $allowed_tiers = $wpdb->get_results($wpdb->prepare("

                SELECT name, level

                FROM {$database->get_table('tiers')}

                WHERE id IN ($placeholders)

                ORDER BY level ASC

            ", $allowed_tier_ids));

            $content->allowed_tiers = $allowed_tiers;

        }

    }

}



?>



<div class="wrap">

    <h1><?php _e('Protected Content', 'epic-membership'); ?></h1>

    

    <div class="epic-membership-content-overview">

        <div class="content-stats">

            <div class="stat-box">

                <div class="stat-number"><?php echo count($protected_content); ?></div>

                <div class="stat-label"><?php _e('Protected Items', 'epic-membership'); ?></div>

            </div>

            <div class="stat-box">

                <div class="stat-number">

                    <?php echo count(array_filter($protected_content, function($item) { 

                        return $item->access_type === 'members_only'; 

                    })); ?>

                </div>

                <div class="stat-label"><?php _e('Members Only', 'epic-membership'); ?></div>

            </div>

            <div class="stat-box">

                <div class="stat-number">

                    <?php echo count(array_filter($protected_content, function($item) { 

                        return $item->access_type === 'scheduled'; 

                    })); ?>

                </div>

                <div class="stat-label"><?php _e('Scheduled', 'epic-membership'); ?></div>

            </div>

        </div>

    </div>



    <?php if (empty($protected_content)): ?>

        <div class="notice notice-info">

            <p><?php _e('No protected content found. Start protecting your content by editing posts and pages.', 'epic-membership'); ?></p>

            <p>

                <a href="<?php echo admin_url('post-new.php'); ?>" class="button button-primary">

                    <?php _e('Create New Post', 'epic-membership'); ?>

                </a>

                <a href="<?php echo admin_url('post-new.php?post_type=page'); ?>" class="button">

                    <?php _e('Create New Page', 'epic-membership'); ?>

                </a>

            </p>

        </div>

    <?php else: ?>

        <table class="wp-list-table widefat fixed striped">

            <thead>

                <tr>

                    <th scope="col" class="manage-column column-title column-primary">

                        <?php _e('Content', 'epic-membership'); ?>

                    </th>

                    <th scope="col" class="manage-column column-type">

                        <?php _e('Type', 'epic-membership'); ?>

                    </th>

                    <th scope="col" class="manage-column column-access">

                        <?php _e('Access Type', 'epic-membership'); ?>

                    </th>

                    <th scope="col" class="manage-column column-requirement">

                        <?php _e('Requirement', 'epic-membership'); ?>

                    </th>

                    <th scope="col" class="manage-column column-status">

                        <?php _e('Status', 'epic-membership'); ?>

                    </th>

                </tr>

            </thead>

            <tbody>

                <?php foreach ($protected_content as $content): ?>

                    <tr>

                        <td class="column-title column-primary">

                            <strong>

                                <a href="<?php echo get_edit_post_link($content->post_id); ?>">

                                    <?php echo esc_html($content->post_title ?: __('(No title)', 'epic-membership')); ?>

                                </a>

                            </strong>

                            <div class="row-actions">

                                <span class="edit">

                                    <a href="<?php echo get_edit_post_link($content->post_id); ?>">

                                        <?php _e('Edit', 'epic-membership'); ?>

                                    </a>

                                </span>

                                | <span class="view">

                                    <a href="<?php echo get_permalink($content->post_id); ?>" target="_blank">

                                        <?php _e('View', 'epic-membership'); ?>

                                    </a>

                                </span>

                            </div>

                            <button type="button" class="toggle-row">

                                <span class="screen-reader-text"><?php _e('Show more details', 'epic-membership'); ?></span>

                            </button>

                        </td>

                        <td class="column-type" data-colname="<?php esc_attr_e('Type', 'epic-membership'); ?>">

                            <span class="post-type-badge <?php echo esc_attr($content->post_type); ?>">

                                <?php echo esc_html(get_post_type_object($content->post_type)->labels->singular_name); ?>

                            </span>

                        </td>

                        <td class="column-access" data-colname="<?php esc_attr_e('Access Type', 'epic-membership'); ?>">

                            <span class="access-type-badge <?php echo esc_attr($content->access_type); ?>">

                                <?php 

                                switch ($content->access_type) {

                                    case 'members_only':

                                        _e('Members Only', 'epic-membership');

                                        break;

                                    case 'scheduled':

                                        _e('Scheduled', 'epic-membership');

                                        break;

                                    default:

                                        _e('Public', 'epic-membership');

                                }

                                ?>

                            </span>

                        </td>

                        <td class="column-requirement" data-colname="<?php esc_attr_e('Requirement', 'epic-membership'); ?>">

                            <?php if ($content->access_type === 'members_only' && $content->tier_name): ?>

                                <span class="tier-requirement">

                                    <?php echo esc_html($content->tier_name); ?>

                                    <small>(Level <?php echo esc_html($content->tier_level); ?>)</small>

                                </span>

                            <?php elseif ($content->access_type === 'scheduled' && $content->scheduled_release): ?>

                                <div class="schedule-requirement">

                                    <div class="schedule-date">

                                        <strong><?php _e('Release:', 'epic-membership'); ?></strong>

                                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($content->scheduled_release))); ?>

                                    </div>

                                    <?php if (!empty($content->allowed_tiers)): ?>

                                        <div class="early-access-tiers">

                                            <strong><?php _e('Early Access:', 'epic-membership'); ?></strong>

                                            <?php

                                            $tier_names = array_map(function($tier) {

                                                return $tier->name . ' (L' . $tier->level . ')';

                                            }, $content->allowed_tiers);

                                            echo esc_html(implode(', ', $tier_names));

                                            ?>

                                        </div>

                                    <?php endif; ?>

                                </div>

                            <?php else: ?>

                                <span class="no-requirement">—</span>

                            <?php endif; ?>

                        </td>

                        <td class="column-status" data-colname="<?php esc_attr_e('Status', 'epic-membership'); ?>">

                            <?php if ($content->post_status === 'publish'): ?>

                                <?php if ($content->access_type === 'scheduled'): ?>

                                    <?php 

                                    $is_released = strtotime($content->scheduled_release) <= time();

                                    ?>

                                    <span class="status-badge <?php echo $is_released ? 'released' : 'scheduled'; ?>">

                                        <?php echo $is_released ? __('Released', 'epic-membership') : __('Scheduled', 'epic-membership'); ?>

                                    </span>

                                <?php else: ?>

                                    <span class="status-badge active"><?php _e('Active', 'epic-membership'); ?></span>

                                <?php endif; ?>

                            <?php else: ?>

                                <span class="status-badge draft"><?php echo esc_html(get_post_status_object($content->post_status)->label); ?></span>

                            <?php endif; ?>

                        </td>

                    </tr>

                <?php endforeach; ?>

            </tbody>

        </table>

    <?php endif; ?>

    

    <div class="epic-membership-content-help">

        <h2><?php _e('How to Protect Content', 'epic-membership'); ?></h2>

        <ol>

            <li><?php _e('Edit any post or page', 'epic-membership'); ?></li>

            <li><?php _e('Find the "Membership Access Control" meta box in the sidebar', 'epic-membership'); ?></li>

            <li><?php _e('Choose your access type:', 'epic-membership'); ?>

                <ul>

                    <li><strong><?php _e('Public:', 'epic-membership'); ?></strong> <?php _e('Everyone can access', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Members Only:', 'epic-membership'); ?></strong> <?php _e('Requires specific membership tier', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Scheduled:', 'epic-membership'); ?></strong> <?php _e('Released at specific date/time', 'epic-membership'); ?></li>

                </ul>

            </li>

            <li><?php _e('Add teaser content to show non-members what they\'re missing', 'epic-membership'); ?></li>

            <li><?php _e('Save your post or page', 'epic-membership'); ?></li>

        </ol>

    </div>

</div>



<style>

.epic-membership-content-overview {

    margin: 20px 0;

}



.content-stats {

    display: flex;

    gap: 20px;

    margin-bottom: 30px;

}



.stat-box {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    text-align: center;

    flex: 1;

    box-shadow: 0 1px 1px rgba(0,0,0,0.04);

}



.stat-number {

    font-size: 32px;

    font-weight: bold;

    color: #1d2327;

    line-height: 1;

}



.stat-label {

    font-size: 13px;

    color: #646970;

    margin-top: 5px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.post-type-badge {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

    background: #f0f0f1;

    color: #646970;

}



.post-type-badge.post {

    background: #d4edda;

    color: #155724;

}



.post-type-badge.page {

    background: #d1ecf1;

    color: #0c5460;

}



.access-type-badge {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

}



.access-type-badge.members_only {

    background: #fff3cd;

    color: #856404;

}



.access-type-badge.scheduled {

    background: #cce5ff;

    color: #004085;

}



.tier-requirement {

    font-weight: 600;

}



.tier-requirement small {

    color: #646970;

    font-weight: normal;

}



.schedule-requirement {

    font-size: 12px;

    color: #646970;

}



.schedule-requirement .schedule-date {

    margin-bottom: 4px;

}



.schedule-requirement .early-access-tiers {

    font-size: 11px;

    color: #0073aa;

    font-style: italic;

}



.no-requirement {

    color: #646970;

}



.status-badge {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

}



.status-badge.active {

    background: #d4edda;

    color: #155724;

}



.status-badge.released {

    background: #d4edda;

    color: #155724;

}



.status-badge.scheduled {

    background: #fff3cd;

    color: #856404;

}



.status-badge.draft {

    background: #f8d7da;

    color: #721c24;

}



.epic-membership-content-help {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    margin-top: 30px;

}



.epic-membership-content-help h2 {

    margin-top: 0;

}



.epic-membership-content-help ul {

    margin-left: 20px;

}



.epic-membership-content-help li {

    margin-bottom: 5px;

}

</style>

