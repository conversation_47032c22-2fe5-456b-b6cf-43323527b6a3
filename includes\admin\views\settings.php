<?php

/**

 * Admin settings page for Epic Membership Plugin

 */



if (!defined('ABSPATH')) {

    exit;

}



// Legacy form processing removed - now handled by Epic_Membership_Settings::handle_settings_save()



// Handle settings update messages

if (isset($_GET['settings-updated']) && $_GET['settings-updated'] === 'true') {

    echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'epic-membership') . '</p></div>';

}

// Handle currency fix messages
if (isset($_GET['currency_fixed']) && $_GET['currency_fixed'] === '1') {
    echo '<div class="notice notice-success is-dismissible"><p>' . __('Currency column fixed successfully! You can now save currency settings for tiers.', 'epic-membership') . '</p></div>';
}

// Check if currency column exists and show warning if not
global $wpdb;
$database = new Epic_Membership_Database();
$table_tiers = $database->get_table('tiers');
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_tiers LIKE 'currency'");

if (empty($column_exists)) {
    $fix_url = wp_nonce_url(
        admin_url('admin.php?page=epic-membership-settings&fix_currency=1'),
        'fix_currency_nonce'
    );
    echo '<div class="notice notice-warning"><p>';
    echo __('Currency column is missing from tiers table. This may cause issues when saving tier currency settings.', 'epic-membership');
    echo ' <a href="' . esc_url($fix_url) . '" class="button button-primary" onclick="return confirm(\'This will add the currency column to the database. Continue?\')">Fix Currency Column</a>';
    echo '</p></div>';
}



if (isset($_GET['settings-error']) && $_GET['settings-error'] === 'true') {

    $errors = get_transient('epic_membership_settings_errors');

    if ($errors) {

        echo '<div class="notice notice-error is-dismissible">';

        echo '<p>' . __('There were errors saving some settings:', 'epic-membership') . '</p>';

        echo '<ul>';

        foreach ($errors as $error) {

            echo '<li>' . esc_html($error) . '</li>';

        }

        echo '</ul>';

        echo '</div>';

        delete_transient('epic_membership_settings_errors');

    } else {

        echo '<div class="notice notice-error is-dismissible"><p>' . __('There was an error saving settings.', 'epic-membership') . '</p></div>';

    }

}



// Get current settings

$current_settings = array(

    'timezone' => get_option('epic_membership_timezone', wp_timezone_string()),

    'ad_integration' => get_option('epic_membership_ad_integration', 'adsense'), // Legacy

    'teaser_length' => get_option('epic_membership_content_teaser_length', 150),

    'enable_logging' => get_option('epic_membership_enable_logging', true),

    'auto_expire_check' => get_option('epic_membership_auto_expire_check', true),

    'adsense_enabled' => get_option('epic_membership_adsense_enabled', false),

    'adsense_head_code' => get_option('epic_membership_adsense_head_code', ''),

    'adsterra_enabled' => get_option('epic_membership_adsterra_enabled', false),

    'adsterra_head_code' => get_option('epic_membership_adsterra_head_code', ''),

    'paypal_enabled' => get_option('epic_membership_paypal_enabled', false),

    'paypal_sandbox_mode' => get_option('epic_membership_paypal_sandbox_mode', true),

    'paypal_client_id' => get_option('epic_membership_paypal_client_id', ''),

    'paypal_client_secret' => get_option('epic_membership_paypal_client_secret', ''),

    'paypal_sandbox_client_id' => get_option('epic_membership_paypal_sandbox_client_id', ''),

    'paypal_sandbox_client_secret' => get_option('epic_membership_paypal_sandbox_client_secret', ''),

    'paypal_webhook_id' => get_option('epic_membership_paypal_webhook_id', ''),

    'paypal_currency' => get_option('epic_membership_paypal_currency', 'USD'),

    'paypal_debug_mode' => get_option('epic_membership_paypal_debug_mode', false),

    'kofi_enabled' => get_option('epic_membership_kofi_enabled', false),

    'kofi_page_url' => get_option('epic_membership_kofi_page_url', ''),

    'kofi_verification_token' => get_option('epic_membership_kofi_verification_token', ''),

    'kofi_currency' => get_option('epic_membership_kofi_currency', 'USD'),

    'kofi_auto_upgrade' => get_option('epic_membership_kofi_auto_upgrade', true),

    'kofi_debug_mode' => get_option('epic_membership_kofi_debug_mode', false),

    'trakteer_enabled' => get_option('epic_membership_trakteer_enabled', false),

    'trakteer_page_url' => get_option('epic_membership_trakteer_page_url', ''),

    'trakteer_verification_token' => get_option('epic_membership_trakteer_verification_token', ''),

    'trakteer_api_key' => get_option('epic_membership_trakteer_api_key', ''),

    'trakteer_currency' => get_option('epic_membership_trakteer_currency', 'IDR'),

    'trakteer_auto_upgrade' => get_option('epic_membership_trakteer_auto_upgrade', true),

    'trakteer_debug_mode' => get_option('epic_membership_trakteer_debug_mode', false),

    'trakteer_sandbox_mode' => get_option('epic_membership_trakteer_sandbox_mode', true),

    'trakteer_webhook_secret' => get_option('epic_membership_trakteer_webhook_secret', ''),

);



?>



<div class="wrap">

    <h1><?php _e('Epic Membership Settings', 'epic-membership'); ?></h1>

    

    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">

        <input type="hidden" name="action" value="epic_membership_save_settings">

        <?php wp_nonce_field('epic_membership_settings'); ?>

        

        <div class="epic-membership-settings-tabs">

            <nav class="nav-tab-wrapper">

                <a href="#general" class="nav-tab nav-tab-active"><?php _e('General', 'epic-membership'); ?></a>

                <a href="#dashboard" class="nav-tab"><?php _e('Dashboard', 'epic-membership'); ?></a>

                <a href="#content" class="nav-tab"><?php _e('Content', 'epic-membership'); ?></a>

                <a href="#ads" class="nav-tab"><?php _e('Advertisements', 'epic-membership'); ?></a>

                <a href="#paypal" class="nav-tab"><?php _e('PayPal', 'epic-membership'); ?></a>

                <a href="#kofi" class="nav-tab"><?php _e('Ko-fi', 'epic-membership'); ?></a>

                <a href="#trakteer" class="nav-tab"><?php _e('Trakteer', 'epic-membership'); ?></a>

                <a href="#advanced" class="nav-tab"><?php _e('Advanced', 'epic-membership'); ?></a>

            </nav>

            

            <!-- General Settings -->

            <div id="general" class="tab-content active">

                <h2><?php _e('General Settings', 'epic-membership'); ?></h2>

                

                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_timezone"><?php _e('Default Timezone', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <select name="epic_membership_timezone" id="epic_membership_timezone" class="regular-text">

                                    <?php

                                    $timezone_handler = new Epic_Membership_Timezone_Handler();

                                    $timezones = $timezone_handler->get_common_timezones();

                                    foreach ($timezones as $value => $label) {

                                        echo '<option value="' . esc_attr($value) . '"' . selected($current_settings['timezone'], $value, false) . '>' . esc_html($label) . '</option>';

                                    }

                                    ?>

                                </select>

                                <p class="description">

                                    <?php _e('Default timezone for scheduled content releases. Users can override this in their profiles.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_enable_logging"><?php _e('Access Logging', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox" 

                                           name="epic_membership_enable_logging" 

                                           id="epic_membership_enable_logging" 

                                           value="1" 

                                           <?php checked($current_settings['enable_logging']); ?>>

                                    <?php _e('Enable access logging for analytics', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Log user access attempts for protected content. Useful for analytics but may impact performance on high-traffic sites.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_auto_expire_check"><?php _e('Automatic Expiration', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox" 

                                           name="epic_membership_auto_expire_check" 

                                           id="epic_membership_auto_expire_check" 

                                           value="1" 

                                           <?php checked($current_settings['auto_expire_check']); ?>>

                                    <?php _e('Automatically check for expired memberships', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Runs hourly to deactivate expired memberships. Disable if you prefer manual management.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>

            </div>



            <!-- Dashboard Settings -->

            <div id="dashboard" class="tab-content">

                <h2><?php _e('Dashboard Settings', 'epic-membership'); ?></h2>



                <?php

                // Get dashboard instance for page info

                $dashboard = new Epic_Membership_User_Dashboard();

                $page_info = $dashboard->get_status_page_info();

                ?>



                <div class="epic-membership-dashboard-status">

                    <h3><?php _e('Membership Status Page', 'epic-membership'); ?></h3>



                    <div class="status-info-card" style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 15px 0;">

                        <?php if ($page_info['exists']): ?>

                            <div style="color: #28a745; margin-bottom: 10px;">

                                <strong>✓ <?php _e('Status Page Active', 'epic-membership'); ?></strong>

                            </div>

                            <p><?php echo wp_kses_post($page_info['message']); ?></p>

                            <div style="margin-top: 15px;">

                                <a href="<?php echo esc_url($page_info['edit_url']); ?>" class="button button-secondary">

                                    <?php _e('Edit Page', 'epic-membership'); ?>

                                </a>

                                <a href="<?php echo esc_url($page_info['url']); ?>" class="button button-secondary" target="_blank">

                                    <?php _e('View Page', 'epic-membership'); ?>

                                </a>

                            </div>

                        <?php else: ?>

                            <div style="color: #dc3545; margin-bottom: 10px;">

                                <strong>⚠ <?php _e('No Status Page Found', 'epic-membership'); ?></strong>

                            </div>

                            <p><?php echo esc_html($page_info['message']); ?></p>

                            <div style="margin-top: 15px;">

                                <button type="button" id="create-status-page" class="button button-primary">

                                    <?php _e('Create Status Page', 'epic-membership'); ?>

                                </button>

                            </div>

                        <?php endif; ?>

                    </div>

                </div>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_auto_create_status_page"><?php _e('Auto-Create Status Page', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="checkbox"

                                       id="epic_membership_auto_create_status_page"

                                       name="epic_membership_auto_create_status_page"

                                       value="1"

                                       <?php checked(get_option('epic_membership_auto_create_status_page', true)); ?> />

                                <label for="epic_membership_auto_create_status_page">

                                    <?php _e('Automatically create membership status page if it doesn\'t exist', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('When enabled, the plugin will automatically create a membership status page at /membership-status/ if one doesn\'t exist.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_dashboard_require_login"><?php _e('Require Login', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="checkbox"

                                       id="epic_membership_dashboard_require_login"

                                       name="epic_membership_dashboard_require_login"

                                       value="1"

                                       <?php checked(get_option('epic_membership_dashboard_require_login', true)); ?> />

                                <label for="epic_membership_dashboard_require_login">

                                    <?php _e('Redirect non-logged-in users to login page', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('When enabled, users who are not logged in will be redirected to the login page when accessing the membership status page.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_dashboard_menu_position"><?php _e('Menu Position', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <select id="epic_membership_dashboard_menu_position" name="epic_membership_dashboard_menu_position">

                                    <option value="auto" <?php selected(get_option('epic_membership_dashboard_menu_position', 'auto'), 'auto'); ?>>

                                        <?php _e('Auto (Primary Menu)', 'epic-membership'); ?>

                                    </option>

                                    <option value="none" <?php selected(get_option('epic_membership_dashboard_menu_position', 'auto'), 'none'); ?>>

                                        <?php _e('Don\'t Add to Menu', 'epic-membership'); ?>

                                    </option>

                                    <option value="user_profile" <?php selected(get_option('epic_membership_dashboard_menu_position', 'auto'), 'user_profile'); ?>>

                                        <?php _e('User Profile Only', 'epic-membership'); ?>

                                    </option>

                                </select>

                                <p class="description">

                                    <?php _e('Choose where the membership status link should appear. You can also manually add the page to any menu through Appearance > Menus.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <div class="epic-membership-dashboard-help">

                    <h3><?php _e('Dashboard Access Methods', 'epic-membership'); ?></h3>

                    <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #0073aa;">

                        <h4><?php _e('Ways Users Can Access the Membership Dashboard:', 'epic-membership'); ?></h4>

                        <ol>

                            <li><strong><?php _e('Direct Page URL:', 'epic-membership'); ?></strong> /membership-status/</li>

                            <li><strong><?php _e('Shortcode:', 'epic-membership'); ?></strong> [epic_membership_dashboard]</li>

                            <li><strong><?php _e('Navigation Menu:', 'epic-membership'); ?></strong> <?php _e('Automatically added to primary menu for logged-in users', 'epic-membership'); ?></li>

                            <li><strong><?php _e('User Profile:', 'epic-membership'); ?></strong> <?php _e('Appears in WordPress user profile pages', 'epic-membership'); ?></li>

                            <li><strong><?php _e('Manual Menu:', 'epic-membership'); ?></strong> <?php _e('Add the status page to any menu via Appearance > Menus', 'epic-membership'); ?></li>

                        </ol>



                        <h4><?php _e('Shortcode Options:', 'epic-membership'); ?></h4>

                        <ul>

                            <li><code>[epic_membership_dashboard]</code> - <?php _e('Basic dashboard', 'epic-membership'); ?></li>

                            <li><code>[epic_membership_dashboard show_upgrade="true"]</code> - <?php _e('Show upgrade options', 'epic-membership'); ?></li>

                            <li><code>[epic_membership_dashboard show_history="true"]</code> - <?php _e('Show membership history', 'epic-membership'); ?></li>

                            <li><code>[epic_membership_dashboard show_stats="true"]</code> - <?php _e('Show user statistics', 'epic-membership'); ?></li>

                        </ul>

                    </div>

                </div>

            </div>



            <!-- Content Settings -->

            <div id="content" class="tab-content">

                <h2><?php _e('Content Settings', 'epic-membership'); ?></h2>

                

                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_content_teaser_length"><?php _e('Teaser Length', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="number" 

                                       name="epic_membership_content_teaser_length" 

                                       id="epic_membership_content_teaser_length" 

                                       value="<?php echo esc_attr($current_settings['teaser_length']); ?>" 

                                       min="50" 

                                       max="500" 

                                       class="small-text">

                                <span><?php _e('characters', 'epic-membership'); ?></span>

                                <p class="description">

                                    <?php _e('Default length for content teasers when no custom teaser is provided.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>

            </div>

            

            <!-- Advertisement Settings -->

            <div id="ads" class="tab-content">

                <h2><?php _e('Advertisement Settings', 'epic-membership'); ?></h2>

                <p class="description"><?php _e('Configure advertisement providers. Ads are automatically hidden for premium members with ad-free capability.', 'epic-membership'); ?></p>



                <!-- Google AdSense Section -->

                <h3><?php _e('Google AdSense', 'epic-membership'); ?></h3>

                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_adsense_enabled"><?php _e('Enable Google AdSense', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="checkbox" name="epic_membership_adsense_enabled" id="epic_membership_adsense_enabled" value="1" <?php checked($current_settings['adsense_enabled']); ?> />

                                <label for="epic_membership_adsense_enabled"><?php _e('Enable Google AdSense auto ads', 'epic-membership'); ?></label>

                                <p class="description">

                                    <?php _e('Enable Google AdSense auto ads. Google will automatically place ads on your site.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_adsense_head_code"><?php _e('AdSense Auto Ads Code', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <textarea name="epic_membership_adsense_head_code"

                                          id="epic_membership_adsense_head_code"

                                          rows="6"

                                          class="large-text code"

                                          placeholder="<script async src=&quot;https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX&quot; crossorigin=&quot;anonymous&quot;></script>"><?php echo esc_textarea($current_settings['adsense_head_code']); ?></textarea>

                                <p class="description">

                                    <?php _e('Paste your Google AdSense auto ads code here. This code will be automatically inserted in the &lt;head&gt; section.', 'epic-membership'); ?>

                                </p>

                                <p class="description">

                                    <strong><?php _e('How to get your AdSense code:', 'epic-membership'); ?></strong><br>

                                    1. <?php _e('Go to your Google AdSense dashboard', 'epic-membership'); ?><br>

                                    2. <?php _e('Navigate to Ads → Overview → Get code', 'epic-membership'); ?><br>

                                    3. <?php _e('Copy the auto ads code and paste it above', 'epic-membership'); ?>

                                </p>

                                <?php if (!current_user_can('unfiltered_html')): ?>

                                <div class="notice notice-info inline">

                                    <p><strong><?php _e('Security Notice:', 'epic-membership'); ?></strong>

                                    <?php _e('HTML/JavaScript code will be filtered for security. Only standard AdSense tags are allowed. Administrators have full HTML access.', 'epic-membership'); ?></p>

                                </div>

                                <?php endif; ?>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <!-- Adsterra Section -->

                <h3><?php _e('Adsterra', 'epic-membership'); ?></h3>



                <?php

                $ad_unit_manager = new Epic_Membership_Ad_Unit_Manager();

                $existing_ad_units = $ad_unit_manager->get_ad_units();

                $is_migrated = get_option('epic_membership_adsterra_migrated', false);

                ?>



                <?php if (!empty($existing_ad_units)): ?>

                    <div class="notice notice-info inline">

                        <p>

                            <strong><?php _e('Multi-Unit System Active!', 'epic-membership'); ?></strong>

                            <?php printf(

                                __('You have %d ad unit(s) configured. Use the advanced %sAd Units Manager%s for full control over multiple ad placements.', 'epic-membership'),

                                count($existing_ad_units),

                                '<a href="' . admin_url('admin.php?page=epic-membership-ad-units') . '">',

                                '</a>'

                            ); ?>

                        </p>

                        <p>

                            <a href="<?php echo admin_url('admin.php?page=epic-membership-ad-units'); ?>" class="button button-primary">

                                <?php _e('Manage Ad Units', 'epic-membership'); ?>

                            </a>

                        </p>

                    </div>

                <?php endif; ?>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_adsterra_enabled"><?php _e('Enable Legacy Adsterra', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="checkbox" name="epic_membership_adsterra_enabled" id="epic_membership_adsterra_enabled" value="1" <?php checked($current_settings['adsterra_enabled']); ?> />

                                <label for="epic_membership_adsterra_enabled"><?php _e('Enable legacy single Adsterra ad', 'epic-membership'); ?></label>

                                <p class="description">

                                    <?php _e('This is the legacy single ad configuration. For multiple ads with different placements, use the Ad Units Manager above.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_adsterra_head_code"><?php _e('Legacy Adsterra Ad Code', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <textarea name="epic_membership_adsterra_head_code"

                                          id="epic_membership_adsterra_head_code"

                                          rows="6"

                                          class="large-text code"

                                          placeholder="<script type='text/javascript' src='//your-adsterra-domain.com/path/to/your-script.js'></script>"><?php echo esc_textarea($current_settings['adsterra_head_code']); ?></textarea>

                                <p class="description">

                                    <?php _e('Legacy single ad code (inserted in head section only). For advanced placement options, use the Ad Units Manager.', 'epic-membership'); ?>

                                </p>

                                <?php if (!current_user_can('unfiltered_html')): ?>

                                <div class="notice notice-info inline">

                                    <p><strong><?php _e('Security Notice:', 'epic-membership'); ?></strong>

                                    <?php _e('HTML/JavaScript code will be filtered for security. Only standard ad network tags are allowed. Administrators have full HTML access.', 'epic-membership'); ?></p>

                                </div>

                                <?php endif; ?>

                                <?php if (!$is_migrated && !empty($current_settings['adsterra_head_code'])): ?>

                                    <div class="notice notice-warning inline">

                                        <p>

                                            <strong><?php _e('Migration Available:', 'epic-membership'); ?></strong>

                                            <?php _e('Your existing ad code can be migrated to the new multi-unit system for better control.', 'epic-membership'); ?>

                                            <a href="<?php echo admin_url('admin.php?page=epic-membership-ad-units'); ?>" class="button button-secondary">

                                                <?php _e('Migrate Now', 'epic-membership'); ?>

                                            </a>

                                        </p>

                                    </div>

                                <?php endif; ?>

                            </td>

                        </tr>

                    </tbody>

                </table>

                

                <div class="epic-membership-ad-help">

                    <h3><?php _e('Advertisement Integration Guide', 'epic-membership'); ?></h3>



                    <div style="display: flex; gap: 20px; margin-bottom: 20px;">

                        <div style="flex: 1;">

                            <h4><?php _e('Google AdSense', 'epic-membership'); ?></h4>

                            <p><?php _e('With AdSense auto ads enabled, Google automatically:', 'epic-membership'); ?></p>

                            <ul>

                                <li><?php _e('Analyzes your site content and layout', 'epic-membership'); ?></li>

                                <li><?php _e('Places ads in optimal locations', 'epic-membership'); ?></li>

                                <li><?php _e('Optimizes ad performance automatically', 'epic-membership'); ?></li>

                                <li><?php _e('Shows relevant ads to visitors', 'epic-membership'); ?></li>

                            </ul>

                        </div>



                        <div style="flex: 1;">

                            <h4><?php _e('Adsterra', 'epic-membership'); ?></h4>

                            <p><?php _e('Adsterra supports various ad formats:', 'epic-membership'); ?></p>

                            <ul>

                                <li><?php _e('Popunder ads (high revenue)', 'epic-membership'); ?></li>

                                <li><?php _e('Social bar ads (non-intrusive)', 'epic-membership'); ?></li>

                                <li><?php _e('Banner ads (traditional display)', 'epic-membership'); ?></li>

                                <li><?php _e('Native ads (content-integrated)', 'epic-membership'); ?></li>

                            </ul>

                        </div>

                    </div>



                    <div class="notice notice-info inline">

                        <p><strong><?php _e('Important:', 'epic-membership'); ?></strong> <?php _e('You can enable both ad providers simultaneously. Both will be automatically hidden for premium members with ad-free capability.', 'epic-membership'); ?></p>

                    </div>

                </div>

            </div>



            <!-- PayPal Settings -->

            <div id="paypal" class="tab-content">

                <h2><?php _e('PayPal Payment Settings', 'epic-membership'); ?></h2>

                <p class="description"><?php _e('Configure PayPal payment gateway for membership purchases. PayPal Standard Checkout will be used for processing payments.', 'epic-membership'); ?></p>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_enabled"><?php _e('Enable PayPal', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_paypal_enabled"

                                           id="epic_membership_paypal_enabled"

                                           value="1"

                                           <?php checked($current_settings['paypal_enabled']); ?>>

                                    <?php _e('Enable PayPal payment gateway', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Allow users to purchase memberships using PayPal.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_sandbox_mode"><?php _e('Sandbox Mode', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_paypal_sandbox_mode"

                                           id="epic_membership_paypal_sandbox_mode"

                                           value="1"

                                           <?php checked($current_settings['paypal_sandbox_mode']); ?>>

                                    <?php _e('Enable sandbox mode for testing', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Use PayPal sandbox for testing. Disable this for live payments.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_currency"><?php _e('Currency', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <select name="epic_membership_paypal_currency" id="epic_membership_paypal_currency" class="regular-text">

                                    <?php

                                    $currencies = array(

                                        'USD' => 'US Dollar (USD)',

                                        'EUR' => 'Euro (EUR)',

                                        'GBP' => 'British Pound (GBP)',

                                        'CAD' => 'Canadian Dollar (CAD)',

                                        'AUD' => 'Australian Dollar (AUD)',

                                        'JPY' => 'Japanese Yen (JPY)',

                                        'CHF' => 'Swiss Franc (CHF)',

                                        'SEK' => 'Swedish Krona (SEK)',

                                        'NOK' => 'Norwegian Krone (NOK)',

                                        'DKK' => 'Danish Krone (DKK)'

                                    );

                                    foreach ($currencies as $code => $name) {

                                        echo '<option value="' . esc_attr($code) . '"' . selected($current_settings['paypal_currency'], $code, false) . '>' . esc_html($name) . '</option>';

                                    }

                                    ?>

                                </select>

                                <p class="description">

                                    <?php _e('Currency for PayPal payments. Make sure your PayPal account supports the selected currency.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <h3><?php _e('Live API Credentials', 'epic-membership'); ?></h3>

                <p class="description"><?php _e('Enter your live PayPal API credentials for production payments.', 'epic-membership'); ?></p>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_client_id"><?php _e('Client ID', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="text"

                                       name="epic_membership_paypal_client_id"

                                       id="epic_membership_paypal_client_id"

                                       value="<?php echo esc_attr($current_settings['paypal_client_id']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Live PayPal Client ID', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('Your live PayPal application Client ID.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_client_secret"><?php _e('Client Secret', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="password"

                                       name="epic_membership_paypal_client_secret"

                                       id="epic_membership_paypal_client_secret"

                                       value="<?php echo esc_attr($current_settings['paypal_client_secret']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Live PayPal Client Secret', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('Your live PayPal application Client Secret.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <h3><?php _e('Sandbox API Credentials', 'epic-membership'); ?></h3>

                <p class="description"><?php _e('Enter your sandbox PayPal API credentials for testing.', 'epic-membership'); ?></p>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_sandbox_client_id"><?php _e('Sandbox Client ID', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="text"

                                       name="epic_membership_paypal_sandbox_client_id"

                                       id="epic_membership_paypal_sandbox_client_id"

                                       value="<?php echo esc_attr($current_settings['paypal_sandbox_client_id']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Sandbox PayPal Client ID', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('Your sandbox PayPal application Client ID for testing.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_sandbox_client_secret"><?php _e('Sandbox Client Secret', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="password"

                                       name="epic_membership_paypal_sandbox_client_secret"

                                       id="epic_membership_paypal_sandbox_client_secret"

                                       value="<?php echo esc_attr($current_settings['paypal_sandbox_client_secret']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Sandbox PayPal Client Secret', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('Your sandbox PayPal application Client Secret for testing.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <h3><?php _e('Advanced Settings', 'epic-membership'); ?></h3>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_webhook_id"><?php _e('Webhook ID', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="text"

                                       name="epic_membership_paypal_webhook_id"

                                       id="epic_membership_paypal_webhook_id"

                                       value="<?php echo esc_attr($current_settings['paypal_webhook_id']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('PayPal Webhook ID (optional)', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('PayPal Webhook ID for enhanced security. Leave empty if not using webhooks.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_paypal_debug_mode"><?php _e('Debug Mode', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_paypal_debug_mode"

                                           id="epic_membership_paypal_debug_mode"

                                           value="1"

                                           <?php checked($current_settings['paypal_debug_mode']); ?>>

                                    <?php _e('Enable debug logging for PayPal transactions', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Log detailed PayPal API interactions for troubleshooting.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <div class="epic-membership-paypal-setup-guide">

                    <h3><?php _e('Setup Instructions', 'epic-membership'); ?></h3>

                    <ol>

                        <li><?php _e('Create a PayPal Developer account at developer.paypal.com', 'epic-membership'); ?></li>

                        <li><?php _e('Create a new application in your PayPal Developer Dashboard', 'epic-membership'); ?></li>

                        <li><?php _e('Copy the Client ID and Client Secret from your application', 'epic-membership'); ?></li>

                        <li><?php _e('Enter the credentials above (use sandbox credentials for testing)', 'epic-membership'); ?></li>

                        <li><?php _e('Enable PayPal payments and test with sandbox mode first', 'epic-membership'); ?></li>

                        <li><?php _e('Once testing is complete, disable sandbox mode for live payments', 'epic-membership'); ?></li>

                    </ol>

                    <p><strong><?php _e('Webhook URL:', 'epic-membership'); ?></strong> <code><?php echo esc_url(admin_url('admin-ajax.php?action=epic_membership_paypal_webhook')); ?></code></p>

                    <p class="description"><?php _e('Configure this URL as a webhook endpoint in your PayPal application for real-time payment notifications.', 'epic-membership'); ?></p>

                </div>

            </div>



            <!-- Ko-fi Settings -->

            <div id="kofi" class="tab-content">

                <h2><?php _e('Ko-fi Payment Settings', 'epic-membership'); ?></h2>

                <p class="description"><?php _e('Configure Ko-fi integration for membership purchases. Ko-fi webhooks will be used to automatically upgrade users when they make payments.', 'epic-membership'); ?></p>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_kofi_enabled"><?php _e('Enable Ko-fi', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_kofi_enabled"

                                           id="epic_membership_kofi_enabled"

                                           value="1"

                                           <?php checked($current_settings['kofi_enabled']); ?>>

                                    <?php _e('Enable Ko-fi payment gateway', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Allow users to purchase memberships using Ko-fi donations.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_kofi_page_url"><?php _e('Ko-fi Page URL', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="url"

                                       name="epic_membership_kofi_page_url"

                                       id="epic_membership_kofi_page_url"

                                       value="<?php echo esc_attr($current_settings['kofi_page_url']); ?>"

                                       class="regular-text"

                                       placeholder="https://ko-fi.com/yourusername">

                                <p class="description">

                                    <?php _e('Your Ko-fi page URL where users will be redirected to make payments.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_kofi_verification_token"><?php _e('Webhook Verification Token', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="text"

                                       name="epic_membership_kofi_verification_token"

                                       id="epic_membership_kofi_verification_token"

                                       value="<?php echo esc_attr($current_settings['kofi_verification_token']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Enter your Ko-fi webhook verification token', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('The verification token from your Ko-fi webhook settings. This ensures webhook authenticity.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_kofi_currency"><?php _e('Currency', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <select name="epic_membership_kofi_currency" id="epic_membership_kofi_currency">

                                    <?php

                                    $currencies = array(

                                        'USD' => __('US Dollar (USD)', 'epic-membership'),

                                        'EUR' => __('Euro (EUR)', 'epic-membership'),

                                        'GBP' => __('British Pound (GBP)', 'epic-membership'),

                                        'CAD' => __('Canadian Dollar (CAD)', 'epic-membership'),

                                        'AUD' => __('Australian Dollar (AUD)', 'epic-membership'),

                                        'JPY' => __('Japanese Yen (JPY)', 'epic-membership')

                                    );



                                    foreach ($currencies as $code => $name) {

                                        echo '<option value="' . esc_attr($code) . '"' . selected($current_settings['kofi_currency'], $code, false) . '>' . esc_html($name) . '</option>';

                                    }

                                    ?>

                                </select>

                                <p class="description">

                                    <?php _e('Currency for Ko-fi payments. Make sure your Ko-fi account supports the selected currency.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_kofi_auto_upgrade"><?php _e('Automatic Upgrades', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_kofi_auto_upgrade"

                                           id="epic_membership_kofi_auto_upgrade"

                                           value="1"

                                           <?php checked($current_settings['kofi_auto_upgrade']); ?>>

                                    <?php _e('Automatically upgrade users based on Ko-fi payments', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('When enabled, users will be automatically upgraded to the appropriate membership tier based on their Ko-fi payment amount.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_kofi_debug_mode"><?php _e('Debug Mode', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_kofi_debug_mode"

                                           id="epic_membership_kofi_debug_mode"

                                           value="1"

                                           <?php checked($current_settings['kofi_debug_mode']); ?>>

                                    <?php _e('Enable debug logging', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Enable detailed logging for Ko-fi webhook processing. Useful for troubleshooting.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <div class="epic-membership-kofi-setup-guide">

                    <h3><?php _e('Setup Instructions', 'epic-membership'); ?></h3>

                    <ol>

                        <li><?php _e('Create a Ko-fi account at ko-fi.com', 'epic-membership'); ?></li>

                        <li><?php _e('Go to your Ko-fi settings and navigate to the Webhooks section', 'epic-membership'); ?></li>

                        <li><?php _e('Add the webhook URL below to your Ko-fi webhook settings', 'epic-membership'); ?></li>

                        <li><?php _e('Copy the verification token from Ko-fi and enter it above', 'epic-membership'); ?></li>

                        <li><?php _e('Enter your Ko-fi page URL above', 'epic-membership'); ?></li>

                        <li><?php _e('Enable Ko-fi payments and test with a small donation', 'epic-membership'); ?></li>

                    </ol>

                    <p><strong><?php _e('Webhook URL:', 'epic-membership'); ?></strong> <code><?php echo esc_url(admin_url('admin-ajax.php?action=epic_membership_kofi_webhook')); ?></code></p>

                    <p class="description"><?php _e('Configure this URL as a webhook endpoint in your Ko-fi settings for real-time payment notifications.', 'epic-membership'); ?></p>



                    <div class="notice notice-info inline">

                        <p><strong><?php _e('How it works:', 'epic-membership'); ?></strong></p>

                        <ul>

                            <li><?php _e('Users make donations through your Ko-fi page', 'epic-membership'); ?></li>

                            <li><?php _e('Ko-fi sends a webhook notification to your site', 'epic-membership'); ?></li>

                            <li><?php _e('The plugin automatically matches the donor email with a WordPress user', 'epic-membership'); ?></li>

                            <li><?php _e('The user is upgraded to the appropriate membership tier based on the donation amount', 'epic-membership'); ?></li>

                        </ul>

                    </div>

                </div>

            </div>



            <!-- Trakteer Settings -->

            <div id="trakteer" class="tab-content">

                <h2><?php _e('Trakteer Payment Settings', 'epic-membership'); ?></h2>

                <p class="description"><?php _e('Configure Trakteer integration for membership purchases with Indonesian Rupiah (IDR) support. Trakteer webhooks will be used to automatically upgrade users when they make payments.', 'epic-membership'); ?></p>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_enabled"><?php _e('Enable Trakteer', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_trakteer_enabled"

                                           id="epic_membership_trakteer_enabled"

                                           value="1"

                                           <?php checked($current_settings['trakteer_enabled']); ?>>

                                    <?php _e('Enable Trakteer payment gateway', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Allow users to purchase memberships using Trakteer with IDR currency support.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_page_url"><?php _e('Trakteer Page URL', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="url"

                                       name="epic_membership_trakteer_page_url"

                                       id="epic_membership_trakteer_page_url"

                                       value="<?php echo esc_attr($current_settings['trakteer_page_url']); ?>"

                                       class="regular-text"

                                       placeholder="https://trakteer.id/yourusername">

                                <p class="description">

                                    <?php _e('Your Trakteer page URL where users will be redirected to make payments.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_verification_token"><?php _e('Webhook Verification Token', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="text"

                                       name="epic_membership_trakteer_verification_token"

                                       id="epic_membership_trakteer_verification_token"

                                       value="<?php echo esc_attr($current_settings['trakteer_verification_token']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Enter your Trakteer webhook verification token', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('The verification token from your Trakteer webhook settings for secure webhook validation.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_webhook_secret"><?php _e('Webhook Secret', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="password"

                                       name="epic_membership_trakteer_webhook_secret"

                                       id="epic_membership_trakteer_webhook_secret"

                                       value="<?php echo esc_attr($current_settings['trakteer_webhook_secret']); ?>"

                                       class="regular-text"

                                       placeholder="<?php _e('Enter your Trakteer webhook secret', 'epic-membership'); ?>">

                                <p class="description">

                                    <?php _e('The webhook secret for HMAC signature verification (optional but recommended for security).', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_currency"><?php _e('Currency', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <select name="epic_membership_trakteer_currency" id="epic_membership_trakteer_currency">

                                    <?php

                                    $currencies = array(

                                        'IDR' => 'Indonesian Rupiah (IDR)',

                                        'USD' => 'US Dollar (USD)',

                                        'EUR' => 'Euro (EUR)',

                                        'GBP' => 'British Pound (GBP)',

                                        'SGD' => 'Singapore Dollar (SGD)',

                                        'MYR' => 'Malaysian Ringgit (MYR)'

                                    );



                                    foreach ($currencies as $code => $name) {

                                        echo '<option value="' . esc_attr($code) . '"' . selected($current_settings['trakteer_currency'], $code, false) . '>' . esc_html($name) . '</option>';

                                    }

                                    ?>

                                </select>

                                <p class="description">

                                    <?php _e('Currency for Trakteer payments. IDR is recommended for Indonesian users.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_sandbox_mode"><?php _e('Sandbox Mode', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_trakteer_sandbox_mode"

                                           id="epic_membership_trakteer_sandbox_mode"

                                           value="1"

                                           <?php checked($current_settings['trakteer_sandbox_mode']); ?>>

                                    <?php _e('Enable sandbox mode for testing', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Use Trakteer sandbox for testing. Disable this for live payments.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_auto_upgrade"><?php _e('Automatic Upgrades', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_trakteer_auto_upgrade"

                                           id="epic_membership_trakteer_auto_upgrade"

                                           value="1"

                                           <?php checked($current_settings['trakteer_auto_upgrade']); ?>>

                                    <?php _e('Automatically upgrade users based on Trakteer payments', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('When enabled, users will be automatically upgraded to the appropriate membership tier based on their Trakteer payment amount.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>



                        <tr>

                            <th scope="row">

                                <label for="epic_membership_trakteer_debug_mode"><?php _e('Debug Mode', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <label>

                                    <input type="checkbox"

                                           name="epic_membership_trakteer_debug_mode"

                                           id="epic_membership_trakteer_debug_mode"

                                           value="1"

                                           <?php checked($current_settings['trakteer_debug_mode']); ?>>

                                    <?php _e('Enable debug logging', 'epic-membership'); ?>

                                </label>

                                <p class="description">

                                    <?php _e('Log detailed Trakteer webhook interactions for troubleshooting.', 'epic-membership'); ?>

                                </p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <div class="epic-membership-trakteer-setup-guide">

                    <h3><?php _e('Setup Instructions', 'epic-membership'); ?></h3>

                    <ol>

                        <li><?php _e('Create a Trakteer account at trakteer.id', 'epic-membership'); ?></li>

                        <li><?php _e('Set up your Trakteer creator page', 'epic-membership'); ?></li>

                        <li><?php _e('Configure webhook settings in your Trakteer dashboard', 'epic-membership'); ?></li>

                        <li><?php _e('Add the webhook URL below to your Trakteer webhook settings', 'epic-membership'); ?></li>

                        <li><?php _e('Copy the verification token from Trakteer and enter it above', 'epic-membership'); ?></li>

                        <li><?php _e('Enter your Trakteer page URL above', 'epic-membership'); ?></li>

                        <li><?php _e('Enable Trakteer payments and test with a small donation', 'epic-membership'); ?></li>

                    </ol>

                    <p><strong><?php _e('Webhook URL:', 'epic-membership'); ?></strong> <code><?php echo esc_url(admin_url('admin-ajax.php?action=epic_membership_trakteer_webhook')); ?></code></p>

                    <p class="description"><?php _e('Configure this URL as a webhook endpoint in your Trakteer settings for real-time payment notifications.', 'epic-membership'); ?></p>



                    <div class="notice notice-info inline">

                        <p><strong><?php _e('How it works:', 'epic-membership'); ?></strong></p>

                        <ul>

                            <li><?php _e('Users make donations through your Trakteer page', 'epic-membership'); ?></li>

                            <li><?php _e('Trakteer sends a webhook notification to your site', 'epic-membership'); ?></li>

                            <li><?php _e('The plugin automatically matches the supporter email with a WordPress user', 'epic-membership'); ?></li>

                            <li><?php _e('The user is upgraded to the appropriate membership tier based on the donation amount', 'epic-membership'); ?></li>

                        </ul>

                    </div>

                </div>

            </div>



            <!-- Advanced Settings -->

            <div id="advanced" class="tab-content">

                <h2><?php _e('Advanced Settings', 'epic-membership'); ?></h2>

                

                <div class="epic-membership-system-info">

                    <h3><?php _e('System Information', 'epic-membership'); ?></h3>

                    <table class="widefat">

                        <tbody>

                            <tr>

                                <td><strong><?php _e('Plugin Version:', 'epic-membership'); ?></strong></td>

                                <td><?php echo EPIC_MEMBERSHIP_VERSION; ?></td>

                            </tr>

                            <tr>

                                <td><strong><?php _e('Database Version:', 'epic-membership'); ?></strong></td>

                                <td><?php echo get_option('epic_membership_db_version', '1.0.0'); ?></td>

                            </tr>

                            <tr>

                                <td><strong><?php _e('WordPress Version:', 'epic-membership'); ?></strong></td>

                                <td><?php echo get_bloginfo('version'); ?></td>

                            </tr>

                            <tr>

                                <td><strong><?php _e('PHP Version:', 'epic-membership'); ?></strong></td>

                                <td><?php echo PHP_VERSION; ?></td>

                            </tr>

                            <tr>

                                <td><strong><?php _e('Server Timezone:', 'epic-membership'); ?></strong></td>

                                <td><?php echo wp_timezone_string(); ?></td>

                            </tr>

                            <tr>

                                <td><strong><?php _e('Cron Status:', 'epic-membership'); ?></strong></td>

                                <td>

                                    <?php if (wp_next_scheduled('epic_membership_check_expired_memberships')): ?>

                                        <span style="color: green;">✓ <?php _e('Active', 'epic-membership'); ?></span>

                                    <?php else: ?>

                                        <span style="color: red;">✗ <?php _e('Inactive', 'epic-membership'); ?></span>

                                    <?php endif; ?>

                                </td>

                            </tr>

                        </tbody>

                    </table>

                </div>

                

                <div class="epic-membership-tools">

                    <h3><?php _e('Tools', 'epic-membership'); ?></h3>

                    <p>

                        <button type="button" class="button" onclick="epicMembershipTools.clearCache()">

                            <?php _e('Clear Plugin Cache', 'epic-membership'); ?>

                        </button>

                        <button type="button" class="button" onclick="epicMembershipTools.checkExpired()">

                            <?php _e('Check Expired Memberships', 'epic-membership'); ?>

                        </button>

                        <button type="button" class="button" onclick="epicMembershipTools.exportData()">

                            <?php _e('Export Membership Data', 'epic-membership'); ?>

                        </button>

                    </p>

                </div>

            </div>

        </div>

        

        <p class="submit">

            <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php esc_attr_e('Save Settings', 'epic-membership'); ?>">

        </p>

    </form>

</div>



<style>

.epic-membership-settings-tabs {

    margin-top: 20px;

}



.nav-tab-wrapper {

    border-bottom: 1px solid #c3c4c7;

    margin-bottom: 20px;

}



.nav-tab {

    cursor: pointer;

    text-decoration: none;

}



.nav-tab:hover {

    background-color: #f0f0f1;

}



.nav-tab.nav-tab-active {

    background-color: #fff;

    border-bottom: 1px solid #fff;

    color: #000;

}



.tab-content {

    display: none;

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    margin-bottom: 20px;

}



.tab-content.active {

    display: block;

}



.epic-membership-ad-help {

    background: #f8f9fa;

    border: 1px solid #dee2e6;

    border-radius: 4px;

    padding: 15px;

    margin-top: 20px;

}



.epic-membership-ad-help pre {

    background: #f1f1f1;

    padding: 10px;

    border-radius: 3px;

    overflow-x: auto;

}



.epic-membership-system-info,

.epic-membership-tools {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    margin-bottom: 20px;

}



.epic-membership-system-info table {

    margin-top: 10px;

}



.epic-membership-system-info td {

    padding: 8px 12px;

    border-bottom: 1px solid #f0f0f1;

}



.epic-membership-tools p {

    margin-bottom: 10px;

}



.epic-membership-tools .button {

    margin-right: 10px;

}

</style>



<script>

jQuery(document).ready(function($) {

    // Enhanced tab switching functionality

    function switchTab(targetId) {

        // Remove active class from all tabs and content

        $('.nav-tab').removeClass('nav-tab-active');

        $('.tab-content').removeClass('active');



        // Add active class to clicked tab and corresponding content

        $('a[href="' + targetId + '"]').addClass('nav-tab-active');

        $(targetId).addClass('active');



        // Store active tab in localStorage for persistence

        localStorage.setItem('epic_membership_active_tab', targetId);

    }



    // Tab click handler

    $('.nav-tab').on('click', function(e) {

        e.preventDefault();

        var target = $(this).attr('href');



        // Validate target exists

        if ($(target).length) {

            switchTab(target);

        }

    });



    // Restore active tab from localStorage or URL hash

    $(document).ready(function() {

        var activeTab = localStorage.getItem('epic_membership_active_tab') || window.location.hash;



        // If no stored tab or hash, default to #general

        if (!activeTab || !$(activeTab).length) {

            activeTab = '#general';

        }



        // Switch to the active tab

        switchTab(activeTab);

    });



    // Handle URL hash changes

    $(window).on('hashchange', function() {

        var hash = window.location.hash;

        if (hash && $(hash).length) {

            switchTab(hash);

        }

    });

});



// Tools functionality

var epicMembershipTools = {

    clearCache: function() {

        if (confirm('<?php echo esc_js(__('Are you sure you want to clear the plugin cache?', 'epic-membership')); ?>')) {

            // Implement cache clearing

            alert('<?php echo esc_js(__('Cache cleared successfully.', 'epic-membership')); ?>');

        }

    },

    

    checkExpired: function() {

        if (confirm('<?php echo esc_js(__('Check for expired memberships now?', 'epic-membership')); ?>')) {

            // Trigger expired membership check

            jQuery.post('<?php echo admin_url('admin-ajax.php'); ?>', {

                action: 'epic_membership_check_expired_now',

                nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'

            }, function(response) {

                if (response.success) {

                    alert('<?php echo esc_js(__('Expired memberships checked successfully.', 'epic-membership')); ?>');

                } else {

                    alert('<?php echo esc_js(__('Error checking expired memberships.', 'epic-membership')); ?>');

                }

            });

        }

    },

    

    exportData: function() {

        if (confirm('<?php echo esc_js(__('Export membership data to CSV?', 'epic-membership')); ?>')) {

            window.location.href = '<?php echo admin_url('admin-post.php?action=epic_membership_export_data&nonce=' . wp_create_nonce('epic_membership_export')); ?>';

        }

    }

};



// Additional jQuery functionality

jQuery(document).ready(function($) {

    // Handle create status page button

    $('#create-status-page').on('click', function(e) {

        e.preventDefault();



        var $button = $(this);

        var originalText = $button.text();



        $button.text('<?php _e('Creating...', 'epic-membership'); ?>').prop('disabled', true);



        $.ajax({

            url: '<?php echo admin_url('admin-ajax.php'); ?>',

            type: 'POST',

            data: {

                action: 'epic_membership_create_status_page',

                nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'

            },

            success: function(response) {

                if (response.success) {

                    location.reload(); // Reload to show the new page status

                } else {

                    alert(response.data || '<?php _e('Failed to create status page.', 'epic-membership'); ?>');

                    $button.text(originalText).prop('disabled', false);

                }

            },

            error: function(xhr, status, error) {

                var errorMessage = '<?php _e('An error occurred. Please try again.', 'epic-membership'); ?>';

                if (xhr.responseText) {

                    try {

                        var errorResponse = JSON.parse(xhr.responseText);

                        if (errorResponse.data) {

                            errorMessage = errorResponse.data;

                        }

                    } catch (e) {

                        // If response is not JSON and short enough, show it

                        if (xhr.responseText.length < 200) {

                            errorMessage = xhr.responseText;

                        }

                    }

                }

                alert(errorMessage);

                $button.text(originalText).prop('disabled', false);

            }

        });

    });



    // PayPal settings validation and feedback

    var paypalForm = $('form');

    var paypalEnabled = $('#epic_membership_paypal_enabled');

    var sandboxMode = $('#epic_membership_paypal_sandbox_mode');

    var liveClientId = $('#epic_membership_paypal_client_id');

    var liveClientSecret = $('#epic_membership_paypal_client_secret');

    var sandboxClientId = $('#epic_membership_paypal_sandbox_client_id');

    var sandboxClientSecret = $('#epic_membership_paypal_sandbox_client_secret');



    // Show/hide credential fields based on sandbox mode

    function toggleCredentialFields() {

        var isEnabled = paypalEnabled.is(':checked');

        var isSandbox = sandboxMode.is(':checked');



        if (isEnabled) {

            if (isSandbox) {

                sandboxClientId.closest('tr').show();

                sandboxClientSecret.closest('tr').show();

                liveClientId.closest('tr').hide();

                liveClientSecret.closest('tr').hide();

            } else {

                sandboxClientId.closest('tr').hide();

                sandboxClientSecret.closest('tr').hide();

                liveClientId.closest('tr').show();

                liveClientSecret.closest('tr').show();

            }

        }

    }



    // Initial toggle

    toggleCredentialFields();



    // Bind events

    paypalEnabled.change(toggleCredentialFields);

    sandboxMode.change(toggleCredentialFields);



    // Form validation

    paypalForm.on('submit', function(e) {

        var errors = [];



        if (paypalEnabled.is(':checked')) {

            var isSandbox = sandboxMode.is(':checked');



            if (isSandbox) {

                if (!sandboxClientId.val().trim()) {

                    errors.push('Sandbox Client ID is required when PayPal is enabled in sandbox mode.');

                }

                if (!sandboxClientSecret.val().trim()) {

                    errors.push('Sandbox Client Secret is required when PayPal is enabled in sandbox mode.');

                }

            } else {

                if (!liveClientId.val().trim()) {

                    errors.push('Live Client ID is required when PayPal is enabled in live mode.');

                }

                if (!liveClientSecret.val().trim()) {

                    errors.push('Live Client Secret is required when PayPal is enabled in live mode.');

                }

            }

        }



        // Ko-fi validation

        var kofiEnabled = $('#epic_membership_kofi_enabled');

        var kofiPageUrl = $('#epic_membership_kofi_page_url');

        var kofiVerificationToken = $('#epic_membership_kofi_verification_token');



        if (kofiEnabled.is(':checked')) {

            if (!kofiPageUrl.val().trim()) {

                errors.push('Ko-fi Page URL is required when Ko-fi is enabled.');

            } else if (!kofiPageUrl.val().includes('ko-fi.com/')) {

                errors.push('Ko-fi Page URL must be a valid Ko-fi page URL.');

            }



            if (!kofiVerificationToken.val().trim()) {

                errors.push('Ko-fi Webhook Verification Token is required when Ko-fi is enabled.');

            }

        }



        // Trakteer validation

        var trakteerEnabled = $('#epic_membership_trakteer_enabled');

        var trakteerPageUrl = $('#epic_membership_trakteer_page_url');

        var trakteerVerificationToken = $('#epic_membership_trakteer_verification_token');



        if (trakteerEnabled.is(':checked')) {

            if (!trakteerPageUrl.val().trim()) {

                errors.push('Trakteer Page URL is required when Trakteer is enabled.');

            } else if (!trakteerPageUrl.val().includes('trakteer.id/')) {

                errors.push('Trakteer Page URL must be a valid Trakteer page URL.');

            }



            if (!trakteerVerificationToken.val().trim()) {

                errors.push('Trakteer Webhook Verification Token is required when Trakteer is enabled.');

            }

        }



        if (errors.length > 0) {

            e.preventDefault();

            alert('Please fix the following errors:\n\n' + errors.join('\n'));

            return false;

        }



        // Show saving indicator

        var submitButton = $(this).find('input[type="submit"]');

        submitButton.val('Saving...').prop('disabled', true);



        // Add a timeout to re-enable the button in case of issues

        setTimeout(function() {

            submitButton.val('Save Settings').prop('disabled', false);

        }, 10000);

    });



    // Add visual feedback for credential fields

    function validateCredentialField(field, minLength) {

        var value = field.val().trim();

        var row = field.closest('tr');



        // Remove existing indicators

        row.find('.credential-status').remove();



        if (value.length === 0) {

            // Empty field

            field.css('border-color', '#ddd');

        } else if (value.length < minLength) {

            // Too short

            field.css('border-color', '#dc3232');

            field.after('<span class="credential-status" style="color: #dc3232; margin-left: 10px;">Too short</span>');

        } else {

            // Looks good

            field.css('border-color', '#46b450');

            field.after('<span class="credential-status" style="color: #46b450; margin-left: 10px;">✓</span>');

        }

    }



    // Bind validation to credential fields

    liveClientId.on('input blur', function() { validateCredentialField($(this), 10); });

    liveClientSecret.on('input blur', function() { validateCredentialField($(this), 10); });

    sandboxClientId.on('input blur', function() { validateCredentialField($(this), 10); });

    sandboxClientSecret.on('input blur', function() { validateCredentialField($(this), 10); });



    // Initial validation

    liveClientId.trigger('blur');

    liveClientSecret.trigger('blur');

    sandboxClientId.trigger('blur');

    sandboxClientSecret.trigger('blur');



    // Ko-fi settings validation

    var kofiEnabled = $('#epic_membership_kofi_enabled');

    var kofiPageUrl = $('#epic_membership_kofi_page_url');

    var kofiVerificationToken = $('#epic_membership_kofi_verification_token');



    // Ko-fi URL validation

    function validateKofiUrl(field) {

        var value = field.val().trim();

        var row = field.closest('tr');



        // Remove existing indicators

        row.find('.credential-status').remove();



        if (value.length === 0) {

            field.css('border-color', '#ddd');

        } else if (!value.includes('ko-fi.com/')) {

            field.css('border-color', '#dc3232');

            field.after('<span class="credential-status" style="color: #dc3232; margin-left: 10px;">Must be a Ko-fi URL</span>');

        } else {

            field.css('border-color', '#46b450');

            field.after('<span class="credential-status" style="color: #46b450; margin-left: 10px;">✓</span>');

        }

    }



    // Bind Ko-fi validation

    kofiPageUrl.on('input blur', function() { validateKofiUrl($(this)); });

    kofiVerificationToken.on('input blur', function() { validateCredentialField($(this), 5); });



    // Initial Ko-fi validation

    kofiPageUrl.trigger('blur');

    kofiVerificationToken.trigger('blur');



    // Trakteer settings validation

    var trakteerEnabled = $('#epic_membership_trakteer_enabled');

    var trakteerPageUrl = $('#epic_membership_trakteer_page_url');

    var trakteerVerificationToken = $('#epic_membership_trakteer_verification_token');



    // Trakteer URL validation

    function validateTrakteerUrl(field) {

        var value = field.val().trim();

        var row = field.closest('tr');

        var feedback = row.find('.validation-feedback');



        if (feedback.length === 0) {

            feedback = $('<div class="validation-feedback"></div>');

            field.after(feedback);

        }



        if (value && !value.includes('trakteer.id/')) {

            feedback.html('<span style="color: #d63638;">⚠ Please enter a valid Trakteer page URL (e.g., https://trakteer.id/yourusername)</span>');

            field.css('border-color', '#d63638');

        } else if (value) {

            feedback.html('<span style="color: #00a32a;">✓ Valid Trakteer URL format</span>');

            field.css('border-color', '#00a32a');

        } else {

            feedback.html('');

            field.css('border-color', '');

        }

    }



    // Bind Trakteer validation

    trakteerPageUrl.on('input blur', function() { validateTrakteerUrl($(this)); });

    trakteerVerificationToken.on('input blur', function() { validateCredentialField($(this), 5); });



    // Initial Trakteer validation

    trakteerPageUrl.trigger('blur');

    trakteerVerificationToken.trigger('blur');

});

</script>

