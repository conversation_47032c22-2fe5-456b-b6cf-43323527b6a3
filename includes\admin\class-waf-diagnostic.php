<?php
/**
 * WAF Diagnostic Tool for Epic Membership
 * 
 * This class helps diagnose and resolve 403 Forbidden errors
 * when saving ad units, typically caused by WAF or security plugins.
 */

if (!defined('ABSPATH')) {
    exit;
}

class Epic_Membership_WAF_Diagnostic {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_ajax_epic_membership_waf_diagnostic', array($this, 'ajax_run_diagnostic'));
        add_action('admin_notices', array($this, 'show_waf_notice'));
    }
    
    /**
     * Run WAF diagnostic
     */
    public function ajax_run_diagnostic() {
        check_ajax_referer('epic_membership_ad_units', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $diagnostic_results = array();
        
        // Test 1: Basic AJAX connectivity
        $diagnostic_results['basic_ajax'] = array(
            'test' => 'Basic AJAX Connectivity',
            'status' => 'pass',
            'message' => 'AJAX endpoint is reachable'
        );
        
        // Test 2: Check for security plugins
        $security_plugins = $this->detect_security_plugins();
        $diagnostic_results['security_plugins'] = array(
            'test' => 'Security Plugins Detection',
            'status' => count($security_plugins) > 0 ? 'warning' : 'pass',
            'message' => count($security_plugins) > 0 ? 
                'Detected security plugins: ' . implode(', ', $security_plugins) : 
                'No known security plugins detected',
            'plugins' => $security_plugins
        );
        
        // Test 3: Check server environment
        $server_info = $this->get_server_info();
        $diagnostic_results['server_info'] = array(
            'test' => 'Server Environment',
            'status' => 'info',
            'message' => 'Server information collected',
            'data' => $server_info
        );
        
        // Test 4: Test ad code patterns
        $test_ad_code = isset($_POST['test_ad_code']) ? $_POST['test_ad_code'] : '';
        if (!empty($test_ad_code)) {
            $pattern_analysis = $this->analyze_ad_code_patterns($test_ad_code);
            $diagnostic_results['ad_code_analysis'] = array(
                'test' => 'Ad Code Pattern Analysis',
                'status' => count($pattern_analysis['triggers']) > 0 ? 'warning' : 'pass',
                'message' => count($pattern_analysis['triggers']) > 0 ? 
                    'Found potential WAF triggers: ' . implode(', ', $pattern_analysis['triggers']) :
                    'No obvious WAF triggers detected',
                'data' => $pattern_analysis
            );
        }
        
        // Test 5: Check .htaccess rules
        $htaccess_analysis = $this->analyze_htaccess();
        $diagnostic_results['htaccess'] = array(
            'test' => '.htaccess Analysis',
            'status' => $htaccess_analysis['has_security_rules'] ? 'warning' : 'pass',
            'message' => $htaccess_analysis['message'],
            'data' => $htaccess_analysis
        );
        
        wp_send_json_success($diagnostic_results);
    }
    
    /**
     * Detect security plugins
     */
    private function detect_security_plugins() {
        $security_plugins = array();
        
        $known_security_plugins = array(
            'wordfence/wordfence.php' => 'Wordfence Security',
            'wp-security-audit-log/wp-security-audit-log.php' => 'WP Security Audit Log',
            'better-wp-security/better-wp-security.php' => 'iThemes Security',
            'sucuri-scanner/sucuri.php' => 'Sucuri Security',
            'all-in-one-wp-security-and-firewall/wp-security.php' => 'All In One WP Security',
            'bulletproof-security/bulletproof-security.php' => 'BulletProof Security',
            'wp-fail2ban/wp-fail2ban.php' => 'WP Fail2Ban',
            'ninjafirewall/ninjafirewall.php' => 'NinjaFirewall',
            'wp-cerber/wp-cerber.php' => 'Cerber Security'
        );
        
        foreach ($known_security_plugins as $plugin_file => $plugin_name) {
            if (is_plugin_active($plugin_file)) {
                $security_plugins[] = $plugin_name;
            }
        }
        
        return $security_plugins;
    }
    
    /**
     * Get server information
     */
    private function get_server_info() {
        return array(
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'php_version' => PHP_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'mod_security' => $this->check_mod_security(),
            'cloudflare' => $this->check_cloudflare(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'Unknown'
        );
    }
    
    /**
     * Check for ModSecurity
     */
    private function check_mod_security() {
        // Check for ModSecurity headers or environment variables
        $indicators = array(
            isset($_SERVER['HTTP_X_MODSECURITY']),
            isset($_SERVER['MODSEC_MATCHED_RULES']),
            function_exists('apache_get_modules') && in_array('mod_security2', apache_get_modules()),
            strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'mod_security') !== false
        );
        
        return in_array(true, $indicators);
    }
    
    /**
     * Check for Cloudflare
     */
    private function check_cloudflare() {
        $cf_headers = array(
            'HTTP_CF_RAY',
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CF_VISITOR',
            'HTTP_CF_COUNTRY'
        );
        
        foreach ($cf_headers as $header) {
            if (isset($_SERVER[$header])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Analyze ad code for potential WAF triggers
     */
    private function analyze_ad_code_patterns($ad_code) {
        $triggers = array();
        $ad_code_lower = strtolower($ad_code);
        
        $waf_patterns = array(
            'script_tag' => '<script',
            'eval_function' => 'eval(',
            'document_write' => 'document.write',
            'inner_html' => 'innerhtml',
            'javascript_protocol' => 'javascript:',
            'vbscript_protocol' => 'vbscript:',
            'on_events' => array('onload=', 'onerror=', 'onclick=', 'onmouseover='),
            'external_urls' => array('http://', 'https://'),
            'base64_content' => 'base64',
            'iframe_tag' => '<iframe'
        );
        
        foreach ($waf_patterns as $pattern_name => $pattern) {
            if (is_array($pattern)) {
                foreach ($pattern as $sub_pattern) {
                    if (strpos($ad_code_lower, $sub_pattern) !== false) {
                        $triggers[] = $pattern_name . ' (' . $sub_pattern . ')';
                    }
                }
            } else {
                if (strpos($ad_code_lower, $pattern) !== false) {
                    $triggers[] = $pattern_name . ' (' . $pattern . ')';
                }
            }
        }
        
        return array(
            'triggers' => $triggers,
            'length' => strlen($ad_code),
            'has_html' => $ad_code !== strip_tags($ad_code),
            'has_javascript' => strpos($ad_code_lower, 'script') !== false
        );
    }
    
    /**
     * Analyze .htaccess for security rules
     */
    private function analyze_htaccess() {
        $htaccess_path = ABSPATH . '.htaccess';
        $analysis = array(
            'exists' => false,
            'readable' => false,
            'has_security_rules' => false,
            'security_rules' => array(),
            'message' => ''
        );
        
        if (file_exists($htaccess_path)) {
            $analysis['exists'] = true;
            
            if (is_readable($htaccess_path)) {
                $analysis['readable'] = true;
                $content = file_get_contents($htaccess_path);
                
                $security_patterns = array(
                    'ModSecurity' => '/mod_security|modsecurity/i',
                    'Request filtering' => '/RewriteCond.*REQUEST_URI/i',
                    'User agent blocking' => '/RewriteCond.*HTTP_USER_AGENT/i',
                    'POST data filtering' => '/RewriteCond.*REQUEST_METHOD.*POST/i'
                );
                
                foreach ($security_patterns as $rule_name => $pattern) {
                    if (preg_match($pattern, $content)) {
                        $analysis['security_rules'][] = $rule_name;
                        $analysis['has_security_rules'] = true;
                    }
                }
                
                $analysis['message'] = $analysis['has_security_rules'] ? 
                    'Found security rules that might block requests' : 
                    '.htaccess exists but no obvious security rules found';
            } else {
                $analysis['message'] = '.htaccess exists but is not readable';
            }
        } else {
            $analysis['message'] = 'No .htaccess file found';
        }
        
        return $analysis;
    }
    
    /**
     * Show WAF diagnostic notice
     */
    public function show_waf_notice() {
        $screen = get_current_screen();
        if ($screen && $screen->id === 'epic-membership_page_epic-membership-ad-units') {
            echo '<div class="notice notice-info is-dismissible">
                <p><strong>Epic Membership:</strong> If you\'re experiencing 403 errors when saving ad units, 
                <a href="#" id="run-waf-diagnostic">click here to run a diagnostic</a> to identify the cause.</p>
            </div>';
        }
    }
}

// Initialize the diagnostic tool
new Epic_Membership_WAF_Diagnostic();
