/**

 * Epic Membership Plugin - Admin JavaScript

 */



(function($) {

    'use strict';

    

    // Admin object

    var EpicMembershipAdmin = {

        

        /**

         * Initialize admin functionality

         */

        init: function() {

            this.initMetaBox();

            this.initTierManagement();

            this.initUserManagement();

            this.initDashboard();

            this.bindGlobalEvents();

        },

        

        /**

         * Initialize meta box functionality

         */

        initMetaBox: function() {

            // Handle access type changes

            $('#epic_membership_access_type').on('change', function() {

                var accessType = $(this).val();

                

                // Hide all conditional fields

                $('.epic-membership-conditional-field').hide();

                

                // Show relevant fields

                if (accessType === 'members_only') {

                    $('#members-only-settings, #content-teaser-settings').show();

                } else if (accessType === 'scheduled') {

                    $('#scheduled-release-settings, #content-teaser-settings').show();

                }

                

                // Update preview

                EpicMembershipAdmin.updateAccessPreview();

            });

            

            // Handle form field changes for preview

            $('#epic_membership_required_tier, #epic_membership_scheduled_release').on('change', this.updateAccessPreview);

            

            // Update preview on page load

            this.updateAccessPreview();

        },

        

        /**

         * Update access preview

         */

        updateAccessPreview: function() {

            var accessType = $('#epic_membership_access_type').val();

            var preview = $('#epic-membership-access-preview');

            var previewText = '';

            

            switch (accessType) {

                case 'public':

                    previewText = epicMembershipAdmin.strings.publicContent || 'This content is publicly accessible to all visitors.';

                    break;

                    

                case 'members_only':

                    var tierSelect = $('#epic_membership_required_tier');

                    var tierText = tierSelect.find('option:selected').text();

                    if (tierSelect.val()) {

                        previewText = 'This content requires ' + tierText + ' membership or higher.';

                    } else {

                        previewText = 'Please select a required membership tier.';

                    }

                    break;

                    

                case 'scheduled':

                    var releaseDate = $('#epic_membership_scheduled_release').val();

                    if (releaseDate) {

                        var formattedDate = new Date(releaseDate).toLocaleString();

                        previewText = 'This content will be publicly available on: ' + formattedDate;

                    } else {

                        previewText = 'Please set a release date and time.';

                    }

                    break;

            }

            

            preview.text(previewText);

            preview.toggleClass('has-content', previewText.length > 0);

        },

        

        /**

         * Initialize tier management

         */

        initTierManagement: function() {

            // Handle tier status toggle

            $('.toggle-tier-status').on('click', function() {

                var $button = $(this);

                var tierId = $button.data('tier-id');

                

                $.ajax({

                    url: ajaxurl,

                    type: 'POST',

                    data: {

                        action: 'epic_membership_toggle_tier_status',

                        tier_id: tierId,

                        nonce: epicMembershipAdmin.nonce

                    },

                    beforeSend: function() {

                        $button.prop('disabled', true);

                    },

                    success: function(response) {

                        if (response.success) {

                            location.reload();

                        } else {

                            alert('Error: ' + response.data);

                        }

                    },

                    error: function() {

                        alert(epicMembershipAdmin.strings.error || 'An error occurred. Please try again.');

                    },

                    complete: function() {

                        $button.prop('disabled', false);

                    }

                });

            });

            

            // Auto-generate slug from name

            $('#tier_name').on('input', function() {

                var name = $(this).val();

                var slug = name.toLowerCase()

                              .replace(/[^a-z0-9]+/g, '-')

                              .replace(/^-+|-+$/g, '');

                $('#tier_slug').val(slug);

            });

        },

        

        /**

         * Initialize user management

         */

        initUserManagement: function() {

            // Handle bulk action selector

            $('#bulk-action-selector').on('change', function() {

                var action = $(this).val();

                $('#bulk-tier-options, #bulk-extend-options').hide();

                

                if (action === 'update_tier') {

                    $('#bulk-tier-options').show();

                } else if (action === 'extend_membership') {

                    $('#bulk-extend-options').show();

                }

                

                // Enable/disable apply button

                $('#bulk-action-form input[type="submit"]').prop('disabled', !action);

            });

            

            // Handle checkbox selection

            $('#cb-select-all-1').on('change', function() {

                $('input[name="user_ids[]"]').prop('checked', this.checked);

                EpicMembershipAdmin.updateBulkActionButton();

            });

            

            $('input[name="user_ids[]"]').on('change', function() {

                EpicMembershipAdmin.updateBulkActionButton();

            });

            

            // Handle edit membership modal

            $('.edit-membership').on('click', function(e) {

                e.preventDefault();

                var userId = $(this).data('user-id');

                EpicMembershipAdmin.openEditMembershipModal(userId);

            });

            

            // Handle extend membership

            $('.extend-membership').on('click', function(e) {

                e.preventDefault();

                var userId = $(this).data('user-id');

                var days = prompt('Extend membership by how many days?', '30');

                

                if (days && parseInt(days) > 0) {

                    EpicMembershipAdmin.extendMembership(userId, parseInt(days));

                }

            });

            

            // Handle modal close

            $('.modal-close').on('click', function() {

                $('#edit-membership-modal').hide();

            });

            

            // Close modal on outside click

            $('#edit-membership-modal').on('click', function(e) {

                if (e.target === this) {

                    $(this).hide();

                }

            });

            

            // Handle bulk form submission

            $('#bulk-action-form').on('submit', function(e) {

                var checkedUsers = $('input[name="user_ids[]"]:checked').length;

                var action = $('#bulk-action-selector').val();

                

                if (!checkedUsers) {

                    e.preventDefault();

                    alert('Please select at least one user.');

                    return false;

                }

                

                if (!action) {

                    e.preventDefault();

                    alert('Please select a bulk action.');

                    return false;

                }

                

                // Add selected user IDs to the form

                $('input[name="user_ids[]"]:checked').each(function() {

                    $('#bulk-action-form').append('<input type="hidden" name="user_ids[]" value="' + $(this).val() + '">');

                });

                

                var confirmMessage = 'Are you sure you want to apply this action to the selected users?';

                return confirm(confirmMessage);

            });

        },

        

        /**

         * Update bulk action button state

         */

        updateBulkActionButton: function() {

            var checkedCount = $('input[name="user_ids[]"]:checked').length;

            var hasAction = $('#bulk-action-selector').val();

            $('#bulk-action-form input[type="submit"]').prop('disabled', !checkedCount || !hasAction);

        },

        

        /**

         * Open edit membership modal

         */

        openEditMembershipModal: function(userId) {

            $('#edit-user-id').val(userId);

            

            // Get current membership data

            $.ajax({

                url: ajaxurl,

                type: 'POST',

                data: {

                    action: 'epic_membership_get_user_membership',

                    user_id: userId,

                    nonce: epicMembershipAdmin.nonce

                },

                success: function(response) {

                    if (response.success && response.data) {

                        $('#edit-tier-id').val(response.data.tier_id || '');

                        $('#edit-duration-days').val('');

                        $('#edit-notes').val('');

                    }

                    $('#edit-membership-modal').show();

                },

                error: function() {

                    $('#edit-membership-modal').show();

                }

            });

        },

        

        /**

         * Extend membership

         */

        extendMembership: function(userId, days) {

            $.ajax({

                url: ajaxurl,

                type: 'POST',

                data: {

                    action: 'epic_membership_extend_membership',

                    user_id: userId,

                    days: days,

                    nonce: epicMembershipAdmin.nonce

                },

                success: function(response) {

                    if (response.success) {

                        location.reload();

                    } else {

                        alert('Error: ' + response.data);

                    }

                },

                error: function() {

                    alert(epicMembershipAdmin.strings.error || 'An error occurred. Please try again.');

                }

            });

        },

        

        /**

         * Initialize dashboard functionality

         */

        initDashboard: function() {

            // Auto-refresh dashboard stats

            if ($('.epic-membership-dashboard-stats').length) {

                setInterval(function() {

                    EpicMembershipAdmin.refreshDashboardStats();

                }, 300000); // 5 minutes

            }

        },

        

        /**

         * Refresh dashboard statistics

         */

        refreshDashboardStats: function() {

            $.ajax({

                url: ajaxurl,

                type: 'POST',

                data: {

                    action: 'epic_membership_get_membership_stats',

                    nonce: epicMembershipAdmin.nonce

                },

                success: function(response) {

                    if (response.success && response.data) {

                        // Update stat numbers

                        $('.stat-card').each(function() {

                            var $card = $(this);

                            var statType = $card.data('stat-type');

                            if (response.data[statType]) {

                                $card.find('.stat-number').text(response.data[statType]);

                            }

                        });

                    }

                }

            });

        },

        

        /**

         * Bind global events

         */

        bindGlobalEvents: function() {

            // Handle AJAX errors globally

            $(document).ajaxError(function(event, xhr, settings, thrownError) {

                if (xhr.status === 403) {

                    alert('Permission denied. Please refresh the page and try again.');

                } else if (xhr.status === 500) {

                    alert('Server error. Please try again later.');

                }

            });

            

            // Handle form validation

            $('form[data-epic-membership-validate]').on('submit', function(e) {

                var isValid = EpicMembershipAdmin.validateForm($(this));

                if (!isValid) {

                    e.preventDefault();

                    return false;

                }

            });

            

            // Handle confirmation dialogs

            $('[data-epic-membership-confirm]').on('click', function(e) {

                var message = $(this).data('epic-membership-confirm');

                if (!confirm(message)) {

                    e.preventDefault();

                    return false;

                }

            });

        },

        

        /**

         * Validate form

         */

        validateForm: function($form) {

            var isValid = true;

            

            // Check required fields

            $form.find('[required]').each(function() {

                var $field = $(this);

                if (!$field.val().trim()) {

                    $field.addClass('error');

                    isValid = false;

                } else {

                    $field.removeClass('error');

                }

            });

            

            // Check email fields

            $form.find('input[type="email"]').each(function() {

                var $field = $(this);

                var email = $field.val().trim();

                if (email && !EpicMembershipAdmin.isValidEmail(email)) {

                    $field.addClass('error');

                    isValid = false;

                } else {

                    $field.removeClass('error');

                }

            });

            

            return isValid;

        },

        

        /**

         * Validate email address

         */

        isValidEmail: function(email) {

            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            return emailRegex.test(email);

        },

        

        /**

         * Show loading state

         */

        showLoading: function($element) {

            $element.addClass('epic-membership-loading');

            $element.append('<span class="epic-membership-spinner"></span>');

        },

        

        /**

         * Hide loading state

         */

        hideLoading: function($element) {

            $element.removeClass('epic-membership-loading');

            $element.find('.epic-membership-spinner').remove();

        }

    };

    

    // Initialize when document is ready

    $(document).ready(function() {

        EpicMembershipAdmin.init();

    });

    

    // Make EpicMembershipAdmin globally available

    window.EpicMembershipAdmin = EpicMembershipAdmin;

    

})(jQuery);

