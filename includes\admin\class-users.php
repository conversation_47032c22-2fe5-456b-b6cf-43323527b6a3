<?php

/**

 * User Membership Management Class

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Users {

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Handle form submissions

        add_action('admin_post_epic_membership_update_user_membership', array($this, 'update_user_membership'));

        add_action('admin_post_epic_membership_bulk_update_memberships', array($this, 'bulk_update_memberships'));



        // AJAX handlers

        add_action('wp_ajax_epic_membership_search_users', array($this, 'ajax_search_users'));

        add_action('wp_ajax_epic_membership_get_user_membership', array($this, 'ajax_get_user_membership'));

        add_action('wp_ajax_epic_membership_extend_membership', array($this, 'ajax_extend_membership'));



        // Add membership info to user profile

        add_action('show_user_profile', array($this, 'show_user_membership_fields'));

        add_action('edit_user_profile', array($this, 'show_user_membership_fields'));

        add_action('personal_options_update', array($this, 'save_user_membership_fields'));

        add_action('edit_user_profile_update', array($this, 'save_user_membership_fields'));

    }

    

    /**

     * Get users with membership information

     */

    public function get_users_with_memberships($args = array()) {

        global $wpdb;

        

        $defaults = array(

            'number' => 20,

            'offset' => 0,

            'search' => '',

            'tier_id' => '',

            'status' => '', // active, expired, all

            'orderby' => 'user_registered',

            'order' => 'DESC'

        );

        

        $args = wp_parse_args($args, $defaults);

        

        $users_table = $wpdb->users;

        $memberships_table = $this->database->get_table('user_memberships');

        $tiers_table = $this->database->get_table('tiers');

        

        // Build query

        $select = "SELECT DISTINCT u.ID, u.user_login, u.user_email, u.user_registered, u.display_name,

                   m.id as membership_id, m.tier_id, m.start_date, m.end_date, m.is_active as membership_active,

                   t.name as tier_name, t.level as tier_level, t.slug as tier_slug";

        

        $from = "FROM $users_table u

                 LEFT JOIN $memberships_table m ON u.ID = m.user_id AND m.is_active = 1

                 LEFT JOIN $tiers_table t ON m.tier_id = t.id";

        

        $where = array('1=1');

        $where_values = array();

        

        // Search filter

        if (!empty($args['search'])) {

            $search = '%' . $wpdb->esc_like($args['search']) . '%';

            $where[] = "(u.user_login LIKE %s OR u.user_email LIKE %s OR u.display_name LIKE %s)";

            $where_values[] = $search;

            $where_values[] = $search;

            $where_values[] = $search;

        }

        

        // Tier filter

        if (!empty($args['tier_id'])) {

            $where[] = "m.tier_id = %d";

            $where_values[] = intval($args['tier_id']);

        }

        

        // Status filter

        if ($args['status'] === 'active') {

            $where[] = "m.is_active = 1 AND (m.end_date IS NULL OR m.end_date > NOW())";

        } elseif ($args['status'] === 'expired') {

            $where[] = "(m.is_active = 0 OR (m.end_date IS NOT NULL AND m.end_date <= NOW()))";

        }

        

        $where_clause = 'WHERE ' . implode(' AND ', $where);

        

        // Order

        $order_clause = sprintf('ORDER BY %s %s', 

            sanitize_sql_orderby($args['orderby']), 

            $args['order'] === 'ASC' ? 'ASC' : 'DESC'

        );

        

        // Limit

        $limit_clause = sprintf('LIMIT %d OFFSET %d', intval($args['number']), intval($args['offset']));

        

        // Execute query

        $sql = "$select $from $where_clause $order_clause $limit_clause";

        

        if (!empty($where_values)) {

            return $wpdb->get_results($wpdb->prepare($sql, $where_values));

        } else {

            return $wpdb->get_results($sql);

        }

    }

    

    /**

     * Get total users count for pagination

     */

    public function get_users_count($args = array()) {

        global $wpdb;

        

        $users_table = $wpdb->users;

        $memberships_table = $this->database->get_table('user_memberships');

        

        $from = "FROM $users_table u

                 LEFT JOIN $memberships_table m ON u.ID = m.user_id AND m.is_active = 1";

        

        $where = array('1=1');

        $where_values = array();

        

        // Apply same filters as get_users_with_memberships

        if (!empty($args['search'])) {

            $search = '%' . $wpdb->esc_like($args['search']) . '%';

            $where[] = "(u.user_login LIKE %s OR u.user_email LIKE %s OR u.display_name LIKE %s)";

            $where_values[] = $search;

            $where_values[] = $search;

            $where_values[] = $search;

        }

        

        if (!empty($args['tier_id'])) {

            $where[] = "m.tier_id = %d";

            $where_values[] = intval($args['tier_id']);

        }

        

        if ($args['status'] === 'active') {

            $where[] = "m.is_active = 1 AND (m.end_date IS NULL OR m.end_date > NOW())";

        } elseif ($args['status'] === 'expired') {

            $where[] = "(m.is_active = 0 OR (m.end_date IS NOT NULL AND m.end_date <= NOW()))";

        }

        

        $where_clause = 'WHERE ' . implode(' AND ', $where);

        $sql = "SELECT COUNT(DISTINCT u.ID) $from $where_clause";

        

        if (!empty($where_values)) {

            return $wpdb->get_var($wpdb->prepare($sql, $where_values));

        } else {

            return $wpdb->get_var($sql);

        }

    }

    

    /**

     * Update user membership

     */

    public function update_user_membership_data($user_id, $tier_id, $duration_days = null, $notes = '') {

        global $wpdb;

        $memberships_table = $this->database->get_table('user_memberships');

        

        // Deactivate existing memberships

        $wpdb->update(

            $memberships_table,

            array('is_active' => 0),

            array('user_id' => $user_id, 'is_active' => 1)

        );

        

        // If tier_id is 0 or empty, just deactivate (remove membership)

        if (empty($tier_id)) {

            return true;

        }

        

        // Get tier information

        $tiers_table = $this->database->get_table('tiers');

        $tier = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $tiers_table WHERE id = %d",

            $tier_id

        ));

        

        if (!$tier) {

            return false;

        }

        

        // Calculate end date

        $start_date = current_time('mysql');

        $end_date = null;

        

        if ($duration_days) {

            $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + $duration_days days"));

        } elseif ($tier->duration_days) {

            $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + {$tier->duration_days} days"));

        }

        

        // Create new membership

        return $wpdb->insert($memberships_table, array(

            'user_id' => $user_id,

            'tier_id' => $tier_id,

            'start_date' => $start_date,

            'end_date' => $end_date,

            'is_active' => 1,

            'notes' => sanitize_textarea_field($notes),

            'created_by' => get_current_user_id()

        ));

    }

    

    /**

     * Extend user membership

     */

    public function extend_user_membership($user_id, $days) {

        global $wpdb;

        $memberships_table = $this->database->get_table('user_memberships');

        

        // Get current active membership

        $membership = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $memberships_table WHERE user_id = %d AND is_active = 1 ORDER BY id DESC LIMIT 1",

            $user_id

        ));

        

        if (!$membership) {

            return false;

        }

        

        // Calculate new end date

        $current_end = $membership->end_date ?: current_time('mysql');

        $new_end_date = date('Y-m-d H:i:s', strtotime($current_end . " + $days days"));

        

        // Update membership

        return $wpdb->update(

            $memberships_table,

            array('end_date' => $new_end_date),

            array('id' => $membership->id)

        );

    }

    

    /**

     * Get membership statistics

     */

    public function get_membership_statistics() {

        global $wpdb;

        $memberships_table = $this->database->get_table('user_memberships');

        $tiers_table = $this->database->get_table('tiers');

        

        return array(

            'total_users' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}"),

            'active_memberships' => $wpdb->get_var("SELECT COUNT(*) FROM $memberships_table WHERE is_active = 1 AND (end_date IS NULL OR end_date > NOW())"),

            'expired_memberships' => $wpdb->get_var("SELECT COUNT(*) FROM $memberships_table WHERE is_active = 0 OR (end_date IS NOT NULL AND end_date <= NOW())"),

            'expiring_soon' => $wpdb->get_var("SELECT COUNT(*) FROM $memberships_table WHERE is_active = 1 AND end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)"),

            'tier_breakdown' => $wpdb->get_results("

                SELECT t.name, t.level, COUNT(m.id) as member_count

                FROM $tiers_table t

                LEFT JOIN $memberships_table m ON t.id = m.tier_id AND m.is_active = 1 AND (m.end_date IS NULL OR m.end_date > NOW())

                WHERE t.is_active = 1

                GROUP BY t.id

                ORDER BY t.level ASC

            ")

        );

    }

    

    /**

     * Handle form submission for updating user membership

     */

    public function update_user_membership() {

        // Check if this is a POST request

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {

            wp_die(__('Invalid request method.', 'epic-membership'));

        }



        // Verify nonce - check both possible nonce field names

        $nonce_verified = false;

        if (isset($_POST['_wpnonce']) && wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_update_user_membership')) {

            $nonce_verified = true;

        } elseif (isset($_POST['epic_membership_update_user_membership_nonce']) && wp_verify_nonce($_POST['epic_membership_update_user_membership_nonce'], 'epic_membership_update_user_membership')) {

            $nonce_verified = true;

        }



        if (!$nonce_verified) {

            wp_die(__('Security check failed. Please try again.', 'epic-membership'));

        }



        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }



        // Validate required fields

        if (empty($_POST['user_id'])) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-users',

                'epic_membership_message' => 'error',

                'error_details' => 'missing_user_id'

            ), admin_url('admin.php')));

            exit;

        }



        $user_id = intval($_POST['user_id']);

        $tier_id = intval($_POST['tier_id'] ?? 0);

        $duration_days = !empty($_POST['duration_days']) ? intval($_POST['duration_days']) : null;

        $notes = sanitize_textarea_field($_POST['notes'] ?? '');



        // Validate user exists

        if (!get_user_by('ID', $user_id)) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-users',

                'epic_membership_message' => 'error',

                'error_details' => 'invalid_user'

            ), admin_url('admin.php')));

            exit;

        }



        $result = $this->update_user_membership_data($user_id, $tier_id, $duration_days, $notes);



        if ($result !== false) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-users',

                'epic_membership_message' => 'user_updated',

                'user_id' => $user_id

            ), admin_url('admin.php')));

        } else {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-users',

                'epic_membership_message' => 'error',

                'error_details' => 'update_failed'

            ), admin_url('admin.php')));

        }

        exit;

    }

    

    /**

     * Handle bulk membership updates

     */

    public function bulk_update_memberships() {

        // Check if this is a POST request

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {

            wp_die(__('Invalid request method.', 'epic-membership'));

        }



        // Verify nonce

        if (!wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_bulk_update')) {

            wp_die(__('Security check failed. Please try again.', 'epic-membership'));

        }



        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }



        $user_ids = array_map('intval', $_POST['user_ids'] ?? array());

        $action = sanitize_text_field($_POST['bulk_action'] ?? '');



        // Validate required fields

        if (empty($user_ids)) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-users',

                'epic_membership_message' => 'error',

                'error_details' => 'no_users_selected'

            ), admin_url('admin.php')));

            exit;

        }



        if (empty($action)) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-users',

                'epic_membership_message' => 'error',

                'error_details' => 'no_action_selected'

            ), admin_url('admin.php')));

            exit;

        }



        $tier_id = intval($_POST['bulk_tier_id'] ?? 0);

        $duration_days = !empty($_POST['bulk_duration_days']) ? intval($_POST['bulk_duration_days']) : null;



        $updated_count = 0;

        $error_count = 0;



        foreach ($user_ids as $user_id) {

            if ($action === 'update_tier') {

                $result = $this->update_user_membership_data($user_id, $tier_id, $duration_days);

                if ($result !== false) {

                    $updated_count++;

                } else {

                    $error_count++;

                }

            } elseif ($action === 'extend_membership') {

                $extend_days = intval($_POST['bulk_extend_days'] ?? 30);

                $result = $this->extend_user_membership($user_id, $extend_days);

                if ($result !== false) {

                    $updated_count++;

                } else {

                    $error_count++;

                }

            }

        }



        wp_redirect(add_query_arg(array(

            'page' => 'epic-membership-users',

            'epic_membership_message' => 'bulk_updated',

            'updated_count' => $updated_count,

            'error_count' => $error_count

        ), admin_url('admin.php')));

        exit;

    }

    

    /**

     * AJAX: Search users

     */

    public function ajax_search_users() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $search = sanitize_text_field($_POST['search'] ?? '');

        

        $users = get_users(array(

            'search' => "*$search*",

            'search_columns' => array('user_login', 'user_email', 'display_name'),

            'number' => 10

        ));

        

        $results = array();

        foreach ($users as $user) {

            $membership = $this->database->get_user_membership($user->ID);

            $results[] = array(

                'id' => $user->ID,

                'login' => $user->user_login,

                'email' => $user->user_email,

                'display_name' => $user->display_name,

                'membership' => $membership ? array(

                    'tier_name' => $membership->tier_name,

                    'tier_level' => $membership->tier_level,

                    'end_date' => $membership->end_date

                ) : null

            );

        }

        

        wp_send_json_success($results);

    }

    

    /**

     * AJAX: Get user membership details

     */

    public function ajax_get_user_membership() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $user_id = intval($_POST['user_id']);

        $membership = $this->database->get_user_membership($user_id);

        

        wp_send_json_success($membership);

    }

    

    /**

     * AJAX: Extend membership

     */

    public function ajax_extend_membership() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $user_id = intval($_POST['user_id']);

        $days = intval($_POST['days']);

        

        $result = $this->extend_user_membership($user_id, $days);

        

        if ($result) {

            wp_send_json_success('Membership extended successfully');

        } else {

            wp_send_json_error('Failed to extend membership');

        }

    }

    

    /**

     * Show membership fields in user profile

     */

    public function show_user_membership_fields($user) {

        if (!current_user_can('manage_options')) {

            return;

        }

        

        $membership = $this->database->get_user_membership($user->ID);

        $tiers = (new Epic_Membership_Tiers())->get_all_tiers(true);

        

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/user-membership-fields.php';

    }

    

    /**

     * Save membership fields from user profile

     */

    public function save_user_membership_fields($user_id) {

        if (!current_user_can('manage_options')) {

            return;

        }

        

        if (isset($_POST['epic_membership_tier_id'])) {

            $tier_id = intval($_POST['epic_membership_tier_id']);

            $duration_days = !empty($_POST['epic_membership_duration_days']) ? intval($_POST['epic_membership_duration_days']) : null;

            $notes = sanitize_textarea_field($_POST['epic_membership_notes'] ?? '');

            

            $this->update_user_membership_data($user_id, $tier_id, $duration_days, $notes);

        }

    }

}

