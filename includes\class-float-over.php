<?php

/**

 * Float Over functionality for Epic Membership Plugin

 *

 * @package Epic_Membership

 * @since 1.0.0

 */



// Prevent direct access

if (!defined('ABSPATH')) {

    exit;

}



/**

 * Epic Membership Float Over Class

 */

class Epic_Membership_Float_Over {



    /**

     * Single instance of the class

     */

    private static $instance = null;



    /**

     * Database instance

     */

    private $database;

    

    /**

     * Get single instance

     */

    public static function get_instance() {

        if (null === self::$instance) {

            self::$instance = new self();

        }

        return self::$instance;

    }

    

    /**

     * Constructor

     */

    private function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->init_hooks();

    }

    

    /**

     * Initialize WordPress hooks

     */

    private function init_hooks() {

        // Enqueue scripts only on single posts

        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        

        // Add custom styles

        add_action('wp_head', array($this, 'add_custom_styles'));

        

        // Display float over only on single posts

        add_action('wp_footer', array($this, 'display_float_over'));

        

        // AJAX handler for getting random link

        add_action('wp_ajax_get_float_over_link', array($this, 'ajax_get_random_link'));

        add_action('wp_ajax_nopriv_get_float_over_link', array($this, 'ajax_get_random_link'));

    }

    

    /**

     * Check if float over should be displayed

     */

    private function should_display_float_over() {

        // Only show on single posts

        if (!is_single()) {

            return false;

        }



        // Check if float over is enabled

        if (!get_option('epic_membership_float_over_enabled', false)) {

            return false;

        }



        // Check if user has premium membership (optional - can be configured)

        if (get_option('epic_membership_float_over_premium_only', false)) {

            // Get current user's membership level using proper database method

            $user_id = get_current_user_id();

            if ($user_id) {

                // Use the proper database method to check membership status

                $membership = $this->database->get_user_membership($user_id);



                // If user has an active premium membership (level > 0), don't show float-over

                if ($membership && $membership->is_active && $membership->tier_level > 0) {

                    // Double-check that membership hasn't expired

                    if (!$membership->end_date || strtotime($membership->end_date) > time()) {

                        return false; // Don't show to premium users

                    }

                }

            }

        }



        return true;

    }

    

    /**

     * Enqueue scripts and styles

     */

    public function enqueue_scripts() {

        if (!$this->should_display_float_over()) {

            return;

        }

        

        wp_enqueue_script(

            'epic-membership-float-over',

            EPIC_MEMBERSHIP_PLUGIN_URL . 'assets/js/float-over.js',

            array('jquery'),

            EPIC_MEMBERSHIP_VERSION,

            true

        );

        

        wp_localize_script('epic-membership-float-over', 'epicFloatOverData', array(

            'ajaxUrl' => admin_url('admin-ajax.php'),

            'nonce' => wp_create_nonce('epic_float_over_nonce'),

            'delay' => get_option('epic_membership_float_over_delay', 5),

            'customText' => get_option('epic_membership_float_over_custom_text', 'Click the button below to continue')

        ));

    }

    

    /**

     * Add custom styles

     */

    public function add_custom_styles() {

        if (!$this->should_display_float_over()) {

            return;

        }

        

        echo '<style>

            body.epic-no-scroll {

                overflow: hidden;

                height: 100%;

                width: 100%;

                position: fixed;

            }

            #epic-float-over {

                z-index: 999999;

                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

            }

            #epic-float-over .float-over-content {

                background: #fff;

                border-radius: 8px;

                padding: 30px;

                max-width: 400px;

                box-shadow: 0 10px 30px rgba(0,0,0,0.3);

            }

            #epic-float-over-button {

                background: #0073aa;

                color: white;

                border: none;

                padding: 12px 24px;

                border-radius: 4px;

                cursor: pointer;

                font-size: 16px;

                transition: background-color 0.3s;

            }

            #epic-float-over-button:hover {

                background: #005a87;

            }

        </style>';

    }

    

    /**

     * Display float over HTML

     */

    public function display_float_over() {

        if (!$this->should_display_float_over()) {

            return;

        }

        

        $custom_text = get_option('epic_membership_float_over_custom_text', 'Click the button below to continue');

        ?>

        <div id="epic-float-over" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:transparent; color:#333; text-align:center;">

            <div style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%);">

                <div class="float-over-content">

                    <p id="epic-float-over-text"><?php echo esc_html($custom_text); ?></p>

                    <button id="epic-float-over-button" style="display:none;">Continue</button>

                </div>

            </div>

        </div>

        <?php

    }

    

    /**

     * AJAX handler to get random link

     */

    public function ajax_get_random_link() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_float_over_nonce')) {

            wp_die('Security check failed');

        }

        

        $random_link = $this->get_random_link();

        wp_send_json_success(array('link' => $random_link));

    }

    

    /**

     * Get random link considering the flexible usage limits per IP

     */

    private function get_random_link() {

        global $wpdb;



        $table_name = $wpdb->prefix . 'epic_membership_float_over_links';

        $ip_address = $this->get_user_ip();



        // Get all configured links

        $all_links = $this->get_all_configured_links();



        if (empty($all_links)) {

            return '#'; // Return placeholder if no links configured

        }



        // Get available links (not at their limit)

        $available_links = $this->get_available_links($all_links, $ip_address);



        // If no available links, reset limited links and try again

        if (empty($available_links)) {

            $this->reset_limited_links($ip_address);

            $available_links = $this->get_available_links($all_links, $ip_address);

        }



        // If still no available links, return placeholder

        if (empty($available_links)) {

            return '#';

        }



        // Select a random link from available ones

        $random_link = $available_links[array_rand($available_links)];



        // Update the count for the selected link (only for limited links)

        $this->update_link_count($random_link, $ip_address);



        return $random_link;

    }



    /**

     * Get all configured links with their settings

     */

    private function get_all_configured_links() {

        $links = array();



        // Get limited links

        $limited_links_string = get_option('epic_membership_float_over_links', '');

        if (!empty($limited_links_string)) {

            $limited_links = array_map('trim', explode(',', $limited_links_string));

            $limited_links = array_filter($limited_links);



            foreach ($limited_links as $link) {

                $links[] = array(

                    'url' => $link,

                    'type' => 'limited',

                    'limit' => $this->get_link_limit($link)

                );

            }

        }



        // Get unlimited links

        $unlimited_links_string = get_option('epic_membership_float_over_unlimited_links', '');

        if (!empty($unlimited_links_string)) {

            $unlimited_links = array_map('trim', explode(',', $unlimited_links_string));

            $unlimited_links = array_filter($unlimited_links);



            foreach ($unlimited_links as $link) {

                $links[] = array(

                    'url' => $link,

                    'type' => 'unlimited',

                    'limit' => 0 // 0 means unlimited

                );

            }

        }



        return $links;

    }



    /**

     * Get the limit for a specific link

     */

    private function get_link_limit($link) {

        $default_limit = get_option('epic_membership_float_over_default_limit', 5);

        $per_link_limits_string = get_option('epic_membership_float_over_per_link_limits', '');



        if (empty($per_link_limits_string)) {

            return $default_limit;

        }



        // Parse per-link limits

        $lines = explode("\n", $per_link_limits_string);

        foreach ($lines as $line) {

            $line = trim($line);

            if (empty($line)) continue;



            $parts = explode('=', $line, 2);

            if (count($parts) === 2) {

                $url = trim($parts[0]);

                $limit = intval(trim($parts[1]));



                if ($url === $link && $limit > 0) {

                    return $limit;

                }

            }

        }



        return $default_limit;

    }

    

    /**

     * Get available links that haven't reached their limit

     */

    private function get_available_links($all_links, $ip_address) {

        global $wpdb;



        $table_name = $wpdb->prefix . 'epic_membership_float_over_links';

        $available_links = array();



        foreach ($all_links as $link_data) {

            $url = $link_data['url'];

            $type = $link_data['type'];

            $limit = $link_data['limit'];



            // Unlimited links are always available

            if ($type === 'unlimited') {

                $available_links[] = $url;

                continue;

            }



            // Check usage count for limited links

            $current_count = $wpdb->get_var($wpdb->prepare(

                "SELECT count FROM $table_name WHERE ip_address = %s AND link = %s",

                $ip_address,

                $url

            ));



            $current_count = intval($current_count);



            // Add to available if under limit or no record exists

            if ($current_count < $limit) {

                $available_links[] = $url;

            }

        }



        return $available_links;

    }



    /**

     * Reset limited links for an IP address

     */

    private function reset_limited_links($ip_address) {

        global $wpdb;



        $table_name = $wpdb->prefix . 'epic_membership_float_over_links';



        // Only reset limited links, keep unlimited links untouched

        $wpdb->query($wpdb->prepare(

            "DELETE FROM $table_name WHERE ip_address = %s AND is_unlimited = 0",

            $ip_address

        ));

    }



    /**

     * Update link count for tracking

     */

    private function update_link_count($link, $ip_address) {

        global $wpdb;



        $table_name = $wpdb->prefix . 'epic_membership_float_over_links';



        // Determine if this is an unlimited link

        $unlimited_links_string = get_option('epic_membership_float_over_unlimited_links', '');

        $unlimited_links = array();



        if (!empty($unlimited_links_string)) {

            $unlimited_links = array_map('trim', explode(',', $unlimited_links_string));

            $unlimited_links = array_filter($unlimited_links);

        }



        $is_unlimited = in_array($link, $unlimited_links) ? 1 : 0;

        $view_limit = $is_unlimited ? 0 : $this->get_link_limit($link);



        // Insert or update the record

        $wpdb->query($wpdb->prepare(

            "INSERT INTO $table_name (ip_address, link, count, view_limit, is_unlimited)

             VALUES (%s, %s, 1, %d, %d)

             ON DUPLICATE KEY UPDATE

                count = count + 1,

                view_limit = %d,

                is_unlimited = %d,

                updated_at = CURRENT_TIMESTAMP",

            $ip_address,

            $link,

            $view_limit,

            $is_unlimited,

            $view_limit,

            $is_unlimited

        ));

    }



    /**

     * Get user IP address

     */

    private function get_user_ip() {

        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {

            return $_SERVER['HTTP_CLIENT_IP'];

        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {

            return $_SERVER['HTTP_X_FORWARDED_FOR'];

        } else {

            return $_SERVER['REMOTE_ADDR'];

        }

    }

}

