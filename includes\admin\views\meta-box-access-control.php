<?php

/**

 * Meta box view for content access control

 */



if (!defined('ABSPATH')) {

    exit;

}

?>



<div class="epic-membership-meta-box">

    <table class="form-table">

        <tr>

            <td>

                <label for="epic_membership_access_type">

                    <strong><?php _e('Access Type', 'epic-membership'); ?></strong>

                </label>

            </td>

        </tr>

        <tr>

            <td>

                <select name="epic_membership_access_type" id="epic_membership_access_type" class="widefat">

                    <option value="public" <?php selected($access_type, 'public'); ?>>

                        <?php _e('Public (Everyone)', 'epic-membership'); ?>

                    </option>

                    <option value="members_only" <?php selected($access_type, 'members_only'); ?>>

                        <?php _e('Members Only', 'epic-membership'); ?>

                    </option>

                    <option value="scheduled" <?php selected($access_type, 'scheduled'); ?>>

                        <?php _e('Scheduled Release', 'epic-membership'); ?>

                    </option>

                </select>

            </td>

        </tr>

    </table>



    <!-- Members Only Settings -->

    <div id="members-only-settings" class="epic-membership-conditional-field" 

         style="<?php echo $access_type === 'members_only' ? '' : 'display: none;'; ?>">

        <table class="form-table">

            <tr>

                <td>

                    <label for="epic_membership_required_tier">

                        <strong><?php _e('Required Membership Tier', 'epic-membership'); ?></strong>

                    </label>

                </td>

            </tr>

            <tr>

                <td>

                    <select name="epic_membership_required_tier" id="epic_membership_required_tier" class="widefat">

                        <option value=""><?php _e('Select Tier', 'epic-membership'); ?></option>

                        <?php foreach ($tiers as $tier): ?>

                            <option value="<?php echo esc_attr($tier->id); ?>" 

                                    <?php selected($required_tier_id, $tier->id); ?>>

                                <?php echo esc_html($tier->name); ?> (Level <?php echo esc_html($tier->level); ?>)

                            </option>

                        <?php endforeach; ?>

                    </select>

                    <p class="description">

                        <?php _e('Users with this tier or higher will have access to this content.', 'epic-membership'); ?>

                    </p>

                </td>

            </tr>

        </table>

    </div>



    <!-- Scheduled Release Settings -->

    <div id="scheduled-release-settings" class="epic-membership-conditional-field"

         style="<?php echo $access_type === 'scheduled' ? '' : 'display: none;'; ?>">

        <table class="form-table">

            <tr>

                <td>

                    <label for="epic_membership_scheduled_release">

                        <strong><?php _e('Release Date & Time', 'epic-membership'); ?></strong>

                    </label>

                </td>

            </tr>

            <tr>

                <td>

                    <input type="datetime-local"

                           name="epic_membership_scheduled_release"

                           id="epic_membership_scheduled_release"

                           value="<?php echo esc_attr($scheduled_release ? date('Y-m-d\TH:i', strtotime($scheduled_release)) : ''); ?>"

                           class="widefat" />

                    <p class="description">

                        <?php printf(

                            __('Content will be publicly available after this date/time. Server timezone: %s', 'epic-membership'),

                            wp_timezone_string()

                        ); ?>

                    </p>

                </td>

            </tr>

            <tr>

                <td>

                    <label for="epic_membership_allowed_tiers">

                        <strong><?php _e('Early Access Tiers', 'epic-membership'); ?></strong>

                    </label>

                </td>

            </tr>

            <tr>

                <td>

                    <div class="epic-membership-tier-selection">

                        <?php

                        // Get current allowed tier IDs

                        $allowed_tier_ids = array();

                        if ($access_settings && $access_settings->allowed_tier_ids) {

                            $allowed_tier_ids = json_decode($access_settings->allowed_tier_ids, true);

                            if (!is_array($allowed_tier_ids)) {

                                $allowed_tier_ids = array();

                            }

                        }

                        ?>

                        <?php foreach ($tiers as $tier): ?>

                            <label class="epic-membership-tier-checkbox">

                                <input type="checkbox"

                                       name="epic_membership_allowed_tiers[]"

                                       value="<?php echo esc_attr($tier->id); ?>"

                                       <?php checked(in_array($tier->id, $allowed_tier_ids)); ?> />

                                <span class="tier-info">

                                    <strong><?php echo esc_html($tier->name); ?></strong>

                                    <small>(Level <?php echo esc_html($tier->level); ?>)</small>

                                </span>

                            </label>

                        <?php endforeach; ?>

                    </div>

                    <p class="description">

                        <?php _e('Select which membership tiers can access this content before the scheduled release time. If no tiers are selected, only the public release time will apply.', 'epic-membership'); ?>

                    </p>

                </td>

            </tr>

        </table>

    </div>



    <!-- Content Teaser Settings -->

    <div id="content-teaser-settings" class="epic-membership-conditional-field" 

         style="<?php echo in_array($access_type, array('members_only', 'scheduled')) ? '' : 'display: none;'; ?>">

        <table class="form-table">

            <tr>

                <td>

                    <label for="epic_membership_teaser_content">

                        <strong><?php _e('Content Teaser', 'epic-membership'); ?></strong>

                    </label>

                </td>

            </tr>

            <tr>

                <td>

                    <?php

                    wp_editor($teaser_content, 'epic_membership_teaser_content', array(

                        'textarea_name' => 'epic_membership_teaser_content',

                        'textarea_rows' => 5,

                        'media_buttons' => false,

                        'teeny' => true,

                        'quicktags' => false

                    ));

                    ?>

                    <p class="description">

                        <?php _e('This content will be shown to users who don\'t have access. Leave empty to show default message.', 'epic-membership'); ?>

                    </p>

                </td>

            </tr>

        </table>

    </div>



    <!-- Preview Section -->

    <div class="epic-membership-preview-section">

        <h4><?php _e('Access Preview', 'epic-membership'); ?></h4>

        <div id="epic-membership-access-preview" class="epic-membership-preview-content">

            <!-- Preview content will be populated by JavaScript -->

        </div>

    </div>

</div>



<style>

.epic-membership-meta-box .form-table {

    margin-top: 10px;

}



.epic-membership-meta-box .form-table td {

    padding: 8px 0;

}



.epic-membership-conditional-field {

    border-top: 1px solid #ddd;

    margin-top: 15px;

    padding-top: 15px;

}



.epic-membership-preview-section {

    margin-top: 20px;

    padding-top: 15px;

    border-top: 1px solid #ddd;

}



.epic-membership-preview-content {

    background: #f9f9f9;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 15px;

    min-height: 60px;

    font-style: italic;

    color: #666;

}



.epic-membership-preview-content.has-content {

    font-style: normal;

    color: #333;

}



.epic-membership-tier-selection {

    display: flex;

    flex-direction: column;

    gap: 8px;

    margin: 10px 0;

}



.epic-membership-tier-checkbox {

    display: flex;

    align-items: center;

    gap: 8px;

    padding: 8px 12px;

    border: 1px solid #ddd;

    border-radius: 4px;

    background: #f9f9f9;

    cursor: pointer;

    transition: all 0.2s ease;

}



.epic-membership-tier-checkbox:hover {

    background: #f0f0f0;

    border-color: #ccc;

}



.epic-membership-tier-checkbox input[type="checkbox"]:checked + .tier-info {

    font-weight: bold;

    color: #0073aa;

}



.epic-membership-tier-checkbox input[type="checkbox"] {

    margin: 0;

}



.tier-info {

    display: flex;

    flex-direction: column;

    gap: 2px;

}



.tier-info small {

    color: #666;

    font-size: 11px;

}

</style>



<script>

jQuery(document).ready(function($) {

    // Handle access type changes

    $('#epic_membership_access_type').on('change', function() {

        var accessType = $(this).val();

        

        // Hide all conditional fields

        $('.epic-membership-conditional-field').hide();

        

        // Show relevant fields

        if (accessType === 'members_only') {

            $('#members-only-settings, #content-teaser-settings').show();

        } else if (accessType === 'scheduled') {

            $('#scheduled-release-settings, #content-teaser-settings').show();

        }

        

        // Update preview

        updateAccessPreview();

    });

    

    // Handle form field changes for preview

    $('#epic_membership_required_tier, #epic_membership_scheduled_release').on('change', updateAccessPreview);

    $('input[name="epic_membership_allowed_tiers[]"]').on('change', updateAccessPreview);

    

    // Update preview on page load

    updateAccessPreview();

    

    function updateAccessPreview() {

        var accessType = $('#epic_membership_access_type').val();

        var preview = $('#epic-membership-access-preview');

        var previewText = '';

        

        switch (accessType) {

            case 'public':

                previewText = '<?php echo esc_js(__("This content is publicly accessible to all visitors.", "epic-membership")); ?>';

                break;

                

            case 'members_only':

                var tierSelect = $('#epic_membership_required_tier');

                var tierText = tierSelect.find('option:selected').text();

                if (tierSelect.val()) {

                    previewText = '<?php echo esc_js(__("This content requires", "epic-membership")); ?> ' + tierText + ' <?php echo esc_js(__("membership or higher.", "epic-membership")); ?>';

                } else {

                    previewText = '<?php echo esc_js(__("Please select a required membership tier.", "epic-membership")); ?>';

                }

                break;

                

            case 'scheduled':

                var releaseDate = $('#epic_membership_scheduled_release').val();

                var selectedTiers = [];

                $('input[name="epic_membership_allowed_tiers[]"]:checked').each(function() {

                    var tierLabel = $(this).siblings('.tier-info').find('strong').text();

                    selectedTiers.push(tierLabel);

                });



                if (releaseDate) {

                    var formattedDate = new Date(releaseDate).toLocaleString();

                    previewText = '<?php echo esc_js(__("This content will be publicly available on:", "epic-membership")); ?> ' + formattedDate;



                    if (selectedTiers.length > 0) {

                        previewText += '<br><strong><?php echo esc_js(__("Early access for:", "epic-membership")); ?></strong> ' + selectedTiers.join(', ');

                    }

                } else {

                    previewText = '<?php echo esc_js(__("Please set a release date and time.", "epic-membership")); ?>';

                }

                break;

        }

        

        preview.html(previewText);

        preview.toggleClass('has-content', previewText.length > 0);

    }

});

</script>

