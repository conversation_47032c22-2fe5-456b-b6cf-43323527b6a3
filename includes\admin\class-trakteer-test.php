<?php
/**
 * Trakteer Test Page for debugging
 */

if (!defined('ABSPATH')) {
    exit;
}

class Epic_Membership_Trakteer_Test {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'epic-membership',
            __('Trakteer Test', 'epic-membership'),
            __('Trakteer Test', 'epic-membership'),
            'manage_options',
            'epic-membership-trakteer-test',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page() {
        $database = new Epic_Membership_Database();

        // Handle force migration
        if (isset($_POST['force_migration']) && wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_force_migration')) {
            $this->force_currency_migration($database);
            echo '<div class="notice notice-success"><p>Database migration forced successfully!</p></div>';
        }

        // Handle fix currency column
        if (isset($_POST['fix_currency']) && wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_fix_currency')) {
            $this->fix_currency_column($database);
            echo '<div class="notice notice-success"><p>Currency column fixed successfully!</p></div>';
        }
        
        // Test database tables
        $table_transactions = $database->get_table('payment_transactions');
        $table_tiers = $database->get_table('tiers');
        $table_memberships = $database->get_table('user_memberships');
        
        // Get Trakteer settings
        $trakteer_enabled = get_option('epic_membership_trakteer_enabled', false);
        $trakteer_page_url = get_option('epic_membership_trakteer_page_url', '');
        $trakteer_verification_token = get_option('epic_membership_trakteer_verification_token', '');
        $trakteer_currency = get_option('epic_membership_trakteer_currency', 'IDR');
        $trakteer_debug_mode = get_option('epic_membership_trakteer_debug_mode', false);
        
        // Get tiers
        global $wpdb;
        $tiers = $wpdb->get_results("SELECT * FROM $table_tiers WHERE is_active = 1 ORDER BY level ASC");
        
        // Get recent transactions
        $recent_transactions = $wpdb->get_results("
            SELECT * FROM $table_transactions 
            WHERE payment_gateway = 'trakteer' 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        
        ?>
        <div class="wrap">
            <h1><?php _e('Trakteer Integration Test', 'epic-membership'); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e('This page helps you test and debug the Trakteer integration.', 'epic-membership'); ?></p>
            </div>

            <h2><?php _e('Database Migration', 'epic-membership'); ?></h2>
            <form method="post" action="">
                <?php wp_nonce_field('epic_membership_force_migration'); ?>
                <?php wp_nonce_field('epic_membership_fix_currency'); ?>
                <p><?php _e('If currency field is not saving, try fixing the database:', 'epic-membership'); ?></p>
                <button type="submit" name="force_migration" class="button button-secondary" onclick="return confirm('Are you sure you want to force database migration?')">
                    <?php _e('Force Database Migration', 'epic-membership'); ?>
                </button>
                <button type="submit" name="fix_currency" class="button button-primary" onclick="return confirm('This will add/fix the currency column. Continue?')">
                    <?php _e('Fix Currency Column', 'epic-membership'); ?>
                </button>
            </form>
            
            <h2><?php _e('Database Tables Status', 'epic-membership'); ?></h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Table', 'epic-membership'); ?></th>
                        <th><?php _e('Name', 'epic-membership'); ?></th>
                        <th><?php _e('Status', 'epic-membership'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>payment_transactions</td>
                        <td><?php echo esc_html($table_transactions); ?></td>
                        <td>
                            <?php 
                            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_transactions'");
                            if ($exists) {
                                echo '<span style="color: green;">✓ Exists</span>';
                            } else {
                                echo '<span style="color: red;">✗ Missing</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td>tiers</td>
                        <td><?php echo esc_html($table_tiers); ?></td>
                        <td>
                            <?php
                            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_tiers'");
                            if ($exists) {
                                echo '<span style="color: green;">✓ Exists</span>';

                                // Check if currency column exists
                                $currency_column = $wpdb->get_results("SHOW COLUMNS FROM $table_tiers LIKE 'currency'");
                                if (!empty($currency_column)) {
                                    echo '<br><small style="color: green;">✓ Currency column exists</small>';
                                } else {
                                    echo '<br><small style="color: red;">✗ Currency column missing</small>';
                                }
                            } else {
                                echo '<span style="color: red;">✗ Missing</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td>user_memberships</td>
                        <td><?php echo esc_html($table_memberships); ?></td>
                        <td>
                            <?php 
                            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_memberships'");
                            if ($exists) {
                                echo '<span style="color: green;">✓ Exists</span>';
                            } else {
                                echo '<span style="color: red;">✗ Missing</span>';
                            }
                            ?>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <h2><?php _e('Trakteer Settings', 'epic-membership'); ?></h2>
            <table class="wp-list-table widefat fixed striped">
                <tbody>
                    <tr>
                        <td><strong>Enabled</strong></td>
                        <td><?php echo $trakteer_enabled ? '<span style="color: green;">✓ Yes</span>' : '<span style="color: red;">✗ No</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Page URL</strong></td>
                        <td><?php echo esc_html($trakteer_page_url ?: 'Not set'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Verification Token</strong></td>
                        <td><?php echo $trakteer_verification_token ? '<span style="color: green;">✓ Set</span>' : '<span style="color: red;">✗ Not set</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Currency</strong></td>
                        <td><?php echo esc_html($trakteer_currency); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Debug Mode</strong></td>
                        <td><?php echo $trakteer_debug_mode ? '<span style="color: green;">✓ Enabled</span>' : '<span style="color: orange;">Disabled</span>'; ?></td>
                    </tr>
                </tbody>
            </table>
            
            <h2><?php _e('Available Tiers', 'epic-membership'); ?></h2>
            <?php if (empty($tiers)): ?>
                <div class="notice notice-warning">
                    <p><?php _e('No active tiers found. Please create membership tiers first.', 'epic-membership'); ?></p>
                </div>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th><?php _e('Name', 'epic-membership'); ?></th>
                            <th><?php _e('Level', 'epic-membership'); ?></th>
                            <th><?php _e('Price', 'epic-membership'); ?></th>
                            <th><?php _e('Duration', 'epic-membership'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tiers as $tier): ?>
                            <tr>
                                <td><?php echo esc_html($tier->id); ?></td>
                                <td><?php echo esc_html($tier->name); ?></td>
                                <td><?php echo esc_html($tier->level); ?></td>
                                <td>
                                    <?php
                                    $tier_currency = $tier->currency ?? 'USD';
                                    $currency_symbol = $tier_currency === 'IDR' ? 'Rp ' : '$';
                                    $formatted_price = $tier_currency === 'IDR' ? number_format($tier->price, 0, ',', '.') : number_format($tier->price, 2);
                                    echo esc_html($currency_symbol . $formatted_price . ' ' . $tier_currency);
                                    ?>
                                </td>
                                <td><?php echo esc_html($tier->duration_days ? $tier->duration_days . ' days' : 'Unlimited'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            
            <h2><?php _e('Recent Trakteer Transactions', 'epic-membership'); ?></h2>
            <?php if (empty($recent_transactions)): ?>
                <div class="notice notice-info">
                    <p><?php _e('No Trakteer transactions found yet.', 'epic-membership'); ?></p>
                </div>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th><?php _e('User', 'epic-membership'); ?></th>
                            <th><?php _e('Amount', 'epic-membership'); ?></th>
                            <th><?php _e('Status', 'epic-membership'); ?></th>
                            <th><?php _e('Date', 'epic-membership'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_transactions as $transaction): ?>
                            <tr>
                                <td><?php echo esc_html($transaction->id); ?></td>
                                <td>
                                    <?php 
                                    $user = get_user_by('ID', $transaction->user_id);
                                    echo $user ? esc_html($user->display_name) : 'User #' . $transaction->user_id;
                                    ?>
                                </td>
                                <td><?php echo esc_html(number_format($transaction->amount, 0, ',', '.') . ' ' . $transaction->currency); ?></td>
                                <td><?php echo esc_html($transaction->payment_status); ?></td>
                                <td><?php echo esc_html(mysql2date('Y-m-d H:i:s', $transaction->created_at)); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            
            <h2><?php _e('Test Payment Button', 'epic-membership'); ?></h2>
            <?php if ($trakteer_enabled && !empty($tiers)): ?>
                <div id="trakteer-test-buttons">
                    <?php foreach ($tiers as $tier): ?>
                        <div style="margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                            <?php
                            $tier_currency = $tier->currency ?? 'USD';
                            $currency_symbol = $tier_currency === 'IDR' ? 'Rp ' : '$';
                            $formatted_price = $tier_currency === 'IDR' ? number_format($tier->price, 0, ',', '.') : number_format($tier->price, 2);
                            ?>
                            <h4><?php echo esc_html($tier->name); ?> - <?php echo esc_html($currency_symbol . $formatted_price . ' ' . $tier_currency); ?></h4>
                            <button type="button" class="button button-primary epic-membership-trakteer-button"
                                    data-tier-id="<?php echo esc_attr($tier->id); ?>"
                                    data-tier-name="<?php echo esc_attr($tier->name); ?>"
                                    data-tier-price="<?php echo esc_attr($tier->price); ?>"
                                    data-currency="<?php echo esc_attr($tier_currency); ?>">
                                Test Trakteer Payment for <?php echo esc_html($tier->name); ?>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <script>
                jQuery(document).ready(function($) {
                    // Load Trakteer JavaScript if not already loaded
                    if (typeof epicMembershipTrakteer === 'undefined') {
                        $.getScript('<?php echo EPIC_MEMBERSHIP_PLUGIN_URL . 'assets/js/trakteer-integration.js'; ?>');
                        
                        // Set up localized data
                        window.epicMembershipTrakteer = {
                            ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
                            nonce: '<?php echo wp_create_nonce('epic_membership_trakteer_nonce'); ?>',
                            currency: '<?php echo esc_js($trakteer_currency); ?>',
                            debug: <?php echo $trakteer_debug_mode ? 'true' : 'false'; ?>
                        };
                    }
                });
                </script>
            <?php else: ?>
                <div class="notice notice-warning">
                    <p><?php _e('Trakteer is not enabled or no tiers available. Please configure Trakteer settings and create membership tiers first.', 'epic-membership'); ?></p>
                </div>
            <?php endif; ?>
            
            <h2><?php _e('Webhook Information', 'epic-membership'); ?></h2>
            <p><strong><?php _e('Webhook URL:', 'epic-membership'); ?></strong></p>
            <code><?php echo esc_url(admin_url('admin-ajax.php?action=epic_membership_trakteer_webhook')); ?></code>
            <p class="description"><?php _e('Use this URL in your Trakteer webhook settings.', 'epic-membership'); ?></p>
            
        </div>
        <?php
    }

    /**
     * Force currency migration
     */
    private function force_currency_migration($database) {
        global $wpdb;

        // Force database version reset to trigger migration
        update_option('epic_membership_db_version', '1.3.0');

        // Run create tables which will trigger migration
        $database->create_tables();

        error_log('Epic Membership: Forced currency migration completed');
    }

    /**
     * Fix currency column specifically
     */
    private function fix_currency_column($database) {
        global $wpdb;

        $table_tiers = $database->get_table('tiers');

        // Check if currency column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_tiers LIKE 'currency'");

        if (empty($column_exists)) {
            // Add currency column
            $sql = "ALTER TABLE $table_tiers ADD COLUMN currency varchar(3) NOT NULL DEFAULT 'USD' AFTER price";
            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('Epic Membership: Failed to add currency column: ' . $wpdb->last_error);
            } else {
                error_log('Epic Membership: Successfully added currency column');
            }
        } else {
            // Modify existing column to ensure it's NOT NULL
            $sql = "ALTER TABLE $table_tiers MODIFY COLUMN currency varchar(3) NOT NULL DEFAULT 'USD'";
            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('Epic Membership: Failed to modify currency column: ' . $wpdb->last_error);
            } else {
                error_log('Epic Membership: Successfully modified currency column');
            }
        }

        // Update existing tiers with default currency
        $default_currency = get_option('epic_membership_default_currency', 'USD');
        $update_result = $wpdb->query($wpdb->prepare(
            "UPDATE $table_tiers SET currency = %s WHERE currency IS NULL OR currency = '' OR currency = '0'",
            $default_currency
        ));

        error_log("Epic Membership: Updated $update_result tiers with default currency: $default_currency");
    }
}
