<?php
/**
 * Trakteer Payment Gateway for Epic Membership Plugin
 * Handles Trakteer webhook integration for automatic membership upgrades with IDR currency support
 */

if (!defined('ABSPATH')) {
    exit;
}

class Epic_Membership_Trakteer_Gateway {
    
    /**
     * Gateway ID
     */
    const GATEWAY_ID = 'trakteer';
    
    /**
     * Trakteer webhook verification token
     */
    private $verification_token;
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Gateway settings
     */
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Epic_Membership_Database();
        $this->load_settings();
        $this->verify_database_tables();
        $this->init_hooks();
    }
    
    /**
     * Load gateway settings
     */
    private function load_settings() {
        $this->settings = array(
            'enabled' => get_option('epic_membership_trakteer_enabled', false),
            'page_url' => get_option('epic_membership_trakteer_page_url', ''),
            'verification_token' => get_option('epic_membership_trakteer_verification_token', ''),
            'api_key' => get_option('epic_membership_trakteer_api_key', ''),
            'currency' => get_option('epic_membership_trakteer_currency', 'IDR'),
            'auto_upgrade' => get_option('epic_membership_trakteer_auto_upgrade', true),
            'debug_mode' => get_option('epic_membership_trakteer_debug_mode', false),
            'sandbox_mode' => get_option('epic_membership_trakteer_sandbox_mode', true),
            'webhook_secret' => get_option('epic_membership_trakteer_webhook_secret', '')
        );
        
        $this->verification_token = $this->settings['verification_token'];
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        if (!$this->settings['enabled']) {
            return;
        }
        
        // AJAX handlers for payment processing
        add_action('wp_ajax_epic_membership_trakteer_payment', array($this, 'handle_payment_request'));
        add_action('wp_ajax_nopriv_epic_membership_trakteer_payment', array($this, 'handle_payment_request'));
        
        // Webhook handler
        add_action('wp_ajax_epic_membership_trakteer_webhook', array($this, 'handle_webhook'));
        add_action('wp_ajax_nopriv_epic_membership_trakteer_webhook', array($this, 'handle_webhook'));
        
        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Add payment buttons to membership tiers - DISABLED to prevent duplicate buttons
        // add_action('epic_membership_tier_payment_buttons', array($this, 'add_payment_button'), 10, 2);
    }
    
    /**
     * Enqueue frontend scripts
     */
    public function enqueue_scripts() {
        if (!$this->settings['enabled']) {
            return;
        }
        
        wp_enqueue_script(
            'epic-membership-trakteer',
            EPIC_MEMBERSHIP_PLUGIN_URL . 'assets/js/trakteer-integration.js',
            array('jquery'),
            EPIC_MEMBERSHIP_VERSION,
            true
        );
        
        wp_localize_script('epic-membership-trakteer', 'epicMembershipTrakteer', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('epic_membership_trakteer_nonce'),
            'currency' => $this->settings['currency'],
            'debug' => $this->settings['debug_mode']
        ));
    }
    
    /**
     * Add Trakteer payment button to tier display
     */
    public function add_payment_button($tier_id, $tier) {
        if (!$this->settings['enabled'] || !is_user_logged_in()) {
            return;
        }
        
        $current_user = wp_get_current_user();
        $user_tier = $this->get_user_current_tier($current_user->ID);
        
        // Don't show button if user already has this tier or higher
        if ($user_tier && $user_tier->level >= $tier->level) {
            return;
        }
        
        $button_text = sprintf(
            __('Support with Trakteer (%s %s)', 'epic-membership'),
            number_format($tier->price, 0, ',', '.'),
            $this->settings['currency']
        );
        
        echo '<div class="epic-membership-trakteer-button-container">';
        echo '<button type="button" class="epic-membership-trakteer-button" ';
        echo 'data-tier-id="' . esc_attr($tier_id) . '" ';
        echo 'data-tier-name="' . esc_attr($tier->name) . '" ';
        echo 'data-tier-price="' . esc_attr($tier->price) . '" ';
        echo 'data-currency="' . esc_attr($this->settings['currency']) . '">';
        echo esc_html($button_text);
        echo '</button>';
        echo '</div>';
    }
    
    /**
     * Handle payment request
     */
    public function handle_payment_request() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_trakteer_nonce')) {
                throw new Exception(__('Security check failed', 'epic-membership'));
            }
            
            // Check if user is logged in
            if (!is_user_logged_in()) {
                throw new Exception(__('You must be logged in to make a payment', 'epic-membership'));
            }
            
            $user_id = get_current_user_id();
            $tier_id = intval($_POST['tier_id']);
            
            if ($this->settings['debug_mode']) {
                error_log('Trakteer payment request for user ' . $user_id . ', tier ' . $tier_id);
            }
            
            // Get tier information
            $tier = $this->get_tier($tier_id);
            if (!$tier) {
                $this->log_error('Tier not found for ID: ' . $tier_id);
                throw new Exception(__('Membership tier not found', 'epic-membership'));
            }
            
            if ($this->settings['debug_mode']) {
                error_log('Tier found: ' . print_r($tier, true));
            }
            
            // Create pending transaction record
            $transaction_id = $this->create_pending_transaction($user_id, $tier_id, $tier);
            
            // Generate Trakteer payment URL
            $payment_url = $this->generate_trakteer_payment_url($tier, $transaction_id);
            
            if ($this->settings['debug_mode']) {
                error_log('Trakteer payment URL generated: ' . $payment_url);
            }
            
            wp_send_json_success(array(
                'payment_url' => $payment_url,
                'transaction_id' => $transaction_id
            ));
            
        } catch (Exception $e) {
            $this->log_error('Trakteer payment request error: ' . $e->getMessage());
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Create pending transaction record
     */
    private function create_pending_transaction($user_id, $tier_id, $tier) {
        global $wpdb;
        
        $table_transactions = $this->database->get_table('payment_transactions');
        
        $transaction_data = array(
            'user_id' => $user_id,
            'tier_id' => $tier_id,
            'payment_gateway' => self::GATEWAY_ID,
            'transaction_id' => 'trakteer_' . time() . '_' . $user_id,
            'amount' => $tier->price,
            'currency' => $tier->currency ?? $this->settings['currency'],
            'payment_status' => 'pending',
            'transaction_data' => json_encode(array(
                'tier_name' => $tier->name,
                'tier_level' => $tier->level,
                'duration_days' => $tier->duration_days,
                'tier_currency' => $tier->currency ?? $this->settings['currency']
            ))
        );
        
        $this->log_debug('Creating transaction with data: ' . json_encode($transaction_data));
        $this->log_debug('Table name: ' . $table_transactions);

        $wpdb->insert($table_transactions, $transaction_data);

        if ($wpdb->last_error) {
            $this->log_error('Database insert error: ' . $wpdb->last_error);
            $this->log_error('Table name: ' . $table_transactions);
            $this->log_error('Transaction data: ' . json_encode($transaction_data));
            throw new Exception('Database error: ' . $wpdb->last_error);
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Generate Trakteer payment URL
     */
    private function generate_trakteer_payment_url($tier, $transaction_id) {
        $base_url = $this->settings['page_url'];

        if (empty($base_url)) {
            throw new Exception(__('Trakteer page URL not configured', 'epic-membership'));
        }

        // Ensure URL starts with https://trakteer.id/
        if (strpos($base_url, 'trakteer.id/') === false) {
            $base_url = 'https://trakteer.id/' . ltrim($base_url, '/');
        }

        // Trakteer URL parameters for better amount suggestion
        $params = array(
            'ref' => 'membership_' . $transaction_id,
            'type' => 'donation'
        );

        // Add amount parameter - Trakteer will suggest this amount but user can change it
        if ($tier->price > 0) {
            $params['amount'] = number_format($tier->price, 0, '', '');
        }

        $payment_url = add_query_arg($params, $base_url);

        return $payment_url;
    }
    
    /**
     * Get tier information
     */
    private function get_tier($tier_id) {
        global $wpdb;
        
        $table_tiers = $this->database->get_table('tiers');
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_tiers WHERE id = %d AND is_active = 1",
            $tier_id
        ));
    }
    
    /**
     * Get user's current tier
     */
    private function get_user_current_tier($user_id) {
        global $wpdb;
        
        $table_memberships = $this->database->get_table('user_memberships');
        $table_tiers = $this->database->get_table('tiers');
        
        return $wpdb->get_row($wpdb->prepare("
            SELECT t.* FROM $table_tiers t
            INNER JOIN $table_memberships m ON t.id = m.tier_id
            WHERE m.user_id = %d AND m.is_active = 1
            ORDER BY t.level DESC
            LIMIT 1
        ", $user_id));
    }
    
    /**
     * Log error message
     */
    private function log_error($message) {
        if ($this->settings['debug_mode']) {
            error_log('Epic Membership Trakteer Gateway Error: ' . $message);
        }
    }
    
    /**
     * Log debug message
     */
    private function log_debug($message) {
        if ($this->settings['debug_mode']) {
            error_log('Epic Membership Trakteer Gateway Debug: ' . $message);
        }
    }

    /**
     * Verify database tables exist
     */
    private function verify_database_tables() {
        global $wpdb;

        $table_transactions = $this->database->get_table('payment_transactions');
        $table_tiers = $this->database->get_table('tiers');
        $table_memberships = $this->database->get_table('user_memberships');

        // Check if tables exist
        $tables_to_check = array(
            'payment_transactions' => $table_transactions,
            'tiers' => $table_tiers,
            'user_memberships' => $table_memberships
        );

        foreach ($tables_to_check as $table_key => $table_name) {
            if (empty($table_name)) {
                $this->log_error("Table name for '$table_key' is empty");
                continue;
            }

            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            if (!$table_exists) {
                $this->log_error("Table '$table_name' does not exist");
                // Try to create tables
                $this->database->create_tables();
            } else {
                $this->log_debug("Table '$table_name' exists");
            }
        }
    }

    /**
     * Handle Trakteer webhook
     */
    public function handle_webhook() {
        try {
            $raw_body = file_get_contents('php://input');
            $webhook_data = json_decode($raw_body, true);

            if (!$webhook_data) {
                throw new Exception('Invalid webhook data');
            }

            // Verify webhook authenticity
            $this->verify_webhook($webhook_data, $raw_body);

            // Process webhook event
            $this->process_webhook_event($webhook_data);

            wp_send_json_success('Webhook processed');

        } catch (Exception $e) {
            $this->log_error('Trakteer Webhook error: ' . $e->getMessage());
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * Verify webhook authenticity based on Trakteer documentation
     */
    private function verify_webhook($webhook_data, $raw_body) {
        // Check for required webhook signature header
        $signature = $_SERVER['HTTP_X_TRAKTEER_SIGNATURE'] ?? '';

        if (empty($signature)) {
            throw new Exception('Missing webhook signature');
        }

        // Verify webhook secret if configured
        if (!empty($this->settings['webhook_secret'])) {
            $expected_signature = hash_hmac('sha256', $raw_body, $this->settings['webhook_secret']);

            if (!hash_equals($signature, $expected_signature)) {
                throw new Exception('Invalid webhook signature');
            }
        }

        // Verify verification token if present in data
        if (isset($webhook_data['verification_token'])) {
            if ($webhook_data['verification_token'] !== $this->verification_token) {
                throw new Exception('Invalid verification token');
            }
        }

        return true;
    }

    /**
     * Process webhook event based on Trakteer webhook structure
     */
    private function process_webhook_event($webhook_data) {
        // Log webhook data for debugging
        $this->log_debug('Trakteer webhook received: ' . json_encode($webhook_data));

        // Extract payment information based on actual Trakteer webhook format
        $transaction_id = sanitize_text_field($webhook_data['transaction_id'] ?? '');
        $type = sanitize_text_field($webhook_data['type'] ?? '');
        $supporter_name = sanitize_text_field($webhook_data['supporter_name'] ?? '');
        $supporter_message = sanitize_text_field($webhook_data['supporter_message'] ?? '');
        $unit = sanitize_text_field($webhook_data['unit'] ?? '');
        $quantity = intval($webhook_data['quantity'] ?? 0);
        $price = floatval($webhook_data['price'] ?? 0);
        $net_amount = floatval($webhook_data['net_amount'] ?? 0);
        $created_at = sanitize_text_field($webhook_data['created_at'] ?? '');

        // Only process tip payments
        if ($type !== 'tip') {
            $this->log_debug('Webhook event not a tip payment: ' . $type);
            return;
        }

        // Use net_amount as the actual payment amount
        $payment_amount = $net_amount > 0 ? $net_amount : $price;

        if ($payment_amount <= 0) {
            throw new Exception('Invalid payment amount');
        }

        // Since Trakteer doesn't provide supporter email in webhook,
        // we need to find user by supporter name or use a different approach
        $user = $this->find_user_by_supporter_info($supporter_name, $transaction_id);
        if (!$user) {
            $this->log_error('Trakteer payment received but no matching user found for supporter: ' . $supporter_name);
            // Store the payment for manual processing
            $this->store_unmatched_payment($webhook_data);
            return;
        }

        // Determine appropriate membership tier based on payment amount
        $tier = $this->get_tier_by_amount($payment_amount);
        if (!$tier) {
            $this->log_error('Trakteer payment received but no matching tier found for amount: ' . $payment_amount);
            return;
        }

        // Create transaction record for Trakteer payment
        $membership_transaction_id = $this->create_trakteer_transaction($user->ID, $tier->id, $webhook_data);

        // Process membership activation
        $this->process_successful_payment($membership_transaction_id, $webhook_data);
    }

    /**
     * Find user by supporter information
     */
    private function find_user_by_supporter_info($supporter_name, $transaction_id) {
        global $wpdb;

        // First, try to find by display name
        $user = get_user_by('login', $supporter_name);
        if ($user) {
            return $user;
        }

        // Try to find by display name
        $users = get_users(array(
            'meta_query' => array(
                array(
                    'key' => 'display_name',
                    'value' => $supporter_name,
                    'compare' => '='
                )
            )
        ));

        if (!empty($users)) {
            return $users[0];
        }

        // Try to find by nickname
        $users = get_users(array(
            'meta_query' => array(
                array(
                    'key' => 'nickname',
                    'value' => $supporter_name,
                    'compare' => '='
                )
            )
        ));

        if (!empty($users)) {
            return $users[0];
        }

        // Check if there's a pending transaction that matches
        $table_transactions = $this->database->get_table('payment_transactions');
        $pending_transaction = $wpdb->get_row($wpdb->prepare("
            SELECT t.*, u.user_email, u.display_name
            FROM $table_transactions t
            JOIN {$wpdb->users} u ON t.user_id = u.ID
            WHERE t.payment_gateway = %s
            AND t.payment_status = 'pending'
            AND t.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY t.created_at DESC
            LIMIT 1
        ", self::GATEWAY_ID));

        if ($pending_transaction) {
            return get_user_by('ID', $pending_transaction->user_id);
        }

        return null;
    }

    /**
     * Store unmatched payment for manual processing
     */
    private function store_unmatched_payment($webhook_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'epic_membership_unmatched_payments';

        // Create table if it doesn't exist
        $this->create_unmatched_payments_table();

        $wpdb->insert(
            $table_name,
            array(
                'gateway' => self::GATEWAY_ID,
                'transaction_id' => sanitize_text_field($webhook_data['transaction_id'] ?? ''),
                'supporter_name' => sanitize_text_field($webhook_data['supporter_name'] ?? ''),
                'amount' => floatval($webhook_data['net_amount'] ?? $webhook_data['price'] ?? 0),
                'currency' => $this->settings['currency'],
                'webhook_data' => json_encode($webhook_data),
                'created_at' => current_time('mysql'),
                'status' => 'unmatched'
            )
        );

        $this->log_error('Trakteer payment stored for manual processing: ' . json_encode($webhook_data));
    }

    /**
     * Create unmatched payments table
     */
    private function create_unmatched_payments_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'epic_membership_unmatched_payments';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            gateway varchar(50) NOT NULL,
            transaction_id varchar(255) NOT NULL,
            supporter_name varchar(255) NOT NULL,
            amount decimal(10,2) NOT NULL,
            currency varchar(10) NOT NULL,
            webhook_data longtext NOT NULL,
            created_at datetime NOT NULL,
            status varchar(20) NOT NULL DEFAULT 'unmatched',
            processed_at datetime NULL,
            user_id int(11) NULL,
            PRIMARY KEY (id),
            KEY gateway (gateway),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Update existing transaction status
     */
    private function update_transaction_status($transaction_id, $webhook_data) {
        global $wpdb;

        $table_transactions = $this->database->get_table('payment_transactions');

        $update_data = array(
            'payment_status' => 'completed',
            'transaction_id' => sanitize_text_field($webhook_data['transaction_id'] ?? ''),
            'processed_at' => current_time('mysql'),
            'webhook_data' => json_encode($webhook_data)
        );

        $wpdb->update(
            $table_transactions,
            $update_data,
            array('id' => $transaction_id),
            array('%s', '%s', '%s', '%s'),
            array('%d')
        );

        if ($wpdb->last_error) {
            throw new Exception('Database error updating transaction: ' . $wpdb->last_error);
        }
    }

    /**
     * Create transaction record for Trakteer payment
     */
    private function create_trakteer_transaction($user_id, $tier_id, $webhook_data) {
        global $wpdb;

        $table_transactions = $this->database->get_table('payment_transactions');

        $tier = $this->get_tier($tier_id);

        // Use net_amount as the actual payment amount
        $payment_amount = floatval($webhook_data['net_amount'] ?? $webhook_data['price'] ?? 0);

        $transaction_data = array(
            'user_id' => $user_id,
            'tier_id' => $tier_id,
            'payment_gateway' => self::GATEWAY_ID,
            'transaction_id' => sanitize_text_field($webhook_data['transaction_id'] ?? ''),
            'amount' => $payment_amount,
            'currency' => $tier->currency ?? $this->settings['currency'],
            'payment_status' => 'completed',
            'processed_at' => current_time('mysql'),
            'transaction_data' => json_encode($webhook_data),
            'webhook_data' => json_encode($webhook_data)
        );

        $wpdb->insert($table_transactions, $transaction_data);

        if ($wpdb->last_error) {
            throw new Exception('Database error creating transaction: ' . $wpdb->last_error);
        }

        return $wpdb->insert_id;
    }

    /**
     * Get tier by payment amount
     */
    private function get_tier_by_amount($amount) {
        global $wpdb;

        $table_tiers = $this->database->get_table('tiers');

        // Find the tier with the closest price match (within 10% tolerance)
        $tiers = $wpdb->get_results("
            SELECT * FROM $table_tiers
            WHERE is_active = 1
            ORDER BY ABS(price - $amount) ASC
        ");

        if (empty($tiers)) {
            return null;
        }

        $best_tier = $tiers[0];
        $price_difference = abs($best_tier->price - $amount);
        $tolerance = $best_tier->price * 0.1; // 10% tolerance

        // Return tier if amount is within tolerance
        if ($price_difference <= $tolerance) {
            return $best_tier;
        }

        return null;
    }

    /**
     * Process successful payment and activate membership
     */
    private function process_successful_payment($transaction_id, $webhook_data) {
        global $wpdb;

        // Get transaction details
        $table_transactions = $this->database->get_table('transactions');
        $transaction = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_transactions WHERE id = %d",
            $transaction_id
        ));

        if (!$transaction) {
            throw new Exception('Transaction not found');
        }

        // Get tier information
        $tier = $this->get_tier($transaction->tier_id);
        if (!$tier) {
            throw new Exception('Tier not found');
        }

        // Activate or upgrade membership
        $this->activate_membership($transaction->user_id, $tier, $transaction);

        // Send notification email if configured
        $this->send_payment_notification($transaction, $tier, $webhook_data);

        $this->log_debug('Membership activated for user ' . $transaction->user_id . ' with tier ' . $tier->name);
    }

    /**
     * Activate membership for user
     */
    private function activate_membership($user_id, $tier, $transaction) {
        global $wpdb;

        $table_memberships = $this->database->get_table('user_memberships');

        // Calculate end date
        $end_date = null;
        if ($tier->duration_days > 0) {
            $end_date = date('Y-m-d H:i:s', strtotime('+' . $tier->duration_days . ' days'));
        }

        // Check if user already has an active membership
        $existing_membership = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM $table_memberships
            WHERE user_id = %d AND is_active = 1
        ", $user_id));

        if ($existing_membership) {
            // Update existing membership if new tier is higher level
            $existing_tier = $this->get_tier($existing_membership->tier_id);

            if ($tier->level > $existing_tier->level) {
                $wpdb->update(
                    $table_memberships,
                    array(
                        'tier_id' => $tier->id,
                        'end_date' => $end_date,
                        'updated_at' => current_time('mysql')
                    ),
                    array('id' => $existing_membership->id),
                    array('%d', '%s', '%s'),
                    array('%d')
                );
            } else {
                // Extend existing membership duration
                if ($tier->duration_days > 0) {
                    $current_end = $existing_membership->end_date ?: current_time('mysql');
                    $new_end_date = date('Y-m-d H:i:s', strtotime($current_end . ' +' . $tier->duration_days . ' days'));

                    $wpdb->update(
                        $table_memberships,
                        array(
                            'end_date' => $new_end_date,
                            'updated_at' => current_time('mysql')
                        ),
                        array('id' => $existing_membership->id),
                        array('%s', '%s'),
                        array('%d')
                    );
                }
            }
        } else {
            // Create new membership
            $membership_data = array(
                'user_id' => $user_id,
                'tier_id' => $tier->id,
                'start_date' => current_time('mysql'),
                'end_date' => $end_date,
                'is_active' => 1,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            );

            $wpdb->insert($table_memberships, $membership_data);
        }

        if ($wpdb->last_error) {
            throw new Exception('Database error activating membership: ' . $wpdb->last_error);
        }
    }

    /**
     * Send payment notification email
     */
    private function send_payment_notification($transaction, $tier, $webhook_data) {
        $user = get_user_by('ID', $transaction->user_id);
        if (!$user) {
            return;
        }

        $subject = sprintf(
            __('[%s] Membership Activated - %s', 'epic-membership'),
            get_bloginfo('name'),
            $tier->name
        );

        $message = sprintf(
            __('Hello %s,

Thank you for your support via Trakteer! Your membership has been activated.

Membership Details:
- Tier: %s
- Amount: %s %s
- Transaction ID: %s

You can now access premium content on our website.

Best regards,
%s Team', 'epic-membership'),
            $user->display_name,
            $tier->name,
            number_format($transaction->amount, 0, ',', '.'),
            $transaction->currency,
            $transaction->gateway_transaction_id,
            get_bloginfo('name')
        );

        wp_mail($user->user_email, $subject, $message);
    }
}
