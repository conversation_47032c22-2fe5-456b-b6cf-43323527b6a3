<?php

/**

 * Main admin class for Epic Membership Plugin

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Admin {

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Add meta boxes to posts and pages

        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));



        // Save meta box data

        add_action('save_post', array($this, 'save_meta_box_data'));



        // Add admin notices

        add_action('admin_notices', array($this, 'admin_notices'));



        // Handle AJAX requests

        add_action('wp_ajax_epic_membership_admin_action', array($this, 'handle_ajax_request'));

    }







    /**

     * Add meta boxes to posts and pages

     */

    public function add_meta_boxes() {

        $post_types = get_post_types(array('public' => true), 'names');

        

        foreach ($post_types as $post_type) {

            add_meta_box(

                'epic-membership-access-control',

                __('Membership Access Control', 'epic-membership'),

                array($this, 'render_access_control_meta_box'),

                $post_type,

                'side',

                'high'

            );

        }

    }

    

    /**

     * Render access control meta box

     */

    public function render_access_control_meta_box($post) {

        // Add nonce field

        wp_nonce_field('epic_membership_meta_box', 'epic_membership_meta_box_nonce');

        

        // Get current settings

        $database = new Epic_Membership_Database();

        $access_settings = $database->get_content_access($post->ID);

        

        // Get available tiers

        global $wpdb;

        $tiers_table = $database->get_table('tiers');

        $tiers = $wpdb->get_results("SELECT * FROM $tiers_table WHERE is_active = 1 ORDER BY level ASC");

        

        // Default values

        $access_type = $access_settings ? $access_settings->access_type : 'public';

        $required_tier_id = $access_settings ? $access_settings->required_tier_id : '';

        $scheduled_release = $access_settings ? $access_settings->scheduled_release : '';

        $teaser_content = $access_settings ? $access_settings->teaser_content : '';

        

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/meta-box-access-control.php';

    }

    

    /**

     * Save meta box data

     */

    public function save_meta_box_data($post_id) {

        // Check if nonce is valid

        if (!isset($_POST['epic_membership_meta_box_nonce']) || 

            !wp_verify_nonce($_POST['epic_membership_meta_box_nonce'], 'epic_membership_meta_box')) {

            return;

        }

        

        // Check if user has permission to edit post

        if (!current_user_can('edit_post', $post_id)) {

            return;

        }

        

        // Don't save on autosave

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {

            return;

        }

        

        // Get form data

        $access_type = sanitize_text_field($_POST['epic_membership_access_type'] ?? 'public');

        $required_tier_id = intval($_POST['epic_membership_required_tier'] ?? 0);

        $scheduled_release = sanitize_text_field($_POST['epic_membership_scheduled_release'] ?? '');

        $teaser_content = wp_kses_post($_POST['epic_membership_teaser_content'] ?? '');



        // Handle allowed tiers for scheduled releases

        $allowed_tier_ids = null;

        if ($access_type === 'scheduled' && isset($_POST['epic_membership_allowed_tiers']) && is_array($_POST['epic_membership_allowed_tiers'])) {

            $allowed_tiers = array_map('intval', $_POST['epic_membership_allowed_tiers']);

            $allowed_tiers = array_filter($allowed_tiers, function($tier_id) {

                return $tier_id > 0;

            });

            if (!empty($allowed_tiers)) {

                $allowed_tier_ids = json_encode(array_values($allowed_tiers));

            }

        }



        // Validate scheduled release date

        if ($scheduled_release && !strtotime($scheduled_release)) {

            $scheduled_release = '';

        }

        

        // Save to database

        global $wpdb;

        $database = new Epic_Membership_Database();

        $table_name = $database->get_table('content_access');

        

        // Check if record exists

        $existing = $wpdb->get_row($wpdb->prepare(

            "SELECT id FROM $table_name WHERE post_id = %d",

            $post_id

        ));

        

        $data = array(

            'post_id' => $post_id,

            'access_type' => $access_type,

            'required_tier_id' => $required_tier_id > 0 ? $required_tier_id : null,

            'scheduled_release' => $scheduled_release ? $scheduled_release : null,

            'release_timezone' => wp_timezone_string(),

            'allowed_tier_ids' => $allowed_tier_ids,

            'teaser_content' => $teaser_content,

            'is_active' => 1

        );

        

        if ($existing) {

            // Update existing record

            $wpdb->update($table_name, $data, array('post_id' => $post_id));

        } else {

            // Insert new record

            $wpdb->insert($table_name, $data);

        }

    }

    

    /**

     * Display admin notices

     */

    public function admin_notices() {

        // Check for success/error messages

        if (isset($_GET['epic_membership_message'])) {

            $message_type = sanitize_text_field($_GET['epic_membership_message']);

            

            switch ($message_type) {

                case 'tier_created':

                    echo '<div class="notice notice-success is-dismissible"><p>' . 

                         __('Membership tier created successfully.', 'epic-membership') . '</p></div>';

                    break;

                case 'tier_updated':

                    echo '<div class="notice notice-success is-dismissible"><p>' . 

                         __('Membership tier updated successfully.', 'epic-membership') . '</p></div>';

                    break;

                case 'tier_deleted':

                    echo '<div class="notice notice-success is-dismissible"><p>' . 

                         __('Membership tier deleted successfully.', 'epic-membership') . '</p></div>';

                    break;

                case 'user_upgraded':

                    echo '<div class="notice notice-success is-dismissible"><p>' . 

                         __('User membership upgraded successfully.', 'epic-membership') . '</p></div>';

                    break;

                case 'error':

                    echo '<div class="notice notice-error is-dismissible"><p>' . 

                         __('An error occurred. Please try again.', 'epic-membership') . '</p></div>';

                    break;

            }

        }

    }

    

    /**

     * Handle AJAX requests

     */

    public function handle_ajax_request() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_die('Security check failed');

        }

        

        // Check user capabilities

        if (!current_user_can('manage_options')) {

            wp_die('Insufficient permissions');

        }

        

        $action_type = sanitize_text_field($_POST['action_type'] ?? '');

        

        switch ($action_type) {

            case 'get_tier_details':

                $this->ajax_get_tier_details();

                break;

            case 'update_user_membership':

                $this->ajax_update_user_membership();

                break;

            case 'get_membership_stats':

                $this->ajax_get_membership_stats();

                break;

            default:

                wp_send_json_error('Invalid action');

        }

    }

    

    /**

     * AJAX: Get tier details

     */

    private function ajax_get_tier_details() {

        $tier_id = intval($_POST['tier_id'] ?? 0);

        

        if (!$tier_id) {

            wp_send_json_error('Invalid tier ID');

        }

        

        global $wpdb;

        $database = new Epic_Membership_Database();

        $table_name = $database->get_table('tiers');

        

        $tier = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $table_name WHERE id = %d",

            $tier_id

        ));

        

        if ($tier) {

            wp_send_json_success($tier);

        } else {

            wp_send_json_error('Tier not found');

        }

    }

    

    /**

     * AJAX: Update user membership

     */

    private function ajax_update_user_membership() {

        $user_id = intval($_POST['user_id'] ?? 0);

        $tier_id = intval($_POST['tier_id'] ?? 0);

        $duration_days = intval($_POST['duration_days'] ?? 30);

        

        if (!$user_id || !$tier_id) {

            wp_send_json_error('Invalid parameters');

        }

        

        // Update user membership

        $result = $this->update_user_membership($user_id, $tier_id, $duration_days);

        

        if ($result) {

            wp_send_json_success('Membership updated successfully');

        } else {

            wp_send_json_error('Failed to update membership');

        }

    }

    

    /**

     * AJAX: Get membership statistics

     */

    private function ajax_get_membership_stats() {

        global $wpdb;

        $database = new Epic_Membership_Database();

        

        $stats = array(

            'total_users' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}"),

            'active_memberships' => $wpdb->get_var("SELECT COUNT(*) FROM {$database->get_table('user_memberships')} WHERE is_active = 1"),

            'expired_memberships' => $wpdb->get_var("SELECT COUNT(*) FROM {$database->get_table('user_memberships')} WHERE is_active = 0"),

            'protected_content' => $wpdb->get_var("SELECT COUNT(*) FROM {$database->get_table('content_access')} WHERE access_type != 'public'")

        );

        

        wp_send_json_success($stats);

    }

    

    /**

     * Update user membership

     */

    private function update_user_membership($user_id, $tier_id, $duration_days) {

        global $wpdb;

        $database = new Epic_Membership_Database();

        $table_name = $database->get_table('user_memberships');

        

        // Deactivate existing memberships

        $wpdb->update(

            $table_name,

            array('is_active' => 0),

            array('user_id' => $user_id, 'is_active' => 1)

        );

        

        // Create new membership

        $start_date = current_time('mysql');

        $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + $duration_days days"));

        

        return $wpdb->insert($table_name, array(

            'user_id' => $user_id,

            'tier_id' => $tier_id,

            'start_date' => $start_date,

            'end_date' => $end_date,

            'is_active' => 1,

            'created_by' => get_current_user_id()

        ));

    }



    /**

     * Admin dashboard page

     */

    public function admin_dashboard_page() {

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/dashboard.php';

    }



    /**

     * Admin tiers page

     */

    public function admin_tiers_page() {

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/tiers.php';

    }



    /**

     * Admin users page

     */

    public function admin_users_page() {

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/users.php';

    }



    /**

     * Admin content page

     */

    public function admin_content_page() {

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/content.php';

    }



    /**

     * Admin settings page

     */

    public function admin_settings_page() {

        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/settings.php';

    }

}

