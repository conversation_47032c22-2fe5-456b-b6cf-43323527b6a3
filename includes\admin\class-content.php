<?php

/**

 * Content Management Class for Epic Membership Plugin

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Content {

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Handle bulk content operations

        add_action('admin_post_epic_membership_bulk_content_update', array($this, 'bulk_content_update'));

        

        // AJAX handlers

        add_action('wp_ajax_epic_membership_get_content_stats', array($this, 'ajax_get_content_stats'));

        add_action('wp_ajax_epic_membership_update_content_access', array($this, 'ajax_update_content_access'));

    }

    

    /**

     * Get protected content statistics

     */

    public function get_content_statistics() {

        global $wpdb;

        $content_access_table = $this->database->get_table('content_access');

        

        return array(

            'total_protected' => $wpdb->get_var("SELECT COUNT(*) FROM $content_access_table WHERE is_active = 1"),

            'members_only' => $wpdb->get_var("SELECT COUNT(*) FROM $content_access_table WHERE access_type = 'members_only' AND is_active = 1"),

            'scheduled' => $wpdb->get_var("SELECT COUNT(*) FROM $content_access_table WHERE access_type = 'scheduled' AND is_active = 1"),

            'public' => $wpdb->get_var("SELECT COUNT(*) FROM $content_access_table WHERE access_type = 'public' AND is_active = 1")

        );

    }

    

    /**

     * Get protected content list

     */

    public function get_protected_content($args = array()) {

        global $wpdb;

        

        $defaults = array(

            'access_type' => '',

            'tier_id' => '',

            'limit' => 20,

            'offset' => 0

        );

        

        $args = wp_parse_args($args, $defaults);

        $content_access_table = $this->database->get_table('content_access');

        $tiers_table = $this->database->get_table('tiers');

        

        $where = array('ca.is_active = 1');

        $where_values = array();

        

        if (!empty($args['access_type'])) {

            $where[] = 'ca.access_type = %s';

            $where_values[] = $args['access_type'];

        }

        

        if (!empty($args['tier_id'])) {

            $where[] = 'ca.required_tier_id = %d';

            $where_values[] = intval($args['tier_id']);

        }

        

        $where_clause = 'WHERE ' . implode(' AND ', $where);

        $limit_clause = sprintf('LIMIT %d OFFSET %d', intval($args['limit']), intval($args['offset']));

        

        $sql = "SELECT ca.*, p.post_title, p.post_type, p.post_status, t.name as tier_name, t.level as tier_level

                FROM $content_access_table ca

                LEFT JOIN {$wpdb->posts} p ON ca.post_id = p.ID

                LEFT JOIN $tiers_table t ON ca.required_tier_id = t.id

                $where_clause

                ORDER BY ca.created_at DESC

                $limit_clause";

        

        if (!empty($where_values)) {

            return $wpdb->get_results($wpdb->prepare($sql, $where_values));

        } else {

            return $wpdb->get_results($sql);

        }

    }

    

    /**

     * Update content access settings

     */

    public function update_content_access($post_id, $access_data) {

        global $wpdb;

        $content_access_table = $this->database->get_table('content_access');

        

        // Sanitize data

        $data = array(

            'post_id' => intval($post_id),

            'access_type' => sanitize_text_field($access_data['access_type']),

            'required_tier_id' => !empty($access_data['required_tier_id']) ? intval($access_data['required_tier_id']) : null,

            'scheduled_release' => !empty($access_data['scheduled_release']) ? sanitize_text_field($access_data['scheduled_release']) : null,

            'release_timezone' => wp_timezone_string(),

            'teaser_content' => wp_kses_post($access_data['teaser_content'] ?? ''),

            'is_active' => 1

        );

        

        // Check if record exists

        $existing = $wpdb->get_row($wpdb->prepare(

            "SELECT id FROM $content_access_table WHERE post_id = %d",

            $post_id

        ));

        

        if ($existing) {

            return $wpdb->update($content_access_table, $data, array('post_id' => $post_id));

        } else {

            return $wpdb->insert($content_access_table, $data);

        }

    }

    

    /**

     * Remove content protection

     */

    public function remove_content_protection($post_id) {

        global $wpdb;

        $content_access_table = $this->database->get_table('content_access');

        

        return $wpdb->update(

            $content_access_table,

            array('is_active' => 0),

            array('post_id' => intval($post_id))

        );

    }

    

    /**

     * Handle bulk content updates

     */

    public function bulk_content_update() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_bulk_content')) {

            wp_die(__('Security check failed.', 'epic-membership'));

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }

        

        $post_ids = array_map('intval', $_POST['post_ids'] ?? array());

        $action = sanitize_text_field($_POST['bulk_action']);

        

        $updated_count = 0;

        

        foreach ($post_ids as $post_id) {

            if ($action === 'remove_protection') {

                if ($this->remove_content_protection($post_id)) {

                    $updated_count++;

                }

            } elseif ($action === 'update_access') {

                $access_data = array(

                    'access_type' => sanitize_text_field($_POST['bulk_access_type'] ?? 'public'),

                    'required_tier_id' => intval($_POST['bulk_tier_id'] ?? 0),

                    'teaser_content' => ''

                );

                

                if ($this->update_content_access($post_id, $access_data)) {

                    $updated_count++;

                }

            }

        }

        

        wp_redirect(add_query_arg(array(

            'page' => 'epic-membership-content',

            'epic_membership_message' => 'bulk_updated',

            'updated_count' => $updated_count

        ), admin_url('admin.php')));

        exit;

    }

    

    /**

     * AJAX: Get content statistics

     */

    public function ajax_get_content_stats() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $stats = $this->get_content_statistics();

        wp_send_json_success($stats);

    }

    

    /**

     * AJAX: Update content access

     */

    public function ajax_update_content_access() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('edit_posts')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $post_id = intval($_POST['post_id']);

        $access_data = $_POST['access_data'];

        

        $result = $this->update_content_access($post_id, $access_data);

        

        if ($result) {

            wp_send_json_success('Content access updated');

        } else {

            wp_send_json_error('Failed to update content access');

        }

    }

    

    /**

     * Get content access by post type

     */

    public function get_content_by_post_type() {

        global $wpdb;

        $content_access_table = $this->database->get_table('content_access');

        

        return $wpdb->get_results("

            SELECT p.post_type, COUNT(ca.id) as protected_count

            FROM $content_access_table ca

            LEFT JOIN {$wpdb->posts} p ON ca.post_id = p.ID

            WHERE ca.is_active = 1

            GROUP BY p.post_type

            ORDER BY protected_count DESC

        ");

    }

    

    /**

     * Get scheduled content that should be released

     */

    public function get_content_to_release() {

        global $wpdb;

        $content_access_table = $this->database->get_table('content_access');

        

        return $wpdb->get_results("

            SELECT ca.*, p.post_title

            FROM $content_access_table ca

            LEFT JOIN {$wpdb->posts} p ON ca.post_id = p.ID

            WHERE ca.access_type = 'scheduled'

            AND ca.scheduled_release <= NOW()

            AND ca.is_active = 1

            ORDER BY ca.scheduled_release ASC

        ");

    }

    

    /**

     * Release scheduled content (convert to public)

     */

    public function release_scheduled_content($post_id) {

        global $wpdb;

        $content_access_table = $this->database->get_table('content_access');

        

        return $wpdb->update(

            $content_access_table,

            array('access_type' => 'public'),

            array('post_id' => intval($post_id), 'access_type' => 'scheduled')

        );

    }

}

