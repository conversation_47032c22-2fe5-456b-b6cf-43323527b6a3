/**
 * Ko-fi Instructions Styles
 */

/* Main Instructions Container */
.epic-kofi-instructions-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.kofi-instructions-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border-left: 4px solid #13c3ff;
}

.kofi-instructions-header .kofi-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.kofi-instructions-header h2 {
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 28px;
}

.kofi-instructions-header p {
    color: #6c757d;
    margin: 0;
    font-size: 16px;
}

/* Pricing Section */
.kofi-pricing-section {
    margin-bottom: 32px;
}

.kofi-pricing-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    text-align: center;
}

.kofi-pricing-table {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.pricing-tier {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.pricing-tier:hover {
    border-color: #13c3ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(19, 195, 255, 0.2);
}

.tier-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.tier-price {
    font-size: 24px;
    font-weight: bold;
    color: #13c3ff;
    margin-bottom: 12px;
}

.tier-description {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Steps Section */
.kofi-steps-section {
    margin-bottom: 32px;
}

.kofi-steps-section h3 {
    color: #2c3e50;
    margin-bottom: 24px;
    font-size: 20px;
    text-align: center;
}

.kofi-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border-left: 4px solid #13c3ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-number {
    background: #13c3ff;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content h4 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    font-size: 16px;
}

.step-content p {
    color: #6c757d;
    margin: 0;
    line-height: 1.5;
}

/* Important Notice */
.kofi-important-notice {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 32px;
    display: flex;
    align-items: flex-start;
}

.notice-icon {
    font-size: 24px;
    margin-right: 16px;
    flex-shrink: 0;
}

.notice-content h4 {
    color: #856404;
    margin: 0 0 8px 0;
    font-size: 16px;
}

.notice-content p {
    color: #856404;
    margin: 0;
    line-height: 1.5;
}

/* FAQ Section */
.kofi-faq-section {
    margin-bottom: 32px;
}

.kofi-faq-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    text-align: center;
}

.kofi-faq-list {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
}

.faq-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.faq-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.faq-question {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 15px;
}

.faq-answer {
    color: #6c757d;
    line-height: 1.5;
    font-size: 14px;
}

/* Support Section */
.kofi-support-section {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.kofi-support-section a {
    color: #13c3ff;
    text-decoration: none;
    font-weight: 500;
}

.kofi-support-section a:hover {
    text-decoration: underline;
}

/* Quick Guide Styles */
.epic-kofi-quick-guide {
    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
    border: 1px solid #b3d9ff;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
}

.quick-guide-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    color: #0066cc;
    font-size: 16px;
}

.quick-guide-header .kofi-icon {
    margin-right: 8px;
    font-size: 20px;
}

.quick-steps {
    margin: 0 0 16px 0;
    padding-left: 20px;
}

.quick-steps li {
    margin-bottom: 8px;
    color: #2c3e50;
    line-height: 1.4;
}

.quick-note {
    background: rgba(255, 255, 255, 0.7);
    padding: 12px;
    border-radius: 8px;
    color: #0066cc;
}

/* Compact Style */
.epic-kofi-quick-guide.compact {
    padding: 16px;
    font-size: 14px;
}

.epic-kofi-quick-guide.compact .quick-guide-header {
    font-size: 14px;
    margin-bottom: 12px;
}

.epic-kofi-quick-guide.compact .quick-steps {
    margin-bottom: 12px;
}

.epic-kofi-quick-guide.compact .quick-steps li {
    margin-bottom: 6px;
}

.epic-kofi-quick-guide.compact .quick-note {
    padding: 8px;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .epic-kofi-instructions-container {
        padding: 16px;
    }
    
    .kofi-instructions-header {
        padding: 20px;
    }
    
    .kofi-instructions-header h2 {
        font-size: 24px;
    }
    
    .kofi-pricing-table {
        grid-template-columns: 1fr;
    }
    
    .kofi-step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin: 0 auto 16px auto;
    }
    
    .kofi-important-notice {
        flex-direction: column;
        text-align: center;
    }
    
    .notice-icon {
        margin: 0 0 12px 0;
    }
}

@media (max-width: 480px) {
    .kofi-instructions-header .kofi-icon {
        font-size: 36px;
    }
    
    .kofi-instructions-header h2 {
        font-size: 20px;
    }
    
    .tier-price {
        font-size: 20px;
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
}
