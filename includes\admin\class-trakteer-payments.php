<?php
/**
 * Trakteer Payments Admin Management
 * Handles unmatched payments and manual processing
 */

if (!defined('ABSPATH')) {
    exit;
}

class Epic_Membership_Trakteer_Payments_Admin {
    
    /**
     * Database instance
     */
    private $database;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new Epic_Membership_Database();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_epic_membership_process_unmatched_payment', array($this, 'process_unmatched_payment'));
        add_action('wp_ajax_epic_membership_ignore_unmatched_payment', array($this, 'ignore_unmatched_payment'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'epic-membership',
            __('Trakteer Payments', 'epic-membership'),
            __('Trakteer Payments', 'epic-membership'),
            'manage_options',
            'epic-membership-trakteer-payments',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        $unmatched_payments = $this->get_unmatched_payments();
        ?>
        <div class="wrap">
            <h1><?php _e('Trakteer Payments Management', 'epic-membership'); ?></h1>
            
            <?php if (empty($unmatched_payments)): ?>
                <div class="notice notice-success">
                    <p><?php _e('No unmatched Trakteer payments found. All payments have been processed successfully!', 'epic-membership'); ?></p>
                </div>
            <?php else: ?>
                <div class="notice notice-warning">
                    <p><?php printf(__('Found %d unmatched Trakteer payments that need manual processing.', 'epic-membership'), count($unmatched_payments)); ?></p>
                </div>
                
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'epic-membership'); ?></th>
                            <th><?php _e('Transaction ID', 'epic-membership'); ?></th>
                            <th><?php _e('Supporter Name', 'epic-membership'); ?></th>
                            <th><?php _e('Amount', 'epic-membership'); ?></th>
                            <th><?php _e('Message', 'epic-membership'); ?></th>
                            <th><?php _e('Actions', 'epic-membership'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($unmatched_payments as $payment): ?>
                            <?php 
                            $webhook_data = json_decode($payment->webhook_data, true);
                            $message = isset($webhook_data['supporter_message']) ? $webhook_data['supporter_message'] : '';
                            ?>
                            <tr>
                                <td><?php echo esc_html(mysql2date('Y-m-d H:i:s', $payment->created_at)); ?></td>
                                <td><?php echo esc_html($payment->transaction_id); ?></td>
                                <td><?php echo esc_html($payment->supporter_name); ?></td>
                                <td><?php echo esc_html(number_format($payment->amount, 0, ',', '.') . ' ' . $payment->currency); ?></td>
                                <td><?php echo esc_html($message); ?></td>
                                <td>
                                    <select class="user-select" data-payment-id="<?php echo esc_attr($payment->id); ?>">
                                        <option value=""><?php _e('Select User', 'epic-membership'); ?></option>
                                        <?php
                                        $users = get_users(array('orderby' => 'display_name'));
                                        foreach ($users as $user) {
                                            echo '<option value="' . esc_attr($user->ID) . '">' . esc_html($user->display_name . ' (' . $user->user_email . ')') . '</option>';
                                        }
                                        ?>
                                    </select>
                                    <button type="button" class="button button-primary process-payment" data-payment-id="<?php echo esc_attr($payment->id); ?>">
                                        <?php _e('Process', 'epic-membership'); ?>
                                    </button>
                                    <button type="button" class="button ignore-payment" data-payment-id="<?php echo esc_attr($payment->id); ?>">
                                        <?php _e('Ignore', 'epic-membership'); ?>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('.process-payment').on('click', function() {
                var paymentId = $(this).data('payment-id');
                var userId = $('.user-select[data-payment-id="' + paymentId + '"]').val();
                
                if (!userId) {
                    alert('<?php _e('Please select a user first.', 'epic-membership'); ?>');
                    return;
                }
                
                if (!confirm('<?php _e('Are you sure you want to process this payment?', 'epic-membership'); ?>')) {
                    return;
                }
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'epic_membership_process_unmatched_payment',
                        payment_id: paymentId,
                        user_id: userId,
                        nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    }
                });
            });
            
            $('.ignore-payment').on('click', function() {
                var paymentId = $(this).data('payment-id');
                
                if (!confirm('<?php _e('Are you sure you want to ignore this payment?', 'epic-membership'); ?>')) {
                    return;
                }
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'epic_membership_ignore_unmatched_payment',
                        payment_id: paymentId,
                        nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Get unmatched payments
     */
    private function get_unmatched_payments() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'epic_membership_unmatched_payments';
        
        return $wpdb->get_results("
            SELECT * FROM $table_name 
            WHERE status = 'unmatched' 
            ORDER BY created_at DESC
        ");
    }
    
    /**
     * Process unmatched payment
     */
    public function process_unmatched_payment() {
        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $payment_id = intval($_POST['payment_id']);
        $user_id = intval($_POST['user_id']);
        
        try {
            global $wpdb;
            
            $table_name = $wpdb->prefix . 'epic_membership_unmatched_payments';
            $payment = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $payment_id));
            
            if (!$payment) {
                throw new Exception('Payment not found');
            }
            
            $webhook_data = json_decode($payment->webhook_data, true);
            
            // Find appropriate tier based on amount
            $trakteer_gateway = new Epic_Membership_Trakteer_Gateway();
            $tier = $this->get_tier_by_amount($payment->amount);
            
            if (!$tier) {
                throw new Exception('No matching tier found for amount: ' . $payment->amount);
            }
            
            // Create transaction
            $table_transactions = $this->database->get_table('payment_transactions');
            $transaction_data = array(
                'user_id' => $user_id,
                'tier_id' => $tier->id,
                'payment_gateway' => 'trakteer',
                'transaction_id' => $payment->transaction_id,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'payment_status' => 'completed',
                'processed_at' => current_time('mysql'),
                'transaction_data' => $payment->webhook_data,
                'webhook_data' => $payment->webhook_data
            );
            
            $wpdb->insert($table_transactions, $transaction_data);
            $transaction_id = $wpdb->insert_id;
            
            // Activate membership
            $this->activate_membership($user_id, $tier);
            
            // Mark payment as processed
            $wpdb->update(
                $table_name,
                array(
                    'status' => 'processed',
                    'processed_at' => current_time('mysql'),
                    'user_id' => $user_id
                ),
                array('id' => $payment_id)
            );
            
            wp_send_json_success('Payment processed successfully');
            
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Ignore unmatched payment
     */
    public function ignore_unmatched_payment() {
        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $payment_id = intval($_POST['payment_id']);
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'epic_membership_unmatched_payments';
        
        $wpdb->update(
            $table_name,
            array('status' => 'ignored'),
            array('id' => $payment_id)
        );
        
        wp_send_json_success('Payment ignored');
    }
    
    /**
     * Get tier by amount
     */
    private function get_tier_by_amount($amount) {
        global $wpdb;
        
        $table_tiers = $this->database->get_table('tiers');
        
        $tiers = $wpdb->get_results("
            SELECT * FROM $table_tiers 
            WHERE is_active = 1 
            ORDER BY ABS(price - $amount) ASC
        ");
        
        if (empty($tiers)) {
            return null;
        }
        
        $best_tier = $tiers[0];
        $price_difference = abs($best_tier->price - $amount);
        $tolerance = $best_tier->price * 0.1; // 10% tolerance
        
        if ($price_difference <= $tolerance) {
            return $best_tier;
        }
        
        return null;
    }
    
    /**
     * Activate membership
     */
    private function activate_membership($user_id, $tier) {
        global $wpdb;
        
        $table_memberships = $this->database->get_table('user_memberships');
        
        $end_date = null;
        if ($tier->duration_days > 0) {
            $end_date = date('Y-m-d H:i:s', strtotime('+' . $tier->duration_days . ' days'));
        }
        
        $existing_membership = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM $table_memberships 
            WHERE user_id = %d AND is_active = 1
        ", $user_id));
        
        if ($existing_membership) {
            $existing_tier = $wpdb->get_row($wpdb->prepare("
                SELECT * FROM {$this->database->get_table('tiers')} WHERE id = %d
            ", $existing_membership->tier_id));
            
            if ($tier->level > $existing_tier->level) {
                $wpdb->update(
                    $table_memberships,
                    array(
                        'tier_id' => $tier->id,
                        'end_date' => $end_date,
                        'updated_at' => current_time('mysql')
                    ),
                    array('id' => $existing_membership->id)
                );
            }
        } else {
            $membership_data = array(
                'user_id' => $user_id,
                'tier_id' => $tier->id,
                'start_date' => current_time('mysql'),
                'end_date' => $end_date,
                'is_active' => 1,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            );
            
            $wpdb->insert($table_memberships, $membership_data);
        }
    }
}
