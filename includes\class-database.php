<?php

/**

 * Database management class for Epic Membership Plugin

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Database {

    

    /**

     * Database version

     */

    const DB_VERSION = '1.4.0';

    

    /**

     * Table names

     */

    private $tables = array();

    

    /**

     * Constructor

     */

    public function __construct() {

        global $wpdb;

        

        $this->tables = array(

            'tiers' => $wpdb->prefix . 'epic_membership_tiers',

            'user_memberships' => $wpdb->prefix . 'epic_user_memberships',

            'content_access' => $wpdb->prefix . 'epic_content_access',

            'access_logs' => $wpdb->prefix . 'epic_access_logs',

            'ad_units' => $wpdb->prefix . 'epic_ad_units',

            'payment_transactions' => $wpdb->prefix . 'epic_payment_transactions'

        );

    }

    

    /**

     * Get table name

     */

    public function get_table($table_key) {

        return isset($this->tables[$table_key]) ? $this->tables[$table_key] : null;

    }

    

    /**

     * Create all database tables

     */

    public function create_tables() {

        $this->create_tiers_table();

        $this->create_user_memberships_table();

        $this->create_content_access_table();

        $this->create_access_logs_table();

        $this->create_ad_units_table();

        $this->create_payment_transactions_table();

        $this->create_float_over_links_table();



        // Update database version

        update_option('epic_membership_db_version', self::DB_VERSION);

    }

    

    /**

     * Create membership tiers table

     */

    private function create_tiers_table() {

        global $wpdb;

        

        $table_name = $this->tables['tiers'];

        $charset_collate = $wpdb->get_charset_collate();

        

        $sql = "CREATE TABLE $table_name (

            id int(11) NOT NULL AUTO_INCREMENT,

            name varchar(100) NOT NULL,

            slug varchar(100) NOT NULL,

            description text,

            level int(11) NOT NULL DEFAULT 0,

            capabilities longtext,

            price decimal(10,2) NOT NULL DEFAULT 0.00,

            currency varchar(3) NOT NULL DEFAULT 'USD',

            duration_days int(11) DEFAULT NULL,

            is_active tinyint(1) NOT NULL DEFAULT 1,

            created_at datetime DEFAULT CURRENT_TIMESTAMP,

            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            UNIQUE KEY slug (slug),

            KEY level (level),

            KEY is_active (is_active),

            KEY name (name)

        ) $charset_collate;";

        

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);

    }

    

    /**

     * Create user memberships table

     */

    private function create_user_memberships_table() {

        global $wpdb;

        

        $table_name = $this->tables['user_memberships'];

        $charset_collate = $wpdb->get_charset_collate();

        

        $sql = "CREATE TABLE $table_name (

            id int(11) NOT NULL AUTO_INCREMENT,

            user_id bigint(20) unsigned NOT NULL,

            tier_id int(11) NOT NULL,

            start_date datetime NOT NULL,

            end_date datetime NULL,

            is_active tinyint(1) NOT NULL DEFAULT 1,

            auto_renew tinyint(1) NOT NULL DEFAULT 0,

            payment_status varchar(20) DEFAULT 'pending',

            notes text,

            created_by bigint(20) unsigned DEFAULT NULL,

            created_at datetime DEFAULT CURRENT_TIMESTAMP,

            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            KEY user_id (user_id),

            KEY tier_id (tier_id),

            KEY is_active (is_active),

            KEY end_date (end_date),

            KEY payment_status (payment_status),

            KEY start_date (start_date)

        ) $charset_collate;";

        

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);

    }

    

    /**

     * Create content access table

     */

    private function create_content_access_table() {

        global $wpdb;

        

        $table_name = $this->tables['content_access'];

        $charset_collate = $wpdb->get_charset_collate();

        

        $sql = "CREATE TABLE $table_name (

            id int(11) NOT NULL AUTO_INCREMENT,

            post_id bigint(20) unsigned NOT NULL,

            access_type varchar(20) NOT NULL DEFAULT 'public',

            required_tier_id int(11) DEFAULT NULL,

            scheduled_release datetime NULL,

            release_timezone varchar(50) DEFAULT NULL,

            allowed_tier_ids longtext DEFAULT NULL,

            teaser_content text,

            is_active tinyint(1) NOT NULL DEFAULT 1,

            created_at datetime DEFAULT CURRENT_TIMESTAMP,

            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            UNIQUE KEY post_id (post_id),

            KEY access_type (access_type),

            KEY required_tier_id (required_tier_id),

            KEY scheduled_release (scheduled_release),

            KEY is_active (is_active)

        ) $charset_collate;";

        

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);

    }

    

    /**

     * Create access logs table for analytics

     */

    private function create_access_logs_table() {

        global $wpdb;

        

        $table_name = $this->tables['access_logs'];

        $charset_collate = $wpdb->get_charset_collate();

        

        $sql = "CREATE TABLE $table_name (

            id int(11) NOT NULL AUTO_INCREMENT,

            user_id bigint(20) unsigned DEFAULT NULL,

            post_id bigint(20) unsigned NOT NULL,

            access_granted tinyint(1) NOT NULL DEFAULT 0,

            user_tier_id int(11) DEFAULT NULL,

            required_tier_id int(11) DEFAULT NULL,

            access_type varchar(20) NOT NULL,

            ip_address varchar(45),

            user_agent text,

            accessed_at datetime DEFAULT CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            KEY user_id (user_id),

            KEY post_id (post_id),

            KEY access_granted (access_granted),

            KEY accessed_at (accessed_at),

            KEY access_type (access_type)

        ) $charset_collate;";

        

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);

    }

    

    /**

     * Insert default membership tiers

     */

    public function insert_default_tiers() {

        global $wpdb;

        

        $table_name = $this->tables['tiers'];

        

        // Check if tiers already exist

        $existing_tiers = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");

        if ($existing_tiers > 0) {

            return;

        }

        

        $default_tiers = array(

            array(

                'name' => 'Free',

                'slug' => 'free',

                'description' => 'Basic free membership with limited access',

                'level' => 0,

                'capabilities' => json_encode(array('read_free_content')),

                'price' => 0.00,

                'duration_days' => null,

                'is_active' => 1

            ),

            array(

                'name' => 'Premium',

                'slug' => 'premium',

                'description' => 'Premium membership with full access and ad-free experience',

                'level' => 10,

                'capabilities' => json_encode(array('read_free_content', 'read_premium_content', 'ad_free_experience', 'early_access')),

                'price' => 9.99,

                'duration_days' => 30,

                'is_active' => 1

            ),

            array(

                'name' => 'VIP',

                'slug' => 'vip',

                'description' => 'VIP membership with exclusive content and priority support',

                'level' => 20,

                'capabilities' => json_encode(array('read_free_content', 'read_premium_content', 'read_vip_content', 'ad_free_experience', 'priority_support', 'early_access')),

                'price' => 19.99,

                'duration_days' => 30,

                'is_active' => 1

            )

        );

        

        foreach ($default_tiers as $tier) {

            $wpdb->insert($table_name, $tier);

        }

    }

    

    /**

     * Check if database needs updating

     */

    public function needs_update() {

        $current_version = get_option('epic_membership_db_version', '0.0.0');

        return version_compare($current_version, self::DB_VERSION, '<');

    }



    /**

     * Update database schema

     */

    public function update_database() {

        $current_version = get_option('epic_membership_db_version', '0.0.0');



        // If this is a fresh install or major update, create/update tables

        if ($this->needs_update()) {

            $this->create_tables();



            // Only insert default tiers if this is a fresh install

            if (version_compare($current_version, '1.0.0', '<')) {

                $this->insert_default_tiers();

            }

        }



        // Update to version 1.1.0 - Add allowed_tier_ids column

        if (version_compare($current_version, '1.1.0', '<')) {

            $this->migrate_to_1_1_0();

        }



        // Update to version 1.2.0 - Add early access capability to default tiers

        if (version_compare($current_version, '1.2.0', '<')) {

            $this->migrate_to_1_2_0();

        }



        // Update to version 1.4.0 - Add currency column to tiers table

        if (version_compare($current_version, '1.4.0', '<')) {

            $this->migrate_to_1_4_0();

        }



        // Update database version

        update_option('epic_membership_db_version', self::DB_VERSION);

    }



    /**

     * Migration to version 1.1.0

     * Adds allowed_tier_ids column to content_access table

     */

    private function migrate_to_1_1_0() {

        global $wpdb;

        $table_name = $this->tables['content_access'];



        // Check if column already exists

        $column_exists = $wpdb->get_results($wpdb->prepare(

            "SHOW COLUMNS FROM $table_name LIKE %s",

            'allowed_tier_ids'

        ));



        if (empty($column_exists)) {

            // Add the new column

            $wpdb->query("ALTER TABLE $table_name ADD COLUMN allowed_tier_ids longtext DEFAULT NULL AFTER release_timezone");

        }

    }



    /**

     * Migration to version 1.2.0

     * Updates default tiers to include early access capability

     */

    private function migrate_to_1_2_0() {

        global $wpdb;

        $table_name = $this->tables['tiers'];



        // Update Premium tier to include early_access capability

        $premium_capabilities = json_encode(array('read_free_content', 'read_premium_content', 'ad_free_experience', 'early_access'));

        $wpdb->update(

            $table_name,

            array('capabilities' => $premium_capabilities),

            array('slug' => 'premium')

        );



        // Update VIP tier to include early_access capability

        $vip_capabilities = json_encode(array('read_free_content', 'read_premium_content', 'read_vip_content', 'ad_free_experience', 'priority_support', 'early_access'));

        $wpdb->update(

            $table_name,

            array('capabilities' => $vip_capabilities),

            array('slug' => 'vip')

        );

    }



    /**

     * Get content access settings with tier information

     */

    public function get_content_access_with_tiers($post_id) {

        global $wpdb;

        $content_access_table = $this->get_table('content_access');

        $tiers_table = $this->get_table('tiers');



        $result = $wpdb->get_row($wpdb->prepare("

            SELECT ca.*, t.name as tier_name, t.level as tier_level

            FROM $content_access_table ca

            LEFT JOIN $tiers_table t ON ca.required_tier_id = t.id

            WHERE ca.post_id = %d AND ca.is_active = 1

        ", $post_id));



        // If we have allowed_tier_ids, decode them and get tier information

        if ($result && $result->allowed_tier_ids) {

            $allowed_tier_ids = json_decode($result->allowed_tier_ids, true);

            if (is_array($allowed_tier_ids) && !empty($allowed_tier_ids)) {

                $placeholders = implode(',', array_fill(0, count($allowed_tier_ids), '%d'));

                $allowed_tiers = $wpdb->get_results($wpdb->prepare("

                    SELECT id, name, level

                    FROM $tiers_table

                    WHERE id IN ($placeholders)

                    ORDER BY level ASC

                ", $allowed_tier_ids));



                $result->allowed_tiers = $allowed_tiers;

            }

        }



        return $result;

    }



    /**

     * Check if user tier is allowed for scheduled content

     */

    public function is_user_tier_allowed_for_scheduled($user_tier_id, $allowed_tier_ids_json) {

        if (empty($allowed_tier_ids_json)) {

            return false;

        }



        $allowed_tier_ids = json_decode($allowed_tier_ids_json, true);

        if (!is_array($allowed_tier_ids)) {

            return false;

        }



        // First check if user's tier is directly in the allowed list

        if (in_array($user_tier_id, $allowed_tier_ids)) {

            return true;

        }



        // If not directly allowed, check hierarchical access

        // Higher level tiers can access content restricted to lower level tiers

        global $wpdb;

        $tiers_table = $this->get_table('tiers');



        // Get user's tier level

        $user_tier = $wpdb->get_row($wpdb->prepare(

            "SELECT level FROM $tiers_table WHERE id = %d",

            $user_tier_id

        ));



        if (!$user_tier) {

            return false;

        }



        // Get the minimum level from allowed tiers

        $placeholders = implode(',', array_fill(0, count($allowed_tier_ids), '%d'));

        $min_allowed_level = $wpdb->get_var($wpdb->prepare(

            "SELECT MIN(level) FROM $tiers_table WHERE id IN ($placeholders)",

            $allowed_tier_ids

        ));



        // User can access if their level is >= minimum allowed level

        return $user_tier->level >= $min_allowed_level;

    }



    /**

     * Check if user's tier has early access capability

     */

    public function user_has_early_access($user_id) {

        return $this->user_has_capability($user_id, 'early_access');

    }



    /**

     * Check if user's tier has a specific capability

     */

    public function user_has_capability($user_id, $capability) {

        $user_membership = $this->get_user_membership($user_id);



        if (!$user_membership || !$user_membership->is_active) {

            return false;

        }



        // Check if membership is expired

        if ($user_membership->end_date && strtotime($user_membership->end_date) <= time()) {

            return false;

        }



        // Parse tier capabilities

        $capabilities = json_decode($user_membership->capabilities, true);

        if (!is_array($capabilities)) {

            return false;

        }



        return in_array($capability, $capabilities);

    }

    

    /**

     * Drop all plugin tables (for uninstall)

     */

    public function drop_tables() {

        global $wpdb;

        

        // Disable foreign key checks temporarily

        $wpdb->query('SET FOREIGN_KEY_CHECKS = 0');

        

        foreach ($this->tables as $table) {

            $wpdb->query("DROP TABLE IF EXISTS $table");

        }

        

        // Re-enable foreign key checks

        $wpdb->query('SET FOREIGN_KEY_CHECKS = 1');

        

        // Remove database version option

        delete_option('epic_membership_db_version');

    }

    

    /**

     * Get user's current membership

     */

    public function get_user_membership($user_id) {

        global $wpdb;

        

        $table_memberships = $this->tables['user_memberships'];

        $table_tiers = $this->tables['tiers'];

        

        $sql = "SELECT m.*, t.name as tier_name, t.slug as tier_slug, t.level as tier_level, t.price as tier_price, t.capabilities

                FROM $table_memberships m

                LEFT JOIN $table_tiers t ON m.tier_id = t.id

                WHERE m.user_id = %d

                AND m.is_active = 1

                AND (m.end_date IS NULL OR m.end_date > NOW())

                ORDER BY t.level DESC

                LIMIT 1";

        

        return $wpdb->get_row($wpdb->prepare($sql, $user_id));

    }

    

    /**

     * Get content access settings

     */

    public function get_content_access($post_id) {

        global $wpdb;

        

        $table_name = $this->tables['content_access'];

        

        $sql = "SELECT ca.*, t.name as tier_name, t.level as tier_level

                FROM $table_name ca

                LEFT JOIN {$this->tables['tiers']} t ON ca.required_tier_id = t.id

                WHERE ca.post_id = %d AND ca.is_active = 1";

        

        return $wpdb->get_row($wpdb->prepare($sql, $post_id));

    }

    

    /**

     * Log access attempt

     */

    public function log_access($user_id, $post_id, $access_granted, $access_type, $user_tier_id = null, $required_tier_id = null) {

        global $wpdb;

        

        $table_name = $this->tables['access_logs'];

        

        $data = array(

            'user_id' => $user_id,

            'post_id' => $post_id,

            'access_granted' => $access_granted ? 1 : 0,

            'access_type' => $access_type,

            'user_tier_id' => $user_tier_id,

            'required_tier_id' => $required_tier_id,

            'ip_address' => $this->get_user_ip(),

            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''

        );

        

        return $wpdb->insert($table_name, $data);

    }

    

    /**

     * Create ad units table

     */

    private function create_ad_units_table() {

        global $wpdb;



        $table_name = $this->tables['ad_units'];

        $charset_collate = $wpdb->get_charset_collate();



        $sql = "CREATE TABLE $table_name (

            id int(11) NOT NULL AUTO_INCREMENT,

            name varchar(100) NOT NULL,

            provider varchar(50) NOT NULL DEFAULT 'adsterra',

            ad_type varchar(50) NOT NULL DEFAULT 'popunder',

            placement varchar(50) NOT NULL DEFAULT 'header',

            ad_code longtext NOT NULL,

            is_active tinyint(1) NOT NULL DEFAULT 1,

            display_order int(11) NOT NULL DEFAULT 0,

            placement_conditions longtext,

            created_at datetime DEFAULT CURRENT_TIMESTAMP,

            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            KEY provider (provider),

            KEY ad_type (ad_type),

            KEY placement (placement),

            KEY is_active (is_active),

            KEY display_order (display_order)

        ) $charset_collate;";



        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);

    }



    /**

     * Create payment transactions table

     */

    private function create_payment_transactions_table() {

        global $wpdb;



        $table_name = $this->tables['payment_transactions'];

        $charset_collate = $wpdb->get_charset_collate();



        $sql = "CREATE TABLE $table_name (

            id int(11) NOT NULL AUTO_INCREMENT,

            user_id bigint(20) unsigned NOT NULL,

            membership_id int(11) DEFAULT NULL,

            tier_id int(11) NOT NULL,

            payment_gateway varchar(50) NOT NULL DEFAULT 'paypal',

            transaction_id varchar(255) NOT NULL,

            paypal_order_id varchar(255) DEFAULT NULL,

            paypal_payment_id varchar(255) DEFAULT NULL,

            amount decimal(10,2) NOT NULL,

            currency varchar(3) NOT NULL DEFAULT 'USD',

            payment_status varchar(50) NOT NULL DEFAULT 'pending',

            gateway_status varchar(50) DEFAULT NULL,

            payment_method varchar(50) DEFAULT NULL,

            payer_email varchar(255) DEFAULT NULL,

            payer_id varchar(255) DEFAULT NULL,

            transaction_data longtext,

            webhook_data longtext,

            notes text,

            processed_at datetime DEFAULT NULL,

            created_at datetime DEFAULT CURRENT_TIMESTAMP,

            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            UNIQUE KEY transaction_id (transaction_id),

            KEY user_id (user_id),

            KEY membership_id (membership_id),

            KEY tier_id (tier_id),

            KEY payment_gateway (payment_gateway),

            KEY payment_status (payment_status),

            KEY paypal_order_id (paypal_order_id),

            KEY created_at (created_at)

        ) $charset_collate;";



        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);

    }



    /**

     * Create float over links table

     */

    private function create_float_over_links_table() {

        global $wpdb;



        $table_name = $wpdb->prefix . 'epic_membership_float_over_links';

        $charset_collate = $wpdb->get_charset_collate();



        $sql = "CREATE TABLE $table_name (

            id mediumint(9) NOT NULL AUTO_INCREMENT,

            ip_address varchar(100) NOT NULL,

            link varchar(255) NOT NULL,

            count int(11) DEFAULT 0 NOT NULL,

            view_limit int(11) DEFAULT 5 NOT NULL,

            is_unlimited tinyint(1) DEFAULT 0 NOT NULL,

            created_at datetime DEFAULT CURRENT_TIMESTAMP,

            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            PRIMARY KEY (id),

            UNIQUE KEY ip_link (ip_address, link),

            KEY idx_ip_unlimited (ip_address, is_unlimited)

        ) $charset_collate;";



        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($sql);



        // Check if we need to update existing table structure

        $this->maybe_update_float_over_table();

    }



    /**

     * Update float over table structure if needed

     */

    private function maybe_update_float_over_table() {

        global $wpdb;



        $table_name = $wpdb->prefix . 'epic_membership_float_over_links';



        // Check if new columns exist

        $columns = $wpdb->get_results("DESCRIBE $table_name");

        $column_names = array_column($columns, 'Field');



        $updates_needed = [];



        if (!in_array('view_limit', $column_names)) {

            $updates_needed[] = "ADD COLUMN view_limit int(11) DEFAULT 5 NOT NULL AFTER count";

        }



        if (!in_array('is_unlimited', $column_names)) {

            $updates_needed[] = "ADD COLUMN is_unlimited tinyint(1) DEFAULT 0 NOT NULL AFTER view_limit";

        }



        // Apply updates if needed

        if (!empty($updates_needed)) {

            foreach ($updates_needed as $update) {

                $wpdb->query("ALTER TABLE $table_name $update");

            }



            // Add new index if it doesn't exist

            $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name WHERE Key_name = 'idx_ip_unlimited'");

            if (empty($indexes)) {

                $wpdb->query("ALTER TABLE $table_name ADD KEY idx_ip_unlimited (ip_address, is_unlimited)");

            }

        }

    }



    /**

     * Get user IP address

     */

    private function get_user_ip() {

        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {

            return $_SERVER['HTTP_CLIENT_IP'];

        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {

            return $_SERVER['HTTP_X_FORWARDED_FOR'];

        } else {

            return $_SERVER['REMOTE_ADDR'];

        }

    }



    /**

     * Migration to version 1.4.0 - Add currency column to tiers table

     */

    private function migrate_to_1_4_0() {

        global $wpdb;



        $table_tiers = $this->tables['tiers'];



        // Check if currency column already exists

        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_tiers LIKE 'currency'");



        if (empty($column_exists)) {

            // Add currency column with NOT NULL constraint

            $sql = "ALTER TABLE $table_tiers ADD COLUMN currency varchar(3) NOT NULL DEFAULT 'USD' AFTER price";

            $result = $wpdb->query($sql);



            if ($result === false) {

                error_log('Epic Membership: Failed to add currency column to tiers table: ' . $wpdb->last_error);

                error_log('Epic Membership: SQL: ' . $sql);

            } else {

                error_log('Epic Membership: Successfully added currency column to tiers table');

            }

        } else {

            error_log('Epic Membership: Currency column already exists in tiers table');



            // Check if column allows NULL and fix it

            $column_info = $wpdb->get_row("SHOW COLUMNS FROM $table_tiers LIKE 'currency'");

            if ($column_info && $column_info->Null === 'YES') {

                $wpdb->query("ALTER TABLE $table_tiers MODIFY COLUMN currency varchar(3) NOT NULL DEFAULT 'USD'");

                error_log('Epic Membership: Modified currency column to NOT NULL');

            }

        }



        // Update existing tiers to have default currency

        $default_currency = get_option('epic_membership_default_currency', 'USD');

        $update_result = $wpdb->query($wpdb->prepare("UPDATE $table_tiers SET currency = %s WHERE currency IS NULL OR currency = '' OR currency = '0'", $default_currency));



        if ($update_result !== false) {

            error_log("Epic Membership: Updated $update_result tiers with default currency: $default_currency");

        }

    }

}

