/**

 * Epic Membership Plugin - Frontend Styles

 */



/* Content Protection Styles */

.epic-membership-protected-content {

    background: #f8f9fa;

    border: 2px solid #e9ecef;

    border-radius: 8px;

    padding: 30px;

    text-align: center;

    margin: 20px 0;

}



.epic-membership-lock-icon {

    font-size: 48px;

    color: #6c757d;

    margin-bottom: 15px;

}



.epic-membership-protection-message {

    font-size: 18px;

    color: #495057;

    margin-bottom: 20px;

    line-height: 1.5;

}



.epic-membership-upgrade-button {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%) !important;

    color: white !important;

    padding: 12px 30px !important;

    border: none !important;

    border-radius: 25px !important;

    font-size: 16px !important;

    font-weight: 600 !important;

    text-decoration: none !important;

    display: inline-block !important;

    transition: all 0.3s ease !important;

    cursor: pointer !important;

    /* Prevent any link-like behaviors */
    outline: none !important;

    /* Ensure button elements behave properly */
    font-family: inherit !important;
    line-height: normal !important;
    vertical-align: baseline !important;

    /* Prevent text selection and dragging */
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;

}



.epic-membership-upgrade-button:hover {

    background: linear-gradient(135deg, #005a87 0%, #004d73 100%) !important;

    color: white !important;

    text-decoration: none !important;

    transform: translateY(-2px) !important;

    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3) !important;

    outline: none !important;

}

/* Ensure focus states don't cause URL changes */
.epic-membership-upgrade-button:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.5) !important;
}

/* Prevent any anchor-specific behaviors */
.epic-membership-upgrade-button:link,
.epic-membership-upgrade-button:visited {
    color: white !important;
    text-decoration: none !important;
}



/* Countdown Timer Styles */

.epic-membership-countdown {

    background: #fff3cd;

    border: 1px solid #ffeaa7;

    border-radius: 6px;

    padding: 20px;

    margin: 15px 0;

    text-align: center;

}



.epic-membership-countdown-title {

    font-size: 16px;

    font-weight: 600;

    color: #856404;

    margin-bottom: 10px;

}



.epic-membership-countdown-timer {

    display: flex;

    justify-content: center;

    gap: 15px;

    margin: 15px 0;

}



.epic-membership-countdown-unit {

    background: white;

    border-radius: 4px;

    padding: 10px;

    min-width: 60px;

    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

}



.epic-membership-countdown-number {

    display: block;

    font-size: 24px;

    font-weight: bold;

    color: #495057;

    line-height: 1;

}



.epic-membership-countdown-label {

    display: block;

    font-size: 12px;

    color: #6c757d;

    text-transform: uppercase;

    margin-top: 5px;

}



.epic-membership-countdown-expired {

    color: #721c24;

    background: #f8d7da;

    border-color: #f5c6cb;

}



/* User Dashboard Styles */

.epic-membership-dashboard {

    background: white;

    border-radius: 12px;

    box-shadow: 0 4px 20px rgba(0,0,0,0.08);

    overflow: hidden;

    margin: 20px 0;

    border: 1px solid #e9ecef;

}



.epic-membership-dashboard-header {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%);

    color: white;

    padding: 25px;

    text-align: center;

    position: relative;

}



.epic-membership-dashboard-header::after {

    content: '';

    position: absolute;

    bottom: 0;

    left: 0;

    right: 0;

    height: 4px;

    background: linear-gradient(90deg, #00a0d2, #0073aa, #005177);

}



.epic-membership-dashboard-title {

    font-size: 26px;

    font-weight: 700;

    margin: 0;

    text-shadow: 0 1px 2px rgba(0,0,0,0.1);

}



.epic-membership-dashboard-content {

    padding: 30px;

}



/* Enhanced Status Card */

.epic-membership-status-card {

    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

    border-radius: 12px;

    padding: 25px;

    margin-bottom: 30px;

    border: 1px solid #e9ecef;

    box-shadow: 0 2px 8px rgba(0,0,0,0.04);

    position: relative;

    overflow: hidden;

}



.epic-membership-status-card::before {

    content: '';

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    height: 4px;

    background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);

}



.epic-membership-status-card.membership-expired::before {

    background: linear-gradient(90deg, #dc3545, #e74c3c);

}



.epic-membership-status-card.membership-expiring::before {

    background: linear-gradient(90deg, #ffc107, #fd7e14);

}



.epic-membership-status-card.membership-warning::before {

    background: linear-gradient(90deg, #fd7e14, #ffc107);

}



.epic-membership-status-header {

    display: flex;

    justify-content: space-between;

    align-items: center;

    margin-bottom: 25px;

    padding-bottom: 15px;

    border-bottom: 2px solid #e9ecef;

}



.epic-membership-status-title {

    font-size: 20px;

    font-weight: 700;

    color: #2c3e50;

    margin: 0;

}



.epic-membership-status-indicator {

    display: flex;

    align-items: center;

    gap: 8px;

}



.status-icon {

    font-size: 20px;

    display: inline-block;

}



.status-text {

    font-weight: 600;

    font-size: 14px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.status-text.active { color: #28a745; }

.status-text.expired { color: #dc3545; }

.status-text.expiring { color: #fd7e14; }

.status-text.warning { color: #ffc107; }

.status-text.pending { color: #6c757d; }

.status-text.inactive { color: #6c757d; }



/* Status Grid */

.epic-membership-status-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));

    gap: 20px;

    margin-bottom: 20px;

}



.epic-membership-status-item {

    display: flex;

    align-items: center;

    gap: 15px;

    padding: 15px;

    background: white;

    border-radius: 8px;

    border: 1px solid #e9ecef;

    transition: all 0.3s ease;

}



.epic-membership-status-item:hover {

    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    transform: translateY(-1px);

}



.status-item-icon {

    font-size: 24px;

    width: 40px;

    height: 40px;

    display: flex;

    align-items: center;

    justify-content: center;

    background: #f8f9fa;

    border-radius: 50%;

    flex-shrink: 0;

}



.status-item-content {

    flex: 1;

    min-width: 0;

}



.status-item-label {

    display: block;

    font-size: 12px;

    font-weight: 500;

    color: #6c757d;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    margin-bottom: 4px;

}



.status-item-value {

    display: block;

    font-weight: 600;

    color: #2c3e50;

    font-size: 14px;

    word-break: break-word;

}



/* Status Value Classes */

.status-item-value.expires-today,

.status-item-value.critical {

    color: #dc3545;

    font-weight: 700;

}



.status-item-value.expires-soon,

.status-item-value.urgent {

    color: #fd7e14;

    font-weight: 700;

}



.status-item-value.expires-week,

.status-item-value.warning {

    color: #ffc107;

    font-weight: 600;

}



.status-item-value.lifetime {

    color: #28a745;

    font-weight: 700;

}



.status-item-value.payment-completed {

    color: #28a745;

}



.status-item-value.payment-pending {

    color: #ffc107;

}



.status-item-value.payment-failed {

    color: #dc3545;

}



.status-item-value.auto-renew-enabled {

    color: #28a745;

}



.status-item-value.auto-renew-disabled {

    color: #6c757d;

}



/* Tier Badge */

.epic-membership-tier-badge {

    display: inline-block;

    padding: 6px 14px;

    border-radius: 25px;

    font-size: 12px;

    font-weight: 700;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    border: 2px solid transparent;

}



.epic-membership-tier-free {

    background: #e9ecef;

    color: #6c757d;

    border-color: #dee2e6;

}



.epic-membership-tier-premium {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%);

    color: white;

    border-color: #0073aa;

}



.epic-membership-tier-vip {

    background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);

    color: white;

    border-color: #5a2d91;

}



/* Status Warning */

.epic-membership-status-warning {

    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);

    border: 1px solid #ffc107;

    border-radius: 8px;

    padding: 15px;

    margin-top: 20px;

    display: flex;

    align-items: center;

    gap: 12px;

}



.warning-icon {

    font-size: 20px;

    flex-shrink: 0;

}



.warning-message {

    flex: 1;

    color: #856404;

    font-weight: 500;

}



/* No Membership State */

.epic-membership-no-membership {

    text-align: center;

    padding: 40px 20px;

    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    border-radius: 12px;

    border: 2px dashed #dee2e6;

}



.no-membership-icon {

    font-size: 48px;

    margin-bottom: 15px;

    opacity: 0.7;

}



.no-membership-message h4 {

    color: #495057;

    margin-bottom: 10px;

    font-size: 18px;

}



.no-membership-message p {

    color: #6c757d;

    margin: 0;

    line-height: 1.5;

}



/* Status Actions */

.epic-membership-status-actions {

    display: flex;

    gap: 15px;

    align-items: center;

    justify-content: center;

    margin-top: 25px;

    padding-top: 20px;

    border-top: 1px solid #e9ecef;

}



.epic-membership-refresh-status {

    display: flex;

    align-items: center;

    gap: 8px;

    padding: 8px 16px;

    background: #f8f9fa;

    border: 1px solid #dee2e6;

    border-radius: 6px;

    color: #495057;

    text-decoration: none;

    font-size: 14px;

    transition: all 0.3s ease;

}



.epic-membership-refresh-status:hover {

    background: #e9ecef;

    color: #495057;

    text-decoration: none;

}



.refresh-icon {

    font-size: 12px;

}



.epic-membership-renew-button {

    padding: 10px 20px;

    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);

    color: white;

    border: none;

    border-radius: 6px;

    font-weight: 600;

    text-decoration: none;

    transition: all 0.3s ease;

}



.epic-membership-renew-button:hover {

    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);

    color: white;

    text-decoration: none;

    transform: translateY(-1px);

}



/* Content Teaser Styles */

.epic-membership-content-teaser {

    position: relative;

    overflow: hidden;

}



.epic-membership-teaser-content {

    max-height: 200px;

    overflow: hidden;

    position: relative;

}



.epic-membership-teaser-fade {

    position: absolute;

    bottom: 0;

    left: 0;

    right: 0;

    height: 60px;

    background: linear-gradient(transparent, white);

}



.epic-membership-teaser-overlay {

    background: rgba(255, 255, 255, 0.95);

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    bottom: 0;

    display: flex;

    align-items: center;

    justify-content: center;

    backdrop-filter: blur(2px);

}



/* Responsive Design */

@media (max-width: 768px) {

    .epic-membership-countdown-timer {

        flex-wrap: wrap;

        gap: 10px;

    }

    

    .epic-membership-countdown-unit {

        min-width: 50px;

        padding: 8px;

    }

    

    .epic-membership-countdown-number {

        font-size: 20px;

    }

    

    .epic-membership-dashboard-content {

        padding: 20px;

    }

    

    .epic-membership-status-info {

        flex-direction: column;

        align-items: flex-start;

        gap: 5px;

    }

}



/* Loading States */

.epic-membership-loading {

    opacity: 0.6;

    pointer-events: none;

}



.epic-membership-spinner {

    display: inline-block;

    width: 20px;

    height: 20px;

    border: 2px solid #f3f3f3;

    border-top: 2px solid #007cba;

    border-radius: 50%;

    animation: epic-membership-spin 1s linear infinite;

}



@keyframes epic-membership-spin {

    0% { transform: rotate(0deg); }

    100% { transform: rotate(360deg); }

}



/* Accessibility */

.epic-membership-sr-only {

    position: absolute;

    width: 1px;

    height: 1px;

    padding: 0;

    margin: -1px;

    overflow: hidden;

    clip: rect(0, 0, 0, 0);

    white-space: nowrap;

    border: 0;

}



/* Benefits Section */

.epic-membership-benefits-section {

    background: white;

    border-radius: 12px;

    padding: 30px;

    margin-bottom: 30px;

    border: 1px solid #e9ecef;

    box-shadow: 0 2px 8px rgba(0,0,0,0.04);

}



.epic-membership-section-header {

    text-align: center;

    margin-bottom: 30px;

    padding-bottom: 20px;

    border-bottom: 2px solid #e9ecef;

}



.epic-membership-section-title {

    font-size: 24px;

    font-weight: 700;

    color: #2c3e50;

    margin: 0 0 10px 0;

}



.epic-membership-section-description {

    color: #6c757d;

    font-size: 16px;

    margin: 0;

    line-height: 1.5;

}



/* Benefits Grid */

.benefits-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

    gap: 20px;

    margin-bottom: 30px;

}



.benefit-item {

    background: white;

    border: 2px solid #e9ecef;

    border-radius: 12px;

    padding: 20px;

    display: flex;

    align-items: flex-start;

    gap: 15px;

    transition: all 0.3s ease;

    position: relative;

    overflow: hidden;

}



.benefit-item.active {

    border-color: #28a745;

    background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);

}



.benefit-item.active::before {

    content: '';

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    height: 4px;

    background: linear-gradient(90deg, #28a745, #20c997);

}



.benefit-item:hover {

    transform: translateY(-2px);

    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

}



.benefit-icon {

    font-size: 24px;

    width: 40px;

    height: 40px;

    display: flex;

    align-items: center;

    justify-content: center;

    background: #f8f9fa;

    border-radius: 50%;

    flex-shrink: 0;

}



.benefit-item.active .benefit-icon {

    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);

    color: white;

}



.benefit-content {

    flex: 1;

    min-width: 0;

}



.benefit-title {

    font-size: 16px;

    font-weight: 600;

    color: #2c3e50;

    margin: 0 0 8px 0;

}



.benefit-description {

    font-size: 14px;

    color: #6c757d;

    margin: 0;

    line-height: 1.4;

}



.benefit-status {

    flex-shrink: 0;

}



.status-badge {

    display: inline-block;

    padding: 4px 8px;

    border-radius: 12px;

    font-size: 11px;

    font-weight: 600;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.status-badge.included {

    background: #d4edda;

    color: #155724;

}



/* Tier Comparison */

.epic-membership-tier-comparison {

    margin-top: 40px;

    padding-top: 30px;

    border-top: 2px solid #e9ecef;

}



.comparison-header {

    text-align: center;

    margin-bottom: 30px;

}



.comparison-header h4 {

    font-size: 20px;

    font-weight: 700;

    color: #2c3e50;

    margin: 0 0 10px 0;

}



.comparison-header p {

    color: #6c757d;

    margin: 0;

    font-size: 16px;

}



.tier-comparison-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));

    gap: 25px;

}



.tier-comparison-card {

    background: white;

    border: 2px solid #e9ecef;

    border-radius: 12px;

    padding: 25px;

    transition: all 0.3s ease;

    position: relative;

    overflow: hidden;

}



.tier-comparison-card:hover {

    border-color: #007cba;

    transform: translateY(-3px);

    box-shadow: 0 6px 20px rgba(0,0,0,0.1);

}



.tier-header {

    display: flex;

    justify-content: space-between;

    align-items: center;

    margin-bottom: 20px;

    padding-bottom: 15px;

    border-bottom: 1px solid #e9ecef;

}



.tier-price {

    font-weight: 700;

    color: #2c3e50;

}



.price-period {

    font-size: 12px;

    color: #6c757d;

    font-weight: 500;

}



.additional-benefits h5 {

    font-size: 14px;

    font-weight: 600;

    color: #495057;

    margin: 0 0 15px 0;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.additional-benefit {

    display: flex;

    align-items: center;

    gap: 10px;

    margin-bottom: 10px;

    padding: 8px 0;

}



.additional-benefit .benefit-icon {

    font-size: 16px;

    width: 24px;

    height: 24px;

}



.additional-benefit .benefit-name {

    font-size: 14px;

    color: #495057;

    font-weight: 500;

}



.tier-action {

    margin-top: 20px;

    text-align: center;

}



/* No Benefits State */

.epic-membership-no-benefits {

    text-align: center;

    padding: 40px 20px;

}



.no-benefits-content {

    margin-bottom: 40px;

}



.no-benefits-icon {

    font-size: 48px;

    margin-bottom: 20px;

    opacity: 0.7;

}



.no-benefits-content h4 {

    font-size: 20px;

    color: #495057;

    margin-bottom: 10px;

}



.no-benefits-content p {

    color: #6c757d;

    font-size: 16px;

    line-height: 1.5;

    margin: 0;

}



.available-tiers h4 {

    font-size: 18px;

    color: #2c3e50;

    margin-bottom: 20px;

}



.tier-options-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));

    gap: 20px;

}



.tier-option-card {

    background: white;

    border: 2px solid #e9ecef;

    border-radius: 12px;

    padding: 20px;

    transition: all 0.3s ease;

}



.tier-option-card:hover {

    border-color: #007cba;

    transform: translateY(-2px);

    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

}



.tier-benefits {

    margin: 15px 0;

}



.tier-benefit {

    display: flex;

    align-items: center;

    gap: 8px;

    margin-bottom: 8px;

    font-size: 14px;

}



.tier-benefit .benefit-icon {

    font-size: 14px;

    width: 20px;

    height: 20px;

}



/* Enhanced Responsive Design */

@media (max-width: 768px) {

    .epic-membership-status-grid {

        grid-template-columns: 1fr;

    }



    .epic-membership-status-actions {

        flex-direction: column;

        gap: 10px;

    }



    .benefits-grid {

        grid-template-columns: 1fr;

    }



    .tier-comparison-grid,

    .tier-options-grid {

        grid-template-columns: 1fr;

    }



    .epic-membership-section-title {

        font-size: 20px;

    }



    .epic-membership-benefits-section,

    .epic-membership-status-card {

        padding: 20px;

    }



    .tier-header {

        flex-direction: column;

        align-items: flex-start;

        gap: 10px;

    }

}



/* Menu Status Indicators */

.menu-status-indicator {

    display: inline-block;

    font-size: 12px;

    margin-left: 5px;

    padding: 2px 4px;

    border-radius: 50%;

    font-weight: bold;

}



.menu-status-indicator.active {

    color: #28a745;

}



.menu-status-indicator.expiring {

    color: #ffc107;

}



.menu-status-indicator.expired {

    color: #dc3545;

}



.epic-membership-menu-item {

    position: relative;

}



.epic-membership-menu-item.membership-expiring > a,

.epic-membership-menu-item.membership-expired > a {

    position: relative;

}



.epic-membership-menu-item.membership-expiring > a::after {

    content: '';

    position: absolute;

    top: 0;

    right: 0;

    width: 8px;

    height: 8px;

    background: #ffc107;

    border-radius: 50%;

    border: 2px solid white;

}



.epic-membership-menu-item.membership-expired > a::after {

    content: '';

    position: absolute;

    top: 0;

    right: 0;

    width: 8px;

    height: 8px;

    background: #dc3545;

    border-radius: 50%;

    border: 2px solid white;

}



/* Status Page Specific Styles */

.epic-membership-status-page .epic-membership-dashboard {

    max-width: 1200px;

    margin: 0 auto;

}



.epic-membership-status-page .site-main {

    padding: 20px;

}



/* AGGRESSIVE AD BLOCKING FOR MEMBERSHIP PAGES */

/* Target by page class */

.epic-membership-status-page .adsbygoogle,

.epic-membership-status-page ins.adsbygoogle,

.epic-membership-status-page [data-ad-client],

.epic-membership-status-page [data-ad-slot],

.epic-membership-status-page .google-ad,

.epic-membership-status-page .adsense,

.epic-membership-status-page .adsterra,

.epic-membership-status-page .adsterra-banner,

.epic-membership-status-page [data-adsterra],

.epic-membership-status-page .advertisement,

.epic-membership-status-page .ad-banner,

.epic-membership-status-page .ad-container,

.epic-membership-status-page .ad-wrapper,

.epic-membership-status-page .banner-ad,

.epic-membership-status-page .sidebar-ad,

.epic-membership-status-page .header-ad,

.epic-membership-status-page .footer-ad,

.epic-membership-status-page .content-ad,

.epic-membership-status-page [class*="ad-"]:not(.epic-membership):not(.admin):not(.add):not(.address):not(.advance):not(.advanced),

.epic-membership-status-page [id*="ad-"]:not(.epic-membership):not(.admin):not(.add):not(.address):not(.advance):not(.advanced),

.epic-membership-status-page [class*="ads-"]:not(.epic-membership),

.epic-membership-status-page [id*="ads-"]:not(.epic-membership),

.epic-membership-status-page .epic-membership-ad-unit,

.epic-membership-status-page iframe[src*="googlesyndication"],

.epic-membership-status-page iframe[src*="adsterra"],

.epic-membership-status-page iframe[src*="doubleclick"],

.epic-membership-status-page script[src*="googlesyndication"],

.epic-membership-status-page script[src*="adsterra"],

.epic-membership-status-page script[src*="doubleclick"],

/* Target by URL pattern */

body[class*="membership"] .adsbygoogle,

body[class*="membership"] ins.adsbygoogle,

body[class*="membership"] [data-ad-client],

body[class*="membership"] [data-ad-slot],

body[class*="membership"] .google-ad,

body[class*="membership"] .adsense,

body[class*="membership"] .adsterra,

body[class*="membership"] .adsterra-banner,

body[class*="membership"] [data-adsterra],

body[class*="membership"] .advertisement,

body[class*="membership"] .ad-banner,

body[class*="membership"] .ad-container,

body[class*="membership"] .ad-wrapper,

body[class*="membership"] .banner-ad,

body[class*="membership"] .epic-membership-ad-unit,

/* Target by page slug */

.page-id-* .adsbygoogle,

.page-id-* ins.adsbygoogle,

.page-id-* [data-ad-client],

.page-id-* [data-ad-slot],

.page-id-* .google-ad,

.page-id-* .adsense,

.page-id-* .adsterra,

.page-id-* .adsterra-banner,

.page-id-* [data-adsterra],

.page-id-* .advertisement,

.page-id-* .ad-banner,

.page-id-* .ad-container,

.page-id-* .ad-wrapper,

.page-id-* .banner-ad,

.page-id-* .epic-membership-ad-unit {

    display: none !important;

    visibility: hidden !important;

    opacity: 0 !important;

    height: 0 !important;

    width: 0 !important;

    margin: 0 !important;

    padding: 0 !important;

    overflow: hidden !important;

    position: absolute !important;

    left: -9999px !important;

    top: -9999px !important;

    z-index: -1 !important;

}



/* Also hide ads when membership dashboard shortcode is present */

.epic-membership-dashboard .adsbygoogle,

.epic-membership-dashboard ins.adsbygoogle,

.epic-membership-dashboard [data-ad-client],

.epic-membership-dashboard [data-ad-slot],

.epic-membership-dashboard .google-ad,

.epic-membership-dashboard .adsense,

.epic-membership-dashboard .adsterra,

.epic-membership-dashboard .adsterra-banner,

.epic-membership-dashboard [data-adsterra],

.epic-membership-dashboard .advertisement,

.epic-membership-dashboard .ad-banner,

.epic-membership-dashboard .ad-container,

.epic-membership-dashboard .ad-wrapper,

.epic-membership-dashboard .banner-ad,

.epic-membership-dashboard .epic-membership-ad-unit {

    display: none !important;

    visibility: hidden !important;

    opacity: 0 !important;

    height: 0 !important;

    width: 0 !important;

    margin: 0 !important;

    padding: 0 !important;

    overflow: hidden !important;

}



/* Upgrade Options Section Styles */

.epic-membership-upgrade-section {

    background: #ffffff;

    border-radius: 12px;

    padding: 30px;

    margin: 30px 0;

    border: 1px solid #e9ecef;

    box-shadow: 0 2px 8px rgba(0,0,0,0.04);

}



.epic-membership-upgrade-section .epic-membership-status-title {

    font-size: 24px;

    font-weight: 700;

    color: #2c3e50;

    margin-bottom: 25px;

    text-align: center;

    position: relative;

    padding-bottom: 15px;

}



.epic-membership-upgrade-section .epic-membership-status-title::after {

    content: '';

    position: absolute;

    bottom: 0;

    left: 50%;

    transform: translateX(-50%);

    width: 60px;

    height: 3px;

    background: linear-gradient(90deg, #007cba, #0073aa);

    border-radius: 2px;

}



.epic-membership-upgrade-options {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));

    gap: 25px;

    margin-top: 30px;

    align-items: stretch;

    grid-auto-rows: 1fr;

    position: relative;

    z-index: 1;

}



.epic-membership-upgrade-option {

    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);

    border: 2px solid #e9ecef;

    border-radius: 16px;

    padding: 25px;

    transition: all 0.3s ease;

    position: relative !important;

    overflow: hidden;

    display: flex !important;

    flex-direction: column !important;

    height: 100% !important;

    min-height: 350px !important;

    max-height: none !important;

    box-sizing: border-box !important;

    justify-content: space-between !important;

    align-items: stretch !important;

}



.epic-membership-upgrade-option::before {

    content: '';

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    height: 4px;

    background: linear-gradient(90deg, #007cba, #0073aa);

    opacity: 0;

    transition: opacity 0.3s ease;

}



.epic-membership-upgrade-option:hover {

    border-color: #007cba;

    transform: translateY(-5px);

    box-shadow: 0 8px 25px rgba(0, 124, 186, 0.15);

}



.epic-membership-upgrade-option:hover::before {

    opacity: 1;

}



.upgrade-tier-name {

    text-align: center;

    margin-bottom: 20px;

}



.upgrade-tier-name .epic-membership-tier-badge {

    font-size: 14px;

    font-weight: 700;

    padding: 8px 16px;

    border-radius: 25px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    display: inline-block;

    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

}



.upgrade-tier-description {

    color: #6c757d;

    font-size: 15px;

    line-height: 1.6;

    margin-bottom: 20px;

    flex-grow: 1;

    text-align: center;

    min-height: 60px;

    display: flex;

    align-items: center;

    justify-content: center;

}



.upgrade-tier-price {

    text-align: center;

    margin-bottom: 25px;

    padding: 15px 0;

    background: rgba(0, 124, 186, 0.05);

    border-radius: 8px;

    border: 1px solid rgba(0, 124, 186, 0.1);

}



.upgrade-tier-price {

    font-size: 28px;

    font-weight: 700;

    color: #2c3e50;

}



.upgrade-tier-price small {

    font-size: 14px;

    color: #6c757d;

    font-weight: 400;

}



.upgrade-tier-action {

    text-align: center;

    margin-top: auto;

    position: relative;

    z-index: 1;

    flex-shrink: 0;

}



.upgrade-tier-action .epic-membership-upgrade-button {

    width: 100%;

    padding: 14px 20px;

    font-size: 16px;

    font-weight: 600;

    border-radius: 8px;

    transition: all 0.3s ease;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    position: relative;

    z-index: 2;

    box-sizing: border-box;

    display: block;

    margin: 0 auto;

}



.upgrade-tier-action .epic-membership-upgrade-button:hover {

    transform: translateY(-2px);

    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.3);

}



/* Button Icons and Enhanced Styling */

.epic-membership-upgrade-button .button-icon {

    display: inline-block;

    margin-right: 8px;

    font-size: 16px;

    vertical-align: middle;

}



.epic-membership-upgrade-button.epic-membership-free-tier {

    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);

    border-color: #28a745;

}



.epic-membership-upgrade-button.epic-membership-free-tier:hover {

    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);

    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);

}



/* Enhanced Tier Badge Styling - Based on tier level and price */



/* Free tier (level 0 or price 0) */

.epic-membership-tier-badge.tier-level-0,

.epic-membership-tier-badge.tier-price-free {

    background: #e9ecef;

    color: #6c757d;

    border-color: #dee2e6;

}



/* Paid tier level 10 (Premium equivalent) */

.epic-membership-tier-badge.tier-level-10,

.epic-membership-tier-badge.tier-price-paid.tier-level-10 {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%);

    color: white;

    border-color: #0073aa;

    position: relative;

}



.epic-membership-tier-badge.tier-level-10::after,

.epic-membership-tier-badge.tier-price-paid.tier-level-10::after {

    content: '⭐';

    position: absolute;

    top: -5px;

    right: -5px;

    font-size: 12px;

}



/* Paid tier level 20+ (VIP equivalent) */

.epic-membership-tier-badge.tier-level-20,

.epic-membership-tier-badge.tier-price-paid.tier-level-20 {

    background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);

    color: white;

    border-color: #5a2d91;

    position: relative;

}



.epic-membership-tier-badge.tier-level-20::after,

.epic-membership-tier-badge.tier-price-paid.tier-level-20::after {

    content: '👑';

    position: absolute;

    top: -5px;

    right: -5px;

    font-size: 12px;

}



/* General paid tier styling (fallback for any paid tier) */

.epic-membership-tier-badge.tier-price-paid {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%);

    color: white;

    border-color: #0073aa;

    position: relative;

}



.epic-membership-tier-badge.tier-price-paid::after {

    content: '⭐';

    position: absolute;

    top: -5px;

    right: -5px;

    font-size: 12px;

}



/* Legacy support for existing slug-based styling */

.epic-membership-tier-premium {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%);

    color: white;

    border-color: #0073aa;

    position: relative;

}



.epic-membership-tier-premium::after {

    content: '⭐';

    position: absolute;

    top: -5px;

    right: -5px;

    font-size: 12px;

}



.epic-membership-tier-vip {

    background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);

    color: white;

    border-color: #5a2d91;

    position: relative;

}



.epic-membership-tier-vip::after {

    content: '👑';

    position: absolute;

    top: -5px;

    right: -5px;

    font-size: 12px;

}



/* Price Styling Enhancements */

.upgrade-tier-price .price-amount {

    display: block;

    font-size: 32px;

    font-weight: 800;

    color: #2c3e50;

    line-height: 1;

    margin-bottom: 5px;

}



.upgrade-tier-price .price-duration {

    display: block;

    font-size: 14px;

    color: #6c757d;

    font-weight: 500;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



/* Loading and Hover States */

.epic-membership-upgrade-option.loading {

    opacity: 0.7;

    pointer-events: none;

}



.epic-membership-upgrade-option.loading::after {

    content: '';

    position: absolute;

    top: 50%;

    left: 50%;

    width: 20px;

    height: 20px;

    margin: -10px 0 0 -10px;

    border: 2px solid #007cba;

    border-top-color: transparent;

    border-radius: 50%;

    animation: spin 1s linear infinite;

}



@keyframes spin {

    to {

        transform: rotate(360deg);

    }

}



/* Popular/Recommended Badge */

.epic-membership-upgrade-option.recommended {

    border-color: #28a745;

    position: relative;

}



.epic-membership-upgrade-option.recommended::before {

    background: linear-gradient(90deg, #28a745, #20c997);

    opacity: 1;

}



.epic-membership-upgrade-option.recommended .upgrade-tier-name::after {

    content: 'RECOMMENDED';

    position: absolute;

    top: -10px;

    right: -10px;

    background: #28a745;

    color: white;

    font-size: 10px;

    font-weight: 700;

    padding: 4px 8px;

    border-radius: 12px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



/* Accessibility and Focus States */

.epic-membership-upgrade-button:focus {

    outline: 3px solid rgba(0, 124, 186, 0.5);

    outline-offset: 2px;

}



.epic-membership-upgrade-option:focus-within {

    border-color: #007cba;

    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);

}



/* Empty State Styling */

.epic-membership-upgrade-options:empty::after {

    content: 'No upgrade options available at this time.';

    display: block;

    text-align: center;

    color: #6c757d;

    font-style: italic;

    padding: 40px 20px;

    background: #f8f9fa;

    border-radius: 8px;

    border: 2px dashed #dee2e6;

}



/* ULTRA STRONG BUTTON POSITIONING - PREVENT ALL INTERFERENCE */

div.epic-membership-upgrade-section div.epic-membership-upgrade-options div.epic-membership-upgrade-option div.upgrade-tier-action {

    position: relative !important;

    bottom: auto !important;

    left: auto !important;

    right: auto !important;

    top: auto !important;

    transform: none !important;

    float: none !important;

    clear: both !important;

    margin-top: auto !important;

    text-align: center !important;

    z-index: 1 !important;

    flex-shrink: 0 !important;

    width: 100% !important;

    max-width: 100% !important;

    min-width: auto !important;

    height: auto !important;

    min-height: auto !important;

    max-height: none !important;

    overflow: visible !important;

    display: block !important;

}



div.epic-membership-upgrade-section div.epic-membership-upgrade-options div.epic-membership-upgrade-option div.upgrade-tier-action a.epic-membership-upgrade-button,

div.epic-membership-upgrade-section div.epic-membership-upgrade-options div.epic-membership-upgrade-option div.upgrade-tier-action button.epic-membership-upgrade-button {

    position: relative !important;

    bottom: auto !important;

    left: auto !important;

    right: auto !important;

    top: auto !important;

    transform: none !important;

    float: none !important;

    clear: both !important;

    z-index: 2 !important;

    width: 100% !important;

    max-width: 100% !important;

    min-width: auto !important;

    height: auto !important;

    min-height: auto !important;

    max-height: none !important;

    overflow: visible !important;

    display: block !important;

    margin: 0 auto !important;

    box-sizing: border-box !important;

}



div.epic-membership-upgrade-section div.epic-membership-upgrade-options div.epic-membership-upgrade-option div.upgrade-tier-action a.epic-membership-upgrade-button:hover,

div.epic-membership-upgrade-section div.epic-membership-upgrade-options div.epic-membership-upgrade-option div.upgrade-tier-action button.epic-membership-upgrade-button:hover {

    transform: translateY(-2px) !important;

}



/* Prevent ANY positioning interference */

.epic-membership-upgrade-options .epic-membership-upgrade-button {

    position: static !important;

}



/* Additional protection against theme/plugin interference */

.epic-membership-upgrade-section * {

    position: static !important;

}



.epic-membership-upgrade-section .epic-membership-upgrade-option {

    position: relative !important;

}



.epic-membership-upgrade-section .upgrade-tier-action {

    position: relative !important;

}



.epic-membership-upgrade-section .epic-membership-upgrade-button {

    position: relative !important;

}



/* Additional Layout Protection */

.epic-membership-upgrade-option * {

    box-sizing: border-box;

}



.epic-membership-upgrade-option {

    contain: layout style;

    isolation: isolate;

}



/* Ensure proper stacking context */

.epic-membership-upgrade-section {

    position: relative;

    z-index: 1;

    contain: layout;

}



/* NUCLEAR OPTION - Override ALL possible interference */

body .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action,

html body .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action,

.wp-site-blocks .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action,

.site-content .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action {

    position: relative !important;

    margin-top: auto !important;

    text-align: center !important;

    flex-shrink: 0 !important;

    order: 999 !important;

}



body .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action .epic-membership-upgrade-button,

html body .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action .epic-membership-upgrade-button,

.wp-site-blocks .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action .epic-membership-upgrade-button,

.site-content .epic-membership-upgrade-section .epic-membership-upgrade-options .epic-membership-upgrade-option .upgrade-tier-action .epic-membership-upgrade-button {

    position: relative !important;

    width: 100% !important;

    display: block !important;

    margin: 0 auto !important;

    box-sizing: border-box !important;

}



/* Prevent theme button styles from interfering */

.epic-membership-upgrade-section .epic-membership-upgrade-button {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%) !important;

    color: white !important;

    border: none !important;

    text-decoration: none !important;

    font-family: inherit !important;

    line-height: normal !important;

    vertical-align: baseline !important;

}



/* Force flexbox behavior */

.epic-membership-upgrade-section .epic-membership-upgrade-option {

    display: flex !important;

    flex-direction: column !important;

    justify-content: space-between !important;

}



.epic-membership-upgrade-section .upgrade-tier-description {

    flex-grow: 1 !important;

}



/* Payment Integration Styles */

.epic-membership-payment-options {

    margin-top: 10px;

    position: relative;

    display: flex;

    flex-direction: column;

    gap: 10px;

}



.epic-membership-payment-methods-info {

    margin-bottom: 5px;

}



.epic-membership-payment-methods-info .payment-methods-text {

    font-size: 14px;

    color: #666;

    margin: 0 0 10px 0;

    font-weight: 500;

}



.epic-membership-payment-methods-note {

    margin-top: 10px;

    padding: 10px;

    background: #f8f9fa;

    border-radius: 4px;

    border-left: 3px solid #007cba;

}



.epic-membership-payment-methods-note .payment-note {

    font-size: 12px;

    color: #666;

    margin: 0;

    line-height: 1.4;

}



.epic-membership-paypal-button {

    background: #0070ba;

    color: white;

    border: none;

    padding: 12px 24px;

    border-radius: 4px;

    cursor: pointer;

    font-size: 14px;

    font-weight: 600;

    transition: background-color 0.3s ease;

    text-decoration: none;

    display: inline-block;

}



.epic-membership-paypal-button:hover {

    background: #005ea6;

    color: white;

    text-decoration: none;

}



.epic-membership-paypal-container {

    margin-top: 10px;

    min-height: 50px;

}



/* Ko-fi Integration Styles */

.epic-membership-kofi-button {

    background: #ff5f5f;

    color: white;

    border: none;

    padding: 12px 24px;

    border-radius: 4px;

    cursor: pointer;

    font-size: 14px;

    font-weight: 600;

    transition: background-color 0.3s ease;

    text-decoration: none;

    display: inline-block;

}



.epic-membership-kofi-button:hover {

    background: #e54545;

    color: white;

    text-decoration: none;

}



.epic-membership-kofi-button .button-icon {

    margin-right: 8px;

}



/* Enhanced Modal System - Based on Ko-fi Patterns */

.epic-membership-modal-overlay {
    /* Core positioning - fixed to viewport */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;

    /* Maximum z-index for theme compatibility */
    z-index: 2147483647 !important;

    /* Flexbox centering - most reliable across themes */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    /* No background overlay for better theme compatibility */
    background: transparent !important;

    /* Visibility and interaction */
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;

    /* Reset any theme interference */
    margin: 0 !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    transform: none !important;

    /* Prevent theme layout interference */
    float: none !important;
    clear: both !important;
}

.epic-membership-modal {
    /* Modal content styling */
    position: relative !important;
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;

    /* Responsive sizing */
    max-width: 500px !important;
    width: 100% !important;
    max-height: 90vh !important;

    /* Content handling */
    overflow-y: auto !important;

    /* Layering */
    z-index: 2147483648 !important; /* One higher than overlay */

    /* Reset positioning */
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;
    top: auto !important;
    left: auto !important;

    /* Display properties */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;

    /* Prevent theme interference */
    float: none !important;
    clear: both !important;
    border: none !important;
    outline: none !important;

    /* Font inheritance prevention */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #333 !important;
}



.epic-membership-modal-header {
    /* Header styling with theme protection */
    padding: 25px 30px 20px !important;
    border-bottom: 1px solid #e9ecef !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;

    /* Reset any theme interference */
    margin: 0 !important;
    background: transparent !important;
    box-sizing: border-box !important;
}

.epic-membership-modal-header h3 {
    /* Title styling */
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;

    /* Font inheritance prevention */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.3 !important;

    /* Reset theme interference */
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
}

.epic-membership-modal-close {
    /* Close button styling */
    background: none !important;
    border: none !important;
    font-size: 24px !important;
    cursor: pointer !important;
    color: #6c757d !important;
    padding: 8px !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;

    /* Reset theme interference */
    margin: 0 !important;
    outline: none !important;
    box-shadow: none !important;
    text-decoration: none !important;
}

.epic-membership-modal-close:hover {
    background: #f8f9fa !important;
    color: #495057 !important;
    transform: none !important;
}



.epic-membership-modal-content {
    /* Content area styling */
    padding: 30px !important;

    /* Reset theme interference */
    margin: 0 !important;
    background: transparent !important;
    box-sizing: border-box !important;
    border: none !important;
    outline: none !important;
}

.epic-membership-modal-content p {
    /* Paragraph styling */
    margin: 0 0 25px 0 !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #495057 !important;

    /* Font inheritance prevention */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;

    /* Reset theme interference */
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
}

.epic-membership-modal-actions {
    /* Action buttons container */
    display: flex !important;
    gap: 15px !important;
    justify-content: flex-end !important;
    margin-top: 30px !important;

    /* Reset theme interference */
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
    box-sizing: border-box !important;
}

/* Primary action button (Confirm/Upgrade) */
.epic-membership-modal-actions .button-primary,
.epic-membership-modal-actions .epic-membership-upgrade-confirm {
    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%) !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    display: inline-block !important;

    /* Font inheritance prevention */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.4 !important;

    /* Reset theme interference */
    margin: 0 !important;
    outline: none !important;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.2) !important;
    text-align: center !important;
    vertical-align: middle !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
}

.epic-membership-modal-actions .button-primary:hover,
.epic-membership-modal-actions .epic-membership-upgrade-confirm:hover {
    background: linear-gradient(135deg, #005a87 0%, #004d73 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 124, 186, 0.3) !important;
}

/* Secondary action button (Cancel) */
.epic-membership-modal-actions .button-secondary,
.epic-membership-modal-actions .epic-membership-modal-cancel {
    background: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
    padding: 12px 24px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    display: inline-block !important;

    /* Font inheritance prevention */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.4 !important;

    /* Reset theme interference */
    margin: 0 !important;
    outline: none !important;
    box-shadow: none !important;
    text-align: center !important;
    vertical-align: middle !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
}

.epic-membership-modal-actions .button-secondary:hover,
.epic-membership-modal-actions .epic-membership-modal-cancel:hover {
    background: #e9ecef !important;
    border-color: #adb5bd !important;
    transform: none !important;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .epic-membership-modal-overlay {
        padding: 15px !important;
    }

    .epic-membership-modal {
        width: 95% !important;
        max-width: none !important;
        margin: 0 !important;
        border-radius: 8px !important;
    }

    .epic-membership-modal-header {
        padding: 20px 20px 15px !important;
    }

    .epic-membership-modal-header h3 {
        font-size: 18px !important;
    }

    .epic-membership-modal-content {
        padding: 20px !important;
    }

    .epic-membership-modal-content p {
        font-size: 15px !important;
        margin-bottom: 20px !important;
    }

    .epic-membership-modal-actions {
        flex-direction: column !important;
        gap: 10px !important;
    }

    .epic-membership-modal-actions .button-primary,
    .epic-membership-modal-actions .epic-membership-upgrade-confirm,
    .epic-membership-modal-actions .button-secondary,
    .epic-membership-modal-actions .epic-membership-modal-cancel {
        width: 100% !important;
        text-align: center !important;
        justify-content: center !important;
    }
}

@media (max-width: 480px) {
    .epic-membership-modal-overlay {
        padding: 10px !important;
    }

    .epic-membership-modal {
        width: 100% !important;
        border-radius: 6px !important;
    }

    .epic-membership-modal-header {
        padding: 15px 15px 10px !important;
    }

    .epic-membership-modal-header h3 {
        font-size: 16px !important;
    }

    .epic-membership-modal-content {
        padding: 15px !important;
    }

    .epic-membership-modal-content p {
        font-size: 14px !important;
        margin-bottom: 15px !important;
    }
}

/* Theme Compatibility Enhancements */
.epic-membership-modal-overlay,
.epic-membership-modal-overlay *,
.epic-membership-modal,
.epic-membership-modal * {
    box-sizing: border-box !important;
}

/* Prevent theme interference with positioning */
html body .epic-membership-modal-overlay,
body .epic-membership-modal-overlay,
.wp-site-blocks .epic-membership-modal-overlay,
.site-content .epic-membership-modal-overlay,
#page .epic-membership-modal-overlay,
#main .epic-membership-modal-overlay,
.entry-content .epic-membership-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 2147483647 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: transparent !important;
    margin: 0 !important;
    padding: 20px !important;
    transform: none !important;
    float: none !important;
    clear: both !important;
}

/* Modal content protection */
html body .epic-membership-modal-overlay .epic-membership-modal,
body .epic-membership-modal-overlay .epic-membership-modal,
.wp-site-blocks .epic-membership-modal-overlay .epic-membership-modal,
.site-content .epic-membership-modal-overlay .epic-membership-modal,
#page .epic-membership-modal-overlay .epic-membership-modal,
#main .epic-membership-modal-overlay .epic-membership-modal,
.entry-content .epic-membership-modal-overlay .epic-membership-modal {
    position: relative !important;
    z-index: 2147483648 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    margin: 0 !important;
    float: none !important;
    clear: both !important;
}



.epic-membership-loading {

    text-align: center;

    padding: 20px;

}



.epic-membership-spinner {

    border: 3px solid #f3f3f3;

    border-top: 3px solid #ff5f5f;

    border-radius: 50%;

    width: 30px;

    height: 30px;

    animation: spin 1s linear infinite;

    margin: 0 auto 15px;

}



@keyframes spin {

    0% { transform: rotate(0deg); }

    100% { transform: rotate(360deg); }

}



.epic-membership-success {

    text-align: center;

    color: #28a745;

}



.epic-membership-error {

    text-align: center;

    color: #dc3545;

}



/* Payment Modal Styles */

.epic-membership-payment-modal {

    position: fixed;

    top: 0;

    left: 0;

    width: 100%;

    height: 100%;

    z-index: 10000;

    display: none;

}



.epic-membership-payment-modal .modal-overlay {

    position: absolute;

    top: 0;

    left: 0;

    width: 100%;

    height: 100%;

    background: transparent; /* No background overlay */

}



.epic-membership-payment-modal .modal-content {

    position: relative;

    background: white;

    max-width: 500px;

    margin: 50px auto;

    border-radius: 8px;

    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

    z-index: 10001;

}



.epic-membership-payment-modal .modal-header {

    padding: 20px;

    border-bottom: 1px solid #eee;

    display: flex;

    justify-content: space-between;

    align-items: center;

}



.epic-membership-payment-modal .modal-header h3 {

    margin: 0;

    font-size: 18px;

}



.epic-membership-payment-modal .modal-close {

    background: none;

    border: none;

    font-size: 24px;

    cursor: pointer;

    color: #666;

}



.epic-membership-payment-modal .modal-body {

    padding: 20px;

}



.epic-membership-payment-modal .payment-method {

    margin-bottom: 20px;

    padding: 15px;

    border: 1px solid #ddd;

    border-radius: 4px;

}



.epic-membership-payment-modal .payment-method h4 {

    margin: 0 0 10px 0;

    color: #333;

}



.epic-membership-payment-modal .payment-method p {

    margin: 0 0 15px 0;

    color: #666;

    font-size: 14px;

}



/* Enhanced Payment Modal Styles */

.epic-membership-payment-modal.enhanced .modal-content {

    max-width: 600px;

}



.epic-membership-payment-confirmation {

    margin-bottom: 20px;

    padding: 20px;

    background: #f8f9fa;

    border-radius: 8px;

    border: 1px solid #e9ecef;

}



.epic-membership-payment-confirmation .confirmation-header h3 {

    margin: 0 0 15px 0;

    color: #2c3e50;

    font-size: 18px;

}



.epic-membership-payment-confirmation .tier-info {

    margin-bottom: 20px;

    padding-bottom: 15px;

    border-bottom: 1px solid #dee2e6;

}



.epic-membership-payment-confirmation .tier-info h4 {

    margin: 0 0 8px 0;

    color: #007cba;

    font-size: 20px;

}



.epic-membership-payment-confirmation .tier-info p {

    margin: 0 0 15px 0;

    color: #6c757d;

    line-height: 1.5;

}



.epic-membership-payment-confirmation .price-info {

    display: flex;

    align-items: center;

    gap: 10px;

}



.epic-membership-payment-confirmation .price {

    font-size: 24px;

    font-weight: 700;

    color: #28a745;

}



.epic-membership-payment-confirmation .duration {

    font-size: 14px;

    color: #6c757d;

    background: #e9ecef;

    padding: 4px 8px;

    border-radius: 4px;

}



.epic-membership-payment-confirmation .benefits-preview h5 {

    margin: 0 0 10px 0;

    color: #495057;

    font-size: 16px;

}



.epic-membership-payment-confirmation .benefits-preview ul {

    margin: 0;

    padding-left: 20px;

    list-style-type: none;

}



.epic-membership-payment-confirmation .benefits-preview li {

    margin-bottom: 8px;

    color: #495057;

    position: relative;

}



.epic-membership-payment-confirmation .benefits-preview li::before {

    content: '✓';

    position: absolute;

    left: -20px;

    color: #28a745;

    font-weight: bold;

}



/* Payment Processing States */

.epic-membership-payment-processing {

    text-align: center;

    padding: 20px;

}



.epic-membership-payment-processing .processing-icon {

    font-size: 48px;

    color: #007cba;

    margin-bottom: 15px;

    animation: epic-membership-pulse 2s infinite;

}



@keyframes epic-membership-pulse {

    0% { opacity: 1; }

    50% { opacity: 0.5; }

    100% { opacity: 1; }

}



.epic-membership-payment-processing h3 {

    margin: 0 0 10px 0;

    color: #2c3e50;

}



.epic-membership-payment-processing p {

    margin: 0;

    color: #6c757d;

}



/* Payment Success/Error States */

.epic-membership-payment-result {

    text-align: center;

    padding: 30px;

}



.epic-membership-payment-result.success {

    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);

    border: 1px solid #28a745;

    color: #155724;

}



.epic-membership-payment-result.error {

    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);

    border: 1px solid #dc3545;

    color: #721c24;

}



.epic-membership-payment-result .result-icon {

    font-size: 64px;

    margin-bottom: 20px;

}



.epic-membership-payment-result h3 {

    margin: 0 0 15px 0;

    font-size: 24px;

}



.epic-membership-payment-result p {

    margin: 0;

    font-size: 16px;

    line-height: 1.5;

}



/* Payment History Styles */

.epic-membership-payment-history-section {

    margin-top: 30px;

}



.epic-membership-payment-history-list {

    display: flex;

    flex-direction: column;

    gap: 15px;

}



.epic-membership-payment-history-item {

    background: #f9f9f9;

    border: 1px solid #e0e0e0;

    border-radius: 8px;

    padding: 15px;

    display: grid;

    grid-template-columns: auto 1fr auto;

    gap: 15px;

    align-items: center;

}



.epic-membership-payment-history-item.completed {

    border-left: 4px solid #28a745;

}



.epic-membership-payment-history-item.pending {

    border-left: 4px solid #ffc107;

}



.epic-membership-payment-history-item.failed {

    border-left: 4px solid #dc3545;

}



.epic-membership-payment-history-item .payment-details {

    display: flex;

    flex-direction: column;

    gap: 4px;

}



.epic-membership-payment-history-item .payment-amount {

    font-weight: 600;

    font-size: 16px;

    color: #333;

}



.epic-membership-payment-history-item .payment-date,

.epic-membership-payment-history-item .payment-method {

    font-size: 14px;

    color: #666;

}



.epic-membership-payment-history-item .payment-status {

    text-align: right;

}



.epic-membership-payment-history-item .status-completed {

    color: #28a745;

    font-weight: 600;

}



.epic-membership-payment-history-item .status-pending {

    color: #ffc107;

    font-weight: 600;

}



.epic-membership-payment-history-item .status-failed {

    color: #dc3545;

    font-weight: 600;

}



.epic-membership-payment-history-item .payment-transaction {

    grid-column: 1 / -1;

    margin-top: 10px;

    padding-top: 10px;

    border-top: 1px solid #e0e0e0;

}



.epic-membership-payment-history-item .payment-transaction small {

    color: #888;

    font-size: 12px;

}



/* Message Styles */

.epic-membership-message {

    padding: 12px 16px;

    margin: 15px 0;

    border-radius: 4px;

    font-weight: 500;

}



.epic-membership-message-success {

    background: #d4edda;

    color: #155724;

    border: 1px solid #c3e6cb;

}



.epic-membership-message-error {

    background: #f8d7da;

    color: #721c24;

    border: 1px solid #f5c6cb;

}



.epic-membership-message-warning {

    background: #fff3cd;

    color: #856404;

    border: 1px solid #ffeaa7;

}



.epic-membership-message-info {

    background: #d1ecf1;

    color: #0c5460;

    border: 1px solid #bee5eb;

}



/* Activity Stats Section Styles */

.epic-membership-stats-section {

    background: #ffffff;

    border-radius: 12px;

    padding: 25px;

    margin-bottom: 30px;

    border: 1px solid #e9ecef;

    box-shadow: 0 2px 8px rgba(0,0,0,0.04);

    position: relative;

    overflow: hidden;

}



.epic-membership-stats-section::before {

    content: '';

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    height: 4px;

    background: linear-gradient(90deg, #007cba, #0073aa, #005177);

}



.epic-membership-stats-section .epic-membership-status-title {

    font-size: 20px;

    font-weight: 700;

    color: #2c3e50;

    margin: 0 0 20px 0;

    padding-bottom: 10px;

    border-bottom: 2px solid #f8f9fa;

}



.epic-membership-stats-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    gap: 20px;

    margin-top: 20px;

}



.epic-membership-stat-item {

    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

    border-radius: 10px;

    padding: 20px;

    text-align: center;

    border: 1px solid #e9ecef;

    transition: all 0.3s ease;

    position: relative;

    overflow: hidden;

}



.epic-membership-stat-item::before {

    content: '';

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    height: 3px;

    background: linear-gradient(90deg, #007cba, #0073aa);

}



.epic-membership-stat-item:hover {

    transform: translateY(-2px);

    box-shadow: 0 4px 15px rgba(0,0,0,0.1);

}



.epic-membership-stat-item .stat-number {

    font-size: 32px;

    font-weight: 700;

    color: #2c3e50;

    line-height: 1;

    margin-bottom: 8px;

    display: block;

}



.epic-membership-stat-item .stat-label {

    font-size: 14px;

    font-weight: 500;

    color: #6c757d;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    margin: 0;

}



/* Membership History Section Styles */

.epic-membership-history-section {

    background: #ffffff;

    border-radius: 12px;

    padding: 25px;

    margin-bottom: 30px;

    border: 1px solid #e9ecef;

    box-shadow: 0 2px 8px rgba(0,0,0,0.04);

    position: relative;

    overflow: hidden;

}



.epic-membership-history-section::before {

    content: '';

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    height: 4px;

    background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);

}



.epic-membership-history-section .epic-membership-status-title {

    font-size: 20px;

    font-weight: 700;

    color: #2c3e50;

    margin: 0 0 20px 0;

    padding-bottom: 10px;

    border-bottom: 2px solid #f8f9fa;

}



.epic-membership-history-list {

    display: flex;

    flex-direction: column;

    gap: 15px;

}



.epic-membership-history-item {

    background: #f8f9fa;

    border: 1px solid #e9ecef;

    border-radius: 10px;

    padding: 20px;

    display: flex;

    align-items: center;

    gap: 20px;

    transition: all 0.3s ease;

    position: relative;

}



.epic-membership-history-item:hover {

    background: #ffffff;

    box-shadow: 0 2px 10px rgba(0,0,0,0.08);

    transform: translateY(-1px);

}



.epic-membership-history-item.active {

    border-left: 4px solid #28a745;

    background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);

}



.epic-membership-history-item.inactive {

    border-left: 4px solid #6c757d;

    opacity: 0.8;

}



.epic-membership-history-item .history-tier {

    flex-shrink: 0;

}



.epic-membership-history-item .history-dates {

    flex: 1;

    display: flex;

    flex-direction: column;

    gap: 4px;

}



.epic-membership-history-item .history-start {

    font-weight: 600;

    color: #2c3e50;

    font-size: 14px;

}



.epic-membership-history-item .history-end {

    color: #6c757d;

    font-size: 13px;

}



.epic-membership-history-item .history-status {

    flex-shrink: 0;

    padding: 6px 12px;

    border-radius: 20px;

    font-size: 12px;

    font-weight: 600;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.epic-membership-history-item .history-status.active {

    background: #d4edda;

    color: #155724;

}



.epic-membership-history-item .history-status.inactive {

    background: #f8d7da;

    color: #721c24;

}



/* Status Text Styles within History Items */

.epic-membership-history-item .status-active {

    color: #28a745;

    font-weight: 600;

    font-size: 12px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    padding: 4px 8px;

    background: #d4edda;

    border-radius: 12px;

    border: 1px solid #c3e6cb;

}



.epic-membership-history-item .status-inactive {

    color: #dc3545;

    font-weight: 600;

    font-size: 12px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

    padding: 4px 8px;

    background: #f8d7da;

    border-radius: 12px;

    border: 1px solid #f5c6cb;

}



/* Enhanced Activity Section Animations */

.epic-membership-stats-section,

.epic-membership-history-section {

    animation: fadeInUp 0.6s ease-out;

}



@keyframes fadeInUp {

    from {

        opacity: 0;

        transform: translateY(20px);

    }

    to {

        opacity: 1;

        transform: translateY(0);

    }

}



.epic-membership-stat-item:nth-child(1) {

    animation-delay: 0.1s;

}



.epic-membership-stat-item:nth-child(2) {

    animation-delay: 0.2s;

}



.epic-membership-stat-item:nth-child(3) {

    animation-delay: 0.3s;

}



.epic-membership-history-item:nth-child(1) {

    animation: slideInLeft 0.6s ease-out 0.1s both;

}



.epic-membership-history-item:nth-child(2) {

    animation: slideInLeft 0.6s ease-out 0.2s both;

}



.epic-membership-history-item:nth-child(3) {

    animation: slideInLeft 0.6s ease-out 0.3s both;

}



.epic-membership-history-item:nth-child(4) {

    animation: slideInLeft 0.6s ease-out 0.4s both;

}



.epic-membership-history-item:nth-child(5) {

    animation: slideInLeft 0.6s ease-out 0.5s both;

}



@keyframes slideInLeft {

    from {

        opacity: 0;

        transform: translateX(-30px);

    }

    to {

        opacity: 1;

        transform: translateX(0);

    }

}



/* Loading States */

.epic-membership-stats-section.loading,

.epic-membership-history-section.loading {

    opacity: 0.6;

    pointer-events: none;

}



.epic-membership-stats-section.loading::after,

.epic-membership-history-section.loading::after {

    content: '';

    position: absolute;

    top: 50%;

    left: 50%;

    width: 20px;

    height: 20px;

    margin: -10px 0 0 -10px;

    border: 2px solid #f3f3f3;

    border-top: 2px solid #007cba;

    border-radius: 50%;

    animation: spin 1s linear infinite;

}



@keyframes spin {

    0% { transform: rotate(0deg); }

    100% { transform: rotate(360deg); }

}



/* Print Styles */

@media print {

    .epic-membership-protected-content,

    .epic-membership-countdown,

    .epic-membership-upgrade-button,

    .epic-membership-paypal-button,

    .epic-membership-kofi-button,

    .epic-membership-payment-modal,

    .epic-membership-modal-overlay,

    .epic-membership-modal,

    .menu-status-indicator {

        display: none !important;

    }

}



/* Responsive Design for PayPal */

@media (max-width: 768px) {

    .epic-membership-payment-modal .modal-content {

        margin: 20px;

        max-width: none;

    }



    .epic-membership-payment-history-item {

        grid-template-columns: 1fr;

        text-align: center;

    }



    .epic-membership-payment-history-item .payment-status {

        text-align: center;

    }



    /* Responsive Design for Upgrade Confirmation Modals */

    .epic-membership-modal {

        width: 95%;

        max-width: none;

        margin: 20px;

        max-height: 85vh;

    }



    .epic-membership-modal-header {

        padding: 20px 20px 15px;

    }



    .epic-membership-modal-header h3 {

        font-size: 18px;

    }



    .epic-membership-modal-content {

        padding: 20px;

    }



    .epic-membership-modal-content p {

        font-size: 15px;

        margin-bottom: 20px;

    }



    .epic-membership-modal-actions {

        flex-direction: column;

        gap: 10px;

        margin-top: 25px;

    }



    .epic-membership-modal-actions .button-primary,

    .epic-membership-modal-actions .epic-membership-upgrade-confirm,

    .epic-membership-modal-actions .button-secondary,

    .epic-membership-modal-actions .epic-membership-modal-cancel {

        width: 100%;

        text-align: center;

        padding: 14px 20px;

    }



    /* Activity Section Responsive */

    .epic-membership-stats-section,

    .epic-membership-history-section {

        padding: 20px;

        margin-bottom: 20px;

    }



    .epic-membership-stats-grid {

        grid-template-columns: 1fr;

        gap: 15px;

    }



    .epic-membership-stat-item {

        padding: 15px;

    }



    .epic-membership-stat-item .stat-number {

        font-size: 28px;

    }



    .epic-membership-history-item {

        flex-direction: column;

        align-items: flex-start;

        gap: 15px;

        padding: 15px;

    }



    .epic-membership-history-item .history-dates {

        width: 100%;

    }



    .epic-membership-history-item .history-status {

        align-self: flex-end;

    }



    .epic-membership-dashboard {

        margin: 10px 0;

    }



    .epic-membership-dashboard-content {

        padding: 20px;

    }



    .epic-membership-status-grid {

        grid-template-columns: 1fr;

        gap: 15px;

    }



    .epic-membership-status-item {

        padding: 12px;

    }



    .epic-membership-tier-grid {

        grid-template-columns: 1fr;

    }



    .epic-membership-tier-card {

        padding: 20px;

    }



    .epic-membership-tier-features {

        grid-template-columns: 1fr;

    }



    .epic-membership-upgrade-grid {

        grid-template-columns: 1fr;

    }



    .epic-membership-upgrade-card {

        padding: 20px;

    }



    /* Upgrade Options Responsive */

    .epic-membership-upgrade-options {

        grid-template-columns: 1fr;

        gap: 20px;

    }



    .epic-membership-upgrade-option {

        padding: 20px;

    }



    .epic-membership-upgrade-section {

        padding: 20px;

        margin: 20px 0;

    }



    .epic-membership-upgrade-section .epic-membership-status-title {

        font-size: 20px;

    }



    .upgrade-tier-price {

        font-size: 24px;

    }

}



/* Small Screen Responsive Design */

@media (max-width: 480px) {

    .epic-membership-stats-section .epic-membership-status-title,

    .epic-membership-history-section .epic-membership-status-title {

        font-size: 18px;

    }



    .epic-membership-stat-item .stat-number {

        font-size: 24px;

    }



    .epic-membership-stat-item .stat-label {

        font-size: 12px;

    }



    /* Small Screen Upgrade Options */

    .epic-membership-upgrade-section .epic-membership-status-title {

        font-size: 18px;

    }



    .epic-membership-upgrade-option {

        padding: 15px;

    }



    .upgrade-tier-price {

        padding: 10px 0;

    }



    .upgrade-tier-price .price-amount {

        font-size: 24px;

    }



    .upgrade-tier-action .epic-membership-upgrade-button {

        padding: 12px 16px;

        font-size: 14px;

    }



    .upgrade-tier-name .epic-membership-tier-badge {

        font-size: 12px;

        padding: 6px 12px;

    }

}



/* Extra Small Screens */

@media (max-width: 360px) {

    .epic-membership-upgrade-section {

        padding: 15px;

        margin: 15px 0;

    }



    .epic-membership-upgrade-options {

        gap: 15px;

    }



    .epic-membership-upgrade-option {

        padding: 12px;

    }



    .upgrade-tier-price .price-amount {

        font-size: 20px;

    }



    .epic-membership-upgrade-button .button-icon {

        display: none;

    }

}



    .epic-membership-history-item .history-start {

        font-size: 13px;

    }



    .epic-membership-history-item .history-end {

        font-size: 12px;

    }



    .epic-membership-stats-section,

    .epic-membership-history-section {

        padding: 15px;

        margin-bottom: 15px;

    }



    .epic-membership-stat-item {

        padding: 12px;

    }



    .epic-membership-history-item {

        padding: 12px;

    }

}



/* Upgrade Confirmation Modal Styles - NO BACKGROUND OVERLAY */

.epic-membership-modal-overlay {

    /* Full page positioning without background */

    position: fixed !important;

    top: 0 !important;

    left: 0 !important;

    right: 0 !important;

    bottom: 0 !important;

    width: 100vw !important;

    height: 100vh !important;

    background: transparent !important; /* No background overlay */

    z-index: 2147483647 !important; /* Maximum z-index value */



    /* Flexbox display for proper centering */

    display: flex !important;

    align-items: center !important;

    justify-content: center !important;



    /* Ensure overlay covers entire page for positioning only */

    margin: 0 !important;

    padding: 20px !important;

    border: none !important;

    outline: none !important;

    box-shadow: none !important;

    transform: none !important;

    box-sizing: border-box !important;



    /* Ensure modal appears above everything */

    pointer-events: auto !important;

    visibility: visible !important;

    opacity: 1 !important;



    /* Prevent theme interference */

    font-family: inherit !important;

    line-height: normal !important;

    text-align: left !important;

    direction: ltr !important;

}



.epic-membership-modal {

    /* Modal popup styling - centered with flexbox */

    background: white !important;

    border-radius: 12px !important;

    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;

    max-width: 500px !important;

    width: 100% !important;

    max-height: 90vh !important;

    overflow-y: auto !important;



    /* Positioning - relative for flexbox centering */

    position: relative !important;

    z-index: 2147483647 !important; /* Maximum z-index value */

    margin: 0 !important;

    transform: none !important;

    top: auto !important;

    left: auto !important;



    /* Display properties */

    display: block !important;

    visibility: visible !important;

    opacity: 1 !important;



    /* Prevent theme interference */

    float: none !important;

    clear: both !important;

    vertical-align: baseline !important;

    text-align: left !important;

    font-family: inherit !important;

    line-height: normal !important;

    color: inherit !important;



    /* Animation */

    animation: modalSlideIn 0.3s ease-out;

    /* Prevent layout issues */

    min-width: 300px !important;

    min-height: 200px !important;

    box-sizing: border-box !important;

}



@keyframes modalSlideIn {

    from {

        opacity: 0;

        transform: scale(0.9) translateY(-20px);

    }

    to {

        opacity: 1;

        transform: scale(1) translateY(0);

    }

}



.epic-membership-modal-header {

    padding: 25px 30px 20px;

    border-bottom: 1px solid #e9ecef;

    display: flex;

    justify-content: space-between;

    align-items: center;

    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

    border-radius: 12px 12px 0 0;

}



.epic-membership-modal-header h3 {

    margin: 0;

    font-size: 20px;

    font-weight: 600;

    color: #2c3e50;

}



.epic-membership-modal-close {

    background: none;

    border: none;

    font-size: 28px;

    cursor: pointer;

    color: #6c757d;

    padding: 0;

    width: 32px;

    height: 32px;

    display: flex;

    align-items: center;

    justify-content: center;

    border-radius: 50%;

    transition: all 0.2s ease;

}



.epic-membership-modal-close:hover {

    background: #f8f9fa;

    color: #495057;

    transform: scale(1.1);

}



.epic-membership-modal-content {

    padding: 30px;

}



.epic-membership-modal-content p {

    margin: 0 0 25px 0;

    font-size: 16px;

    line-height: 1.6;

    color: #495057;

}



.epic-membership-modal-actions {

    display: flex;

    gap: 15px;

    justify-content: flex-end;

    margin-top: 30px;

}



.epic-membership-modal-actions .button-primary,

.epic-membership-modal-actions .epic-membership-upgrade-confirm {

    background: linear-gradient(135deg, #007cba 0%, #0073aa 100%);

    color: white;

    border: none;

    padding: 12px 24px;

    border-radius: 6px;

    font-size: 14px;

    font-weight: 600;

    cursor: pointer;

    transition: all 0.3s ease;

    text-decoration: none;

    display: inline-block;

}



.epic-membership-modal-actions .button-primary:hover,

.epic-membership-modal-actions .epic-membership-upgrade-confirm:hover {

    background: linear-gradient(135deg, #005a87 0%, #004d73 100%);

    transform: translateY(-1px);

    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);

}



.epic-membership-modal-actions .button-secondary,

.epic-membership-modal-actions .epic-membership-modal-cancel {

    background: #f8f9fa;

    color: #495057;

    border: 1px solid #dee2e6;

    padding: 12px 24px;

    border-radius: 6px;

    font-size: 14px;

    font-weight: 500;

    cursor: pointer;

    transition: all 0.3s ease;

    text-decoration: none;

    display: inline-block;

}



.epic-membership-modal-actions .button-secondary:hover,

.epic-membership-modal-actions .epic-membership-modal-cancel:hover {

    background: #e9ecef;

    border-color: #adb5bd;

    transform: translateY(-1px);

}



/* Message Notifications */

.epic-membership-message {

    position: fixed;

    top: 20px;

    right: 20px;

    padding: 15px 20px;

    border-radius: 5px;

    color: white;

    font-weight: 600;

    z-index: 10000;

    max-width: 400px;

    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    display: none;

}



.epic-membership-message.epic-membership-success {

    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);

}



.epic-membership-message.epic-membership-error {

    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);

}



/* Modal Theme Override Protection */

/* Prevent WordPress themes from interfering with modal positioning */

body.epic-membership-modal-open {

    overflow: hidden !important;

    position: relative !important;

}



/* Ultra-high specificity modal overlay protection */

html body .epic-membership-modal-overlay,

body .epic-membership-modal-overlay,

.wp-site-blocks .epic-membership-modal-overlay,

.site-content .epic-membership-modal-overlay,

#page .epic-membership-modal-overlay,

#main .epic-membership-modal-overlay,

.entry-content .epic-membership-modal-overlay {

    position: fixed !important;

    top: 0 !important;

    left: 0 !important;

    right: 0 !important;

    bottom: 0 !important;

    width: 100vw !important;

    height: 100vh !important;

    z-index: 999999 !important;

    display: block !important;

    background: transparent !important; /* No background overlay */

    margin: 0 !important;

    padding: 0 !important;

    border: none !important;

    outline: none !important;

    box-shadow: none !important;

    transform: none !important;

    clip: auto !important;

    overflow: visible !important;

}



/* Ultra-high specificity modal content protection */

html body .epic-membership-modal-overlay .epic-membership-modal,

body .epic-membership-modal-overlay .epic-membership-modal,

.wp-site-blocks .epic-membership-modal-overlay .epic-membership-modal,

.site-content .epic-membership-modal-overlay .epic-membership-modal,

#page .epic-membership-modal-overlay .epic-membership-modal,

#main .epic-membership-modal-overlay .epic-membership-modal,

.entry-content .epic-membership-modal-overlay .epic-membership-modal {

    position: relative !important;

    z-index: 1000000 !important;

    background: white !important;

    border-radius: 12px !important;

    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;

    max-width: 500px !important;

    width: 100% !important;

    max-height: 90vh !important;

    overflow-y: auto !important;

    margin: 0 !important;

    padding: 0 !important;

    border: none !important;

    outline: none !important;

    transform: none !important;

    clip: auto !important;

    display: block !important;

    visibility: visible !important;

    opacity: 1 !important;

    top: auto !important;

    left: auto !important;

}



.epic-membership-message.epic-membership-info {

    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);

}



/* NUCLEAR THEME COMPATIBILITY - MAXIMUM SPECIFICITY MODAL PROTECTION */

/* These rules use the highest possible CSS specificity to override any theme interference */



/* Body class when modal is open - prevent scrolling */

html.epic-membership-modal-open,

html body.epic-membership-modal-open {

    overflow: hidden !important;

    position: relative !important;

    height: 100% !important;

}



/* Overlay protection with maximum specificity */

html body div.epic-membership-modal-overlay,

html body.wp-admin div.epic-membership-modal-overlay,

html body.wp-core-ui div.epic-membership-modal-overlay,

html body #page div.epic-membership-modal-overlay,

html body #main div.epic-membership-modal-overlay,

html body .site div.epic-membership-modal-overlay,

html body .site-content div.epic-membership-modal-overlay,

html body .entry-content div.epic-membership-modal-overlay,

html body .post-content div.epic-membership-modal-overlay,

html body .page-content div.epic-membership-modal-overlay,

html body article div.epic-membership-modal-overlay,

html body section div.epic-membership-modal-overlay,

html body main div.epic-membership-modal-overlay {

    position: fixed !important;

    top: 0 !important;

    left: 0 !important;

    right: 0 !important;

    bottom: 0 !important;

    width: 100vw !important;

    height: 100vh !important;

    z-index: 2147483647 !important;

    display: flex !important;

    align-items: center !important;

    justify-content: center !important;

    background: transparent !important; /* No background overlay */

    margin: 0 !important;

    padding: 20px !important;

    box-sizing: border-box !important;

    border: none !important;

    outline: none !important;

    box-shadow: none !important;

    transform: none !important;

    clip: auto !important;

    overflow: visible !important;

    visibility: visible !important;

    opacity: 1 !important;

    pointer-events: auto !important;

}



/* Modal content protection with maximum specificity */

html body div.epic-membership-modal-overlay div.epic-membership-modal,

html body.wp-admin div.epic-membership-modal-overlay div.epic-membership-modal,

html body.wp-core-ui div.epic-membership-modal-overlay div.epic-membership-modal,

html body #page div.epic-membership-modal-overlay div.epic-membership-modal,

html body #main div.epic-membership-modal-overlay div.epic-membership-modal,

html body .site div.epic-membership-modal-overlay div.epic-membership-modal,

html body .site-content div.epic-membership-modal-overlay div.epic-membership-modal,

html body .entry-content div.epic-membership-modal-overlay div.epic-membership-modal,

html body .post-content div.epic-membership-modal-overlay div.epic-membership-modal,

html body .page-content div.epic-membership-modal-overlay div.epic-membership-modal,

html body article div.epic-membership-modal-overlay div.epic-membership-modal,

html body section div.epic-membership-modal-overlay div.epic-membership-modal,

html body main div.epic-membership-modal-overlay div.epic-membership-modal {

    position: relative !important;

    z-index: 2147483647 !important;

    background: white !important;

    border-radius: 12px !important;

    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;

    max-width: 500px !important;

    width: 100% !important;

    max-height: 90vh !important;

    overflow-y: auto !important;

    margin: 0 !important;

    padding: 0 !important;

    border: none !important;

    outline: none !important;

    transform: none !important;

    clip: auto !important;

    display: block !important;

    visibility: visible !important;

    opacity: 1 !important;

    float: none !important;

    clear: both !important;

    vertical-align: baseline !important;

    text-align: left !important;

    font-family: inherit !important;

    line-height: normal !important;

    color: inherit !important;

    min-width: 300px !important;

    min-height: 200px !important;

    box-sizing: border-box !important;

}



/* Prevent any theme from hiding the modal */

.epic-membership-modal-overlay,

.epic-membership-modal-overlay *,

.epic-membership-modal,

.epic-membership-modal * {

    display: block !important;

    visibility: visible !important;

    opacity: 1 !important;

}



/* Special handling for flexbox items */

.epic-membership-modal-overlay {

    display: block !important;

}



.epic-membership-modal-actions {

    display: flex !important;

}



/* Prevent theme CSS from affecting modal buttons */

.epic-membership-modal-actions .button-primary,

.epic-membership-modal-actions .epic-membership-upgrade-confirm,

.epic-membership-modal-actions .button-secondary,

.epic-membership-modal-actions .epic-membership-modal-cancel {

    display: inline-block !important;

    visibility: visible !important;

    opacity: 1 !important;

    position: relative !important;

    z-index: auto !important;

    float: none !important;

    clear: none !important;

    margin: 0 !important;

    transform: none !important;

}



/* Force modal to appear above admin bar and any theme elements */

.epic-membership-modal-overlay {

    z-index: 2147483647 !important;

}



/* Ensure modal works with common theme frameworks */

.twentytwentyone .epic-membership-modal-overlay,

.twentytwentytwo .epic-membership-modal-overlay,

.twentytwentythree .epic-membership-modal-overlay,

.twentytwentyfour .epic-membership-modal-overlay,

.astra-theme .epic-membership-modal-overlay,

.generatepress .epic-membership-modal-overlay,

.oceanwp-theme .epic-membership-modal-overlay,

.kadence-theme .epic-membership-modal-overlay,

.neve-theme .epic-membership-modal-overlay {

    position: fixed !important;

    top: 0 !important;

    left: 0 !important;

    right: 0 !important;

    bottom: 0 !important;

    width: 100vw !important;

    height: 100vh !important;

    background: transparent !important; /* No background overlay */

    z-index: 2147483647 !important;

    display: block !important;

}



/* Theme-specific modal fixes */



/* Elementor Theme Fixes */

body.epic-modal-elementor-fix .epic-membership-modal-overlay {

    position: fixed !important;

    top: 0 !important;

    left: 0 !important;

    right: 0 !important;

    bottom: 0 !important;

    width: 100vw !important;

    height: 100vh !important;

    background: transparent !important; /* No background overlay */

    z-index: 2147483647 !important;

    display: block !important;

}



body.epic-modal-elementor-fix .elementor-section,

body.epic-modal-elementor-fix .elementor-container {

    transform: none !important;

}



/* Divi Theme Fixes */

body.epic-modal-divi-fix .epic-membership-modal-overlay {

    position: fixed !important;

    top: 0 !important;

    left: 0 !important;

    right: 0 !important;

    bottom: 0 !important;

    width: 100vw !important;

    height: 100vh !important;

    background: transparent !important; /* No background overlay */

    z-index: 2147483647 !important;

    display: block !important;

}



body.epic-modal-divi-fix #et-main-area,

body.epic-modal-divi-fix .et_pb_section {

    transform: none !important;

}



/* Avada Theme Fixes */

body.epic-modal-avada-fix .fusion-header,

body.epic-modal-avada-fix .fusion-main {

    z-index: 999 !important;

}



body.epic-modal-avada-fix .epic-membership-modal-overlay {

    z-index: 2147483647 !important;

}



/* Astra Theme Fixes */

body.epic-modal-astra-fix .ast-container,

body.epic-modal-astra-fix .site-content {

    transform: none !important;

}



/* GeneratePress Theme Fixes */

body.epic-modal-generatepress-fix .site-content,

body.epic-modal-generatepress-fix .inside-article {

    transform: none !important;

}



/* OceanWP Theme Fixes */

body.epic-modal-oceanwp-fix .oceanwp-modal,

body.epic-modal-oceanwp-fix .sidr-overlay {

    z-index: 999 !important;

}



/* Removed old fallback modal styles - replaced with Ko-fi style implementation */



/* Enhanced viewport compatibility */

@supports not (width: 100vw) {

    .epic-membership-modal-overlay {

        width: 100% !important;

        height: 100% !important;

    }

}



/* Enhanced transform compatibility */

@supports (transform: translate3d(0,0,0)) {

    .epic-membership-modal-overlay {

        transform: translate3d(0,0,0) !important;

    }

}



/* CSS Grid compatibility fixes */

.wp-site-blocks.epic-membership-modal-open,

.wp-block-group.epic-membership-modal-open {

    display: block !important;

}



/* Flexbox compatibility fixes */

.epic-membership-modal-overlay {

    display: block !important;

}



/* Alternative Modal Strategies */



/* Removed old absolute modal styles - replaced with Ko-fi style implementation */



/* Removed old table modal styles - replaced with Ko-fi style implementation */



/* Removed old enhanced fallback modal and admin bar compatibility styles - replaced with Ko-fi style implementation */



/* Ko-fi Enhanced Modal Styles */

.epic-membership-kofi-instructions {

    text-align: left;

}



.kofi-tier-info {

    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    padding: 20px;

    border-radius: 12px;

    margin-bottom: 24px;

    border-left: 4px solid #13c3ff;

    box-shadow: 0 2px 8px rgba(19, 195, 255, 0.1);

}



.kofi-tier-info h4 {

    margin: 0 0 8px 0;

    color: #2c3e50;

    font-size: 18px;

    font-weight: 600;

}



.tier-price {

    margin: 0;

    font-size: 24px;

    color: #13c3ff;

    font-weight: bold;

}



.kofi-payment-steps {

    margin-bottom: 24px;

}



.kofi-payment-steps h4 {

    color: #2c3e50;

    margin-bottom: 16px;

    font-size: 16px;

    font-weight: 600;

    display: flex;

    align-items: center;

}



.kofi-payment-steps ol {

    padding-left: 24px;

    line-height: 1.8;

    counter-reset: step-counter;

}



.kofi-payment-steps li {

    margin-bottom: 12px;

    color: #495057;

    font-size: 15px;

    position: relative;

    counter-increment: step-counter;

}



.kofi-payment-steps li::marker {

    font-weight: bold;

    color: #13c3ff;

}



.highlight-amount {

    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);

    padding: 4px 8px;

    border-radius: 6px;

    color: #856404;

    font-weight: bold;

    border: 1px solid #ffeaa7;

}



.kofi-amount-notice {

    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);

    border: 1px solid #ffeaa7;

    border-radius: 12px;

    padding: 20px;

    margin-bottom: 24px;

    position: relative;

}



.kofi-amount-notice::before {

    content: "⚠️";

    position: absolute;

    top: 20px;

    left: 20px;

    font-size: 20px;

}



.kofi-amount-notice p {

    margin: 0 0 0 40px;

    color: #856404;

    font-size: 15px;

    font-weight: 500;

}



.modal-actions {

    display: flex;

    gap: 16px;

    justify-content: center;

    margin-top: 32px;

}



.kofi-proceed-btn {

    background: linear-gradient(135deg, #13c3ff 0%, #0099cc 100%);

    color: white;

    border: none;

    padding: 16px 32px;

    border-radius: 12px;

    font-size: 16px;

    font-weight: 600;

    cursor: pointer;

    transition: all 0.3s ease;

    box-shadow: 0 4px 16px rgba(19, 195, 255, 0.3);

    position: relative;

    overflow: hidden;

}



.kofi-proceed-btn::before {

    content: '';

    position: absolute;

    top: 0;

    left: -100%;

    width: 100%;

    height: 100%;

    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);

    transition: left 0.5s;

}



.kofi-proceed-btn:hover {

    transform: translateY(-2px);

    box-shadow: 0 6px 20px rgba(19, 195, 255, 0.4);

}



.kofi-proceed-btn:hover::before {

    left: 100%;

}



.kofi-cancel-btn {

    background: #6c757d;

    color: white;

    border: none;

    padding: 16px 32px;

    border-radius: 12px;

    font-size: 16px;

    cursor: pointer;

    transition: all 0.3s ease;

}



.kofi-cancel-btn:hover {

    background: #5a6268;

    transform: translateY(-1px);

}



/* Success Message Enhanced Styles */

.epic-membership-success {

    text-align: center;

}



.success-icon {

    font-size: 64px;

    margin-bottom: 20px;

    animation: successPulse 2s infinite;

}



@keyframes successPulse {

    0%, 100% { transform: scale(1); }

    50% { transform: scale(1.1); }

}



.epic-membership-success h4 {

    color: #28a745;

    margin-bottom: 24px;

    font-size: 20px;

    font-weight: 600;

}



.payment-reminder {

    background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);

    border: 1px solid #b3d9ff;

    border-radius: 12px;

    padding: 20px;

    margin-bottom: 24px;

    text-align: left;

}



.payment-reminder h5 {

    margin: 0 0 16px 0;

    color: #0066cc;

    font-size: 16px;

    font-weight: 600;

    display: flex;

    align-items: center;

}



.payment-reminder ul {

    margin: 0;

    padding-left: 24px;

}



.payment-reminder li {

    margin-bottom: 10px;

    color: #2c3e50;

    line-height: 1.6;

    font-size: 15px;

}



.auto-activation {

    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);

    border: 1px solid #c3e6cb;

    border-radius: 12px;

    padding: 20px;

    margin-bottom: 20px;

}



.auto-activation p {

    margin: 0 0 8px 0;

    color: #155724;

    font-weight: 500;

}



.auto-activation p:last-child {

    margin-bottom: 0;

    font-size: 14px;

    opacity: 0.8;

}



.help-links {

    margin-top: 20px;

    padding-top: 20px;

    border-top: 1px solid #dee2e6;

}



.help-links p {

    margin: 0;

    color: #6c757d;

    font-size: 14px;

}



.help-links a {

    color: #007bff;

    text-decoration: none;

    font-weight: 500;

}



.help-links a:hover {

    text-decoration: underline;

}



/* Responsive Ko-fi Modal */

@media (max-width: 768px) {

    .epic-membership-modal {

        width: 95% !important;

        margin: 20px !important;

    }



    .kofi-tier-info {

        padding: 16px;

    }



    .kofi-tier-info h4 {

        font-size: 16px;

    }



    .tier-price {

        font-size: 20px;

    }



    .kofi-payment-steps ol {

        padding-left: 20px;

    }



    .kofi-payment-steps li {

        font-size: 14px;

    }



    .modal-actions {

        flex-direction: column;

        gap: 12px;

    }



    .kofi-proceed-btn,

    .kofi-cancel-btn {

        width: 100%;

        padding: 14px 24px;

    }



    .payment-reminder,

    .auto-activation {

        padding: 16px;

    }

}

/* ========================================
   UPGRADE TIER MODAL STYLES
   Following Ko-fi Modal Patterns
   ======================================== */

/* Upgrade Tier Modal Content */
.epic-membership-upgrade-instructions {
    text-align: left;
}

.upgrade-tier-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    text-align: center;
}

.upgrade-tier-info h4 {
    margin: 0 0 10px 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
}

.upgrade-tier-info .tier-price {
    margin: 0 !important;
    font-size: 16px !important;
    color: #495057 !important;
}

.upgrade-tier-info .tier-price strong {
    color: #007cba !important;
    font-size: 20px !important;
}

.tier-description {
    margin-bottom: 25px !important;
}

.tier-description p {
    margin: 0 !important;
    padding: 15px !important;
    background: #e3f2fd !important;
    border-left: 4px solid #2196f3 !important;
    border-radius: 4px !important;
    font-style: italic !important;
    color: #1565c0 !important;
}

.upgrade-confirmation {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.upgrade-confirmation p {
    margin: 0 0 10px 0 !important;
    color: #856404 !important;
}

.upgrade-confirmation p:last-child {
    margin-bottom: 0 !important;
    font-size: 14px !important;
    color: #6c757d !important;
}

/* Modal Actions - Following Ko-fi Pattern */
.modal-actions {
    display: flex !important;
    gap: 15px !important;
    justify-content: center !important;
    margin-top: 30px !important;
}

.upgrade-confirm-btn,
.upgrade-cancel-btn {
    padding: 12px 30px !important;
    border: none !important;
    border-radius: 6px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    text-align: center !important;
}

.upgrade-confirm-btn {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(0, 124, 186, 0.3) !important;
}

.upgrade-confirm-btn:hover {
    background: linear-gradient(135deg, #005a87 0%, #004666 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.4) !important;
    color: white !important;
}

.upgrade-cancel-btn {
    background: #6c757d !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
}

.upgrade-cancel-btn:hover {
    background: #5a6268 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
    color: white !important;
}

/* Loading State */
.epic-membership-upgrade-instructions .epic-membership-loading {
    text-align: center !important;
    padding: 40px 20px !important;
}

.epic-membership-upgrade-instructions .epic-membership-loading p {
    margin: 0 !important;
    font-size: 16px !important;
    color: #495057 !important;
}

/* Message Notifications */
.epic-membership-message {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    padding: 15px 20px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    z-index: 2147483648 !important;
    max-width: 400px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    display: none !important;
}

.epic-membership-message-success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.epic-membership-message-error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

/* Responsive Design for Upgrade Modal */
@media (max-width: 768px) {
    .upgrade-tier-info {
        padding: 15px;
        margin-bottom: 20px;
    }

    .upgrade-tier-info h4 {
        font-size: 16px !important;
    }

    .upgrade-confirmation {
        padding: 15px;
        margin-bottom: 20px;
    }

    .modal-actions {
        flex-direction: column;
        gap: 10px;
    }

    .upgrade-confirm-btn,
    .upgrade-cancel-btn {
        width: 100%;
        padding: 14px 20px !important;
    }
}

@media (max-width: 480px) {
    .upgrade-tier-info {
        padding: 12px;
        margin-bottom: 15px;
    }

    .upgrade-tier-info h4 {
        font-size: 14px !important;
    }

    .upgrade-tier-info .tier-price strong {
        font-size: 18px !important;
    }

    .upgrade-confirmation {
        padding: 12px;
        margin-bottom: 15px;
    }

    .epic-membership-message {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
    }
}

