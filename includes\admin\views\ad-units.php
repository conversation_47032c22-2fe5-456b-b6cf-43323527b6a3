<?php

/**

 * Ad Units Admin View

 * Interface for managing multiple Adsterra ad units

 */



if (!defined('ABSPATH')) {

    exit;

}

?>



<div class="wrap">

    <h1><?php _e('Adsterra Ad Units', 'epic-membership'); ?>

        <button type="button" class="page-title-action" id="add-new-ad-unit">

            <?php _e('Add New Ad Unit', 'epic-membership'); ?>

        </button>

    </h1>







    <div class="epic-membership-ad-units-container">

        

        <!-- Ad Units List -->

        <div class="ad-units-list">

            <h2><?php _e('Existing Ad Units', 'epic-membership'); ?></h2>

            

            <?php if (empty($ad_units)): ?>

                <div class="no-ad-units">

                    <p><?php _e('No ad units found. Click "Add New Ad Unit" to create your first ad unit.', 'epic-membership'); ?></p>

                    <p class="description">

                        <?php _e('Ad units allow you to manage multiple Adsterra advertisements with different placements and types.', 'epic-membership'); ?>

                    </p>

                </div>

            <?php else: ?>

                <div class="ad-units-grid" id="ad-units-sortable">

                    <?php foreach ($ad_units as $ad_unit): ?>

                        <div class="ad-unit-card" data-ad-unit-id="<?php echo esc_attr($ad_unit->id); ?>">

                            <div class="ad-unit-header">

                                <div class="ad-unit-drag-handle">⋮⋮</div>

                                <h3 class="ad-unit-name"><?php echo esc_html($ad_unit->name); ?></h3>

                                <div class="ad-unit-status">

                                    <label class="toggle-switch">

                                        <input type="checkbox" class="ad-unit-toggle" 

                                               data-ad-unit-id="<?php echo esc_attr($ad_unit->id); ?>"

                                               <?php checked($ad_unit->is_active, 1); ?>>

                                        <span class="toggle-slider"></span>

                                    </label>

                                </div>

                            </div>

                            

                            <div class="ad-unit-details">

                                <div class="ad-unit-meta">

                                    <span class="ad-type-badge ad-type-<?php echo esc_attr($ad_unit->ad_type); ?>">

                                        <?php echo esc_html($ad_types[$ad_unit->ad_type] ?? $ad_unit->ad_type); ?>

                                    </span>

                                    <span class="placement-badge">

                                        <?php echo esc_html($placements[$ad_unit->placement] ?? $ad_unit->placement); ?>

                                    </span>

                                </div>

                                

                                <div class="ad-unit-code-preview">

                                    <code><?php echo esc_html(wp_trim_words($ad_unit->ad_code, 10, '...')); ?></code>

                                </div>

                                

                                <div class="ad-unit-actions">

                                    <button type="button" class="button edit-ad-unit" 

                                            data-ad-unit-id="<?php echo esc_attr($ad_unit->id); ?>">

                                        <?php _e('Edit', 'epic-membership'); ?>

                                    </button>

                                    <button type="button" class="button button-link-delete delete-ad-unit" 

                                            data-ad-unit-id="<?php echo esc_attr($ad_unit->id); ?>">

                                        <?php _e('Delete', 'epic-membership'); ?>

                                    </button>

                                </div>

                            </div>

                        </div>

                    <?php endforeach; ?>

                </div>

                

                <div class="ad-units-help">

                    <p class="description">

                        <?php _e('Drag and drop ad units to reorder them. The order affects display priority for ads in the same placement.', 'epic-membership'); ?>

                    </p>

                </div>

            <?php endif; ?>

        </div>

        

        <!-- Ad Unit Form Modal -->

        <div id="ad-unit-modal" class="epic-modal" style="display: none;">

            <div class="epic-modal-content">

                <div class="epic-modal-header">

                    <h2 id="modal-title"><?php _e('Add New Ad Unit', 'epic-membership'); ?></h2>

                    <button type="button" class="epic-modal-close">&times;</button>

                </div>

                

                <div class="epic-modal-body">

                    <form id="ad-unit-form">

                        <input type="hidden" id="ad-unit-id" name="ad_unit_id" value="">

                        

                        <table class="form-table">

                            <tr>

                                <th scope="row">

                                    <label for="ad-unit-name"><?php _e('Ad Unit Name', 'epic-membership'); ?></label>

                                </th>

                                <td>

                                    <input type="text" id="ad-unit-name" name="name" class="regular-text" required>

                                    <p class="description"><?php _e('A descriptive name for this ad unit (e.g., "Homepage Popunder", "Article Banner").', 'epic-membership'); ?></p>

                                </td>

                            </tr>

                            

                            <tr>

                                <th scope="row">

                                    <label for="ad-unit-type"><?php _e('Ad Type', 'epic-membership'); ?></label>

                                </th>

                                <td>

                                    <select id="ad-unit-type" name="ad_type" class="regular-text" required>

                                        <option value=""><?php _e('Select ad type...', 'epic-membership'); ?></option>

                                        <?php foreach ($ad_types as $type => $label): ?>

                                            <option value="<?php echo esc_attr($type); ?>"><?php echo esc_html($label); ?></option>

                                        <?php endforeach; ?>

                                    </select>

                                    <p class="description"><?php _e('Choose the type of Adsterra ad format.', 'epic-membership'); ?></p>

                                </td>

                            </tr>

                            

                            <tr>

                                <th scope="row">

                                    <label for="ad-unit-placement"><?php _e('Placement', 'epic-membership'); ?></label>

                                </th>

                                <td>

                                    <select id="ad-unit-placement" name="placement" class="regular-text" required>

                                        <option value=""><?php _e('Select placement...', 'epic-membership'); ?></option>

                                        <?php foreach ($placements as $placement => $label): ?>

                                            <option value="<?php echo esc_attr($placement); ?>"><?php echo esc_html($label); ?></option>

                                        <?php endforeach; ?>

                                    </select>

                                    <p class="description"><?php _e('Choose where this ad should be displayed on your site.', 'epic-membership'); ?></p>

                                </td>

                            </tr>

                            

                            <tr>

                                <th scope="row">

                                    <label for="ad-unit-code"><?php _e('Ad Code', 'epic-membership'); ?></label>

                                </th>

                                <td>

                                    <textarea id="ad-unit-code" name="ad_code" rows="8" class="large-text code" required

                                              placeholder="<script type='text/javascript' src='//your-adsterra-domain.com/path/to/your-script.js'></script>"></textarea>

                                    <p class="description">

                                        <?php _e('Paste your Adsterra ad code here. Get this from your Adsterra publisher dashboard.', 'epic-membership'); ?>

                                    </p>

                                </td>

                            </tr>

                            

                            <tr>

                                <th scope="row">

                                    <label for="ad-unit-active"><?php _e('Status', 'epic-membership'); ?></label>

                                </th>

                                <td>

                                    <label>

                                        <input type="checkbox" id="ad-unit-active" name="is_active" value="1" checked>

                                        <?php _e('Active (ads will be displayed)', 'epic-membership'); ?>

                                    </label>

                                    <p class="description"><?php _e('Uncheck to disable this ad unit without deleting it.', 'epic-membership'); ?></p>

                                </td>

                            </tr>

                        </table>

                    </form>

                </div>

                

                <div class="epic-modal-footer">

                    <button type="button" class="button button-primary" id="save-ad-unit">

                        <?php _e('Save Ad Unit', 'epic-membership'); ?>

                    </button>

                    <button type="button" class="button" id="cancel-ad-unit">

                        <?php _e('Cancel', 'epic-membership'); ?>

                    </button>

                </div>

            </div>

        </div>

    </div>

    

    <!-- Help Section -->

    <div class="epic-membership-help-section">

        <h3><?php _e('Ad Unit Management Guide', 'epic-membership'); ?></h3>

        

        <div class="help-columns">

            <div class="help-column">

                <h4><?php _e('Ad Types', 'epic-membership'); ?></h4>

                <ul>

                    <li><strong><?php _e('Popunder:', 'epic-membership'); ?></strong> <?php _e('High revenue, opens in background', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Social Bar:', 'epic-membership'); ?></strong> <?php _e('Non-intrusive bottom bar', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Banner:', 'epic-membership'); ?></strong> <?php _e('Traditional display ads', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Native:', 'epic-membership'); ?></strong> <?php _e('Content-integrated ads', 'epic-membership'); ?></li>

                    <li><strong><?php _e('In-Page Push:', 'epic-membership'); ?></strong> <?php _e('Push notification style', 'epic-membership'); ?></li>

                </ul>

            </div>

            

            <div class="help-column">

                <h4><?php _e('Placements', 'epic-membership'); ?></h4>

                <ul>

                    <li><strong><?php _e('Header:', 'epic-membership'); ?></strong> <?php _e('Inserted in HTML head section', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Footer:', 'epic-membership'); ?></strong> <?php _e('Inserted before closing body tag', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Before Content:', 'epic-membership'); ?></strong> <?php _e('Above post/page content', 'epic-membership'); ?></li>

                    <li><strong><?php _e('After Content:', 'epic-membership'); ?></strong> <?php _e('Below post/page content', 'epic-membership'); ?></li>

                    <li><strong><?php _e('Sidebar:', 'epic-membership'); ?></strong> <?php _e('Use with widget or theme function', 'epic-membership'); ?></li>

                </ul>

            </div>

        </div>

        

        <div class="help-note">

            <p><strong><?php _e('Note:', 'epic-membership'); ?></strong> 

               <?php _e('All ad units are automatically hidden for premium members with ad-free capability. You can manage multiple ad units of the same type with different placements for better monetization.', 'epic-membership'); ?>

            </p>

        </div>

    </div>

</div>

