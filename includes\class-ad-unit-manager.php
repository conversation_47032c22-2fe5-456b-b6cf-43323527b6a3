<?php

/**

 * Ad Unit Manager Class for Epic Membership Plugin

 * Handles CRUD operations for multiple Adsterra ad units

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Ad_Unit_Manager {

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Supported ad types

     */

    private $supported_ad_types = array(

        'popunder' => 'Popunder Ads',

        'social_bar' => 'Social Bar Ads',

        'banner' => 'Banner Ads',

        'native' => 'Native Ads',

        'in_page_push' => 'In-Page Push Ads'

    );

    

    /**

     * Supported placements

     */

    private $supported_placements = array(

        'header' => 'Header (wp_head)',

        'footer' => 'Footer (wp_footer)',

        'body_end' => 'Before Closing Body Tag',

        'before_content' => 'Before Content',

        'after_content' => 'After Content',

        'sidebar' => 'Sidebar',

        'custom' => 'Custom Hook'

    );

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

    }

    

    /**

     * Get all ad units

     */

    public function get_ad_units($active_only = false) {

        global $wpdb;

        

        $table_name = $this->database->get_table('ad_units');

        $where_clause = $active_only ? 'WHERE is_active = 1' : '';

        

        $sql = "SELECT * FROM $table_name $where_clause ORDER BY display_order ASC, id ASC";

        

        return $wpdb->get_results($sql);

    }

    

    /**

     * Get ad unit by ID

     */

    public function get_ad_unit($id) {

        global $wpdb;

        

        $table_name = $this->database->get_table('ad_units');

        

        return $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $table_name WHERE id = %d",

            $id

        ));

    }

    

    /**

     * Get ad units by placement

     */

    public function get_ad_units_by_placement($placement, $active_only = true) {

        global $wpdb;

        

        $table_name = $this->database->get_table('ad_units');

        $where_clause = 'WHERE placement = %s';

        

        if ($active_only) {

            $where_clause .= ' AND is_active = 1';

        }

        

        $sql = "SELECT * FROM $table_name $where_clause ORDER BY display_order ASC, id ASC";

        

        return $wpdb->get_results($wpdb->prepare($sql, $placement));

    }

    

    /**

     * Create new ad unit

     */

    public function create_ad_unit($data) {

        global $wpdb;



        // Validate data

        $validated_data = $this->validate_ad_unit_data($data);

        if (is_wp_error($validated_data)) {

            return $validated_data;

        }



        $table_name = $this->database->get_table('ad_units');



        // Check if table exists and create if needed

        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

        if (!$table_exists) {

            // Create the table using the database class

            require_once EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/class-database.php';

            $database = new Epic_Membership_Database();

            $database->create_tables();

        }



        // Get next display order

        $max_order = $wpdb->get_var("SELECT MAX(display_order) FROM $table_name");

        $validated_data['display_order'] = ($max_order !== null) ? $max_order + 1 : 1;



        $result = $wpdb->insert(

            $table_name,

            $validated_data,

            array('%s', '%s', '%s', '%s', '%s', '%d', '%d', '%s')

        );



        if ($result === false) {

            return new WP_Error('db_error', __('Failed to create ad unit.', 'epic-membership') . ($wpdb->last_error ? ' Error: ' . $wpdb->last_error : ''));

        }



        return $wpdb->insert_id;

    }

    

    /**

     * Update ad unit

     */

    public function update_ad_unit($id, $data) {

        global $wpdb;

        

        // Validate data

        $validated_data = $this->validate_ad_unit_data($data, $id);

        if (is_wp_error($validated_data)) {

            return $validated_data;

        }

        

        $table_name = $this->database->get_table('ad_units');

        

        $result = $wpdb->update(

            $table_name,

            $validated_data,

            array('id' => $id),

            array('%s', '%s', '%s', '%s', '%s', '%d', '%d', '%s'),

            array('%d')

        );

        

        if ($result === false) {

            return new WP_Error('db_error', __('Failed to update ad unit.', 'epic-membership'));

        }

        

        return true;

    }

    

    /**

     * Delete ad unit

     */

    public function delete_ad_unit($id) {

        global $wpdb;

        

        $table_name = $this->database->get_table('ad_units');

        

        $result = $wpdb->delete(

            $table_name,

            array('id' => $id),

            array('%d')

        );

        

        if ($result === false) {

            return new WP_Error('db_error', __('Failed to delete ad unit.', 'epic-membership'));

        }

        

        return true;

    }

    

    /**

     * Toggle ad unit status

     */

    public function toggle_ad_unit_status($id) {

        global $wpdb;

        

        $table_name = $this->database->get_table('ad_units');

        

        // Get current status

        $current_status = $wpdb->get_var($wpdb->prepare(

            "SELECT is_active FROM $table_name WHERE id = %d",

            $id

        ));

        

        if ($current_status === null) {

            return new WP_Error('not_found', __('Ad unit not found.', 'epic-membership'));

        }

        

        $new_status = $current_status ? 0 : 1;

        

        $result = $wpdb->update(

            $table_name,

            array('is_active' => $new_status),

            array('id' => $id),

            array('%d'),

            array('%d')

        );

        

        if ($result === false) {

            return new WP_Error('db_error', __('Failed to toggle ad unit status.', 'epic-membership'));

        }

        

        return $new_status;

    }

    

    /**

     * Update ad units order

     */

    public function update_ad_units_order($ordered_ids) {

        global $wpdb;

        

        $table_name = $this->database->get_table('ad_units');

        

        foreach ($ordered_ids as $order => $id) {

            $wpdb->update(

                $table_name,

                array('display_order' => $order + 1),

                array('id' => intval($id)),

                array('%d'),

                array('%d')

            );

        }

        

        return true;

    }

    

    /**

     * Validate ad unit data

     */

    private function validate_ad_unit_data($data, $id = null) {

        $validated = array();



        // Name validation

        if (empty($data['name'])) {

            return new WP_Error('validation_error', __('Ad unit name is required.', 'epic-membership'));

        }

        $validated['name'] = sanitize_text_field($data['name']);



        // Provider validation (currently only Adsterra)

        $validated['provider'] = 'adsterra';



        // Ad type validation

        if (empty($data['ad_type']) || !array_key_exists($data['ad_type'], $this->supported_ad_types)) {

            return new WP_Error('validation_error', __('Invalid ad type.', 'epic-membership'));

        }

        $validated['ad_type'] = sanitize_text_field($data['ad_type']);



        // Placement validation

        if (empty($data['placement']) || !array_key_exists($data['placement'], $this->supported_placements)) {

            return new WP_Error('validation_error', __('Invalid placement.', 'epic-membership'));

        }

        $validated['placement'] = sanitize_text_field($data['placement']);



        // Ad code validation

        if (empty($data['ad_code'])) {

            return new WP_Error('validation_error', __('Ad code is required.', 'epic-membership'));

        }



        // Sanitize ad code

        $validated['ad_code'] = $this->sanitize_ad_code($data['ad_code']);



        // Active status

        $validated['is_active'] = intval(isset($data['is_active']) ? $data['is_active'] : 0);



        // Placement conditions (JSON)

        $validated['placement_conditions'] = isset($data['placement_conditions'])

            ? wp_json_encode($data['placement_conditions'])

            : null;



        return $validated;

    }

    

    /**

     * Sanitize ad code

     */

    private function sanitize_ad_code($ad_code) {

        // Empty code is always safe

        if (empty($ad_code)) {

            return '';

        }



        // Check if user has unfiltered_html capability (administrators in single-site, super admins in multisite)

        if (current_user_can('unfiltered_html')) {

            // For users with unfiltered_html capability, allow most HTML but still do basic cleanup

            $ad_code = wp_check_invalid_utf8($ad_code);

            $ad_code = trim($ad_code);



            // Log for security audit purposes

            error_log("Epic Membership: User with unfiltered_html capability saved ad unit HTML code. User ID: " . get_current_user_id());



            return $ad_code;

        }



        // For users without unfiltered_html capability, use wp_kses with allowed HTML tags

        // Define allowed HTML tags and attributes for ad codes

        $allowed_html = array(

            'script' => array(

                'type' => array(),

                'src' => array(),

                'async' => array(),

                'defer' => array(),

                'crossorigin' => array(),

                'integrity' => array(),

                'charset' => array(),

                'language' => array(),

                'id' => array(),

                'class' => array(),

                'data-ad-client' => array(),

                'data-ad-slot' => array(),

                'data-ad-format' => array(),

                'data-full-width-responsive' => array(),

            ),

            'ins' => array(

                'class' => array(),

                'style' => array(),

                'data-ad-client' => array(),

                'data-ad-slot' => array(),

                'data-ad-format' => array(),

                'data-full-width-responsive' => array(),

            ),

            'div' => array(

                'id' => array(),

                'class' => array(),

                'style' => array(),

            ),

            'noscript' => array(),

        );



        // Use wp_kses to sanitize while preserving allowed HTML

        $ad_code = wp_kses($ad_code, $allowed_html);

        $ad_code = trim($ad_code);



        return $ad_code;

    }

    

    /**

     * Get supported ad types

     */

    public function get_supported_ad_types() {

        return $this->supported_ad_types;

    }

    

    /**

     * Get supported placements

     */

    public function get_supported_placements() {

        return $this->supported_placements;

    }

    

    /**

     * Migrate legacy single ad unit to new system

     */

    public function migrate_legacy_ad_unit() {

        $legacy_enabled = get_option('epic_membership_adsterra_enabled', false);

        $legacy_code = get_option('epic_membership_adsterra_head_code', '');

        

        // Check if migration is needed

        if (!$legacy_enabled || empty($legacy_code)) {

            return false;

        }

        

        // Check if already migrated

        $existing_units = $this->get_ad_units();

        if (!empty($existing_units)) {

            return false;

        }

        

        // Create legacy ad unit

        $legacy_data = array(

            'name' => __('Legacy Adsterra Ad', 'epic-membership'),

            'provider' => 'adsterra',

            'ad_type' => 'popunder', // Default type

            'placement' => 'header',

            'ad_code' => $legacy_code,

            'is_active' => 1

        );

        

        $result = $this->create_ad_unit($legacy_data);

        

        if (!is_wp_error($result)) {

            // Mark as migrated by disabling legacy option

            update_option('epic_membership_adsterra_migrated', true);

            return true;

        }

        

        return false;

    }

}

