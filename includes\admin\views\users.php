<?php

/**

 * Admin page for managing user memberships

 */



if (!defined('ABSPATH')) {

    exit;

}



// Handle success/error messages

if (isset($_GET['epic_membership_message'])) {

    $message_type = sanitize_text_field($_GET['epic_membership_message']);

    $error_details = isset($_GET['error_details']) ? sanitize_text_field($_GET['error_details']) : '';

    $updated_count = isset($_GET['updated_count']) ? intval($_GET['updated_count']) : 0;

    $error_count = isset($_GET['error_count']) ? intval($_GET['error_count']) : 0;

    $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;



    switch ($message_type) {

        case 'user_updated':

            $user = $user_id ? get_user_by('ID', $user_id) : null;

            $user_name = $user ? $user->display_name : __('User', 'epic-membership');

            echo '<div class="notice notice-success is-dismissible"><p>';

            printf(__('Membership for %s has been updated successfully.', 'epic-membership'), '<strong>' . esc_html($user_name) . '</strong>');

            echo '</p></div>';

            break;



        case 'bulk_updated':

            echo '<div class="notice notice-success is-dismissible"><p>';

            if ($error_count > 0) {

                printf(__('%d users updated successfully, %d errors occurred.', 'epic-membership'), $updated_count, $error_count);

            } else {

                printf(_n('%d user updated successfully.', '%d users updated successfully.', $updated_count, 'epic-membership'), $updated_count);

            }

            echo '</p></div>';

            break;



        case 'error':

            echo '<div class="notice notice-error is-dismissible"><p>';

            switch ($error_details) {

                case 'missing_user_id':

                    _e('Error: No user ID provided.', 'epic-membership');

                    break;

                case 'invalid_user':

                    _e('Error: Invalid user selected.', 'epic-membership');

                    break;

                case 'update_failed':

                    _e('Error: Failed to update user membership. Please try again.', 'epic-membership');

                    break;

                case 'no_users_selected':

                    _e('Error: No users selected for bulk action.', 'epic-membership');

                    break;

                case 'no_action_selected':

                    _e('Error: No bulk action selected.', 'epic-membership');

                    break;

                default:

                    _e('An error occurred while updating user membership.', 'epic-membership');

            }

            echo '</p></div>';

            break;

    }

}



// Initialize users manager

$users_manager = new Epic_Membership_Users();

$tiers_manager = new Epic_Membership_Tiers();



// Get filter parameters

$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;

$per_page = 20;

$search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

$tier_filter = isset($_GET['tier_id']) ? intval($_GET['tier_id']) : '';

$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';



// Get users with memberships

$args = array(

    'number' => $per_page,

    'offset' => ($current_page - 1) * $per_page,

    'search' => $search,

    'tier_id' => $tier_filter,

    'status' => $status_filter

);



$users = $users_manager->get_users_with_memberships($args);

$total_users = $users_manager->get_users_count($args);

$total_pages = ceil($total_users / $per_page);



// Get available tiers for filters

$tiers = $tiers_manager->get_all_tiers(true);



// Get statistics

$stats = $users_manager->get_membership_statistics();



?>



<div class="wrap">

    <h1 class="wp-heading-inline">

        <?php _e('User Memberships', 'epic-membership'); ?>

    </h1>

    

    <hr class="wp-header-end">



    <!-- Statistics Cards -->

    <div class="epic-membership-stats-cards">

        <div class="stats-card">

            <div class="stats-number"><?php echo number_format($stats['total_users']); ?></div>

            <div class="stats-label"><?php _e('Total Users', 'epic-membership'); ?></div>

        </div>

        <div class="stats-card">

            <div class="stats-number"><?php echo number_format($stats['active_memberships']); ?></div>

            <div class="stats-label"><?php _e('Active Memberships', 'epic-membership'); ?></div>

        </div>

        <div class="stats-card">

            <div class="stats-number"><?php echo number_format($stats['expired_memberships']); ?></div>

            <div class="stats-label"><?php _e('Expired Memberships', 'epic-membership'); ?></div>

        </div>

        <div class="stats-card warning">

            <div class="stats-number"><?php echo number_format($stats['expiring_soon']); ?></div>

            <div class="stats-label"><?php _e('Expiring Soon', 'epic-membership'); ?></div>

        </div>

    </div>



    <!-- Filters -->

    <div class="tablenav top">

        <div class="alignleft actions">

            <form method="get" action="">

                <input type="hidden" name="page" value="epic-membership-users">

                

                <!-- Search -->

                <input type="search" 

                       name="s" 

                       value="<?php echo esc_attr($search); ?>" 

                       placeholder="<?php esc_attr_e('Search users...', 'epic-membership'); ?>">

                

                <!-- Tier Filter -->

                <select name="tier_id">

                    <option value=""><?php _e('All Tiers', 'epic-membership'); ?></option>

                    <?php foreach ($tiers as $tier): ?>

                        <option value="<?php echo esc_attr($tier->id); ?>" <?php selected($tier_filter, $tier->id); ?>>

                            <?php echo esc_html($tier->name); ?>

                        </option>

                    <?php endforeach; ?>

                </select>

                

                <!-- Status Filter -->

                <select name="status">

                    <option value=""><?php _e('All Statuses', 'epic-membership'); ?></option>

                    <option value="active" <?php selected($status_filter, 'active'); ?>><?php _e('Active', 'epic-membership'); ?></option>

                    <option value="expired" <?php selected($status_filter, 'expired'); ?>><?php _e('Expired', 'epic-membership'); ?></option>

                </select>

                

                <input type="submit" class="button" value="<?php esc_attr_e('Filter', 'epic-membership'); ?>">

                

                <?php if ($search || $tier_filter || $status_filter): ?>

                    <a href="<?php echo esc_url(remove_query_arg(array('s', 'tier_id', 'status', 'paged'))); ?>" class="button">

                        <?php _e('Clear Filters', 'epic-membership'); ?>

                    </a>

                <?php endif; ?>

            </form>

        </div>

        

        <!-- Bulk Actions -->

        <div class="alignleft actions bulkactions">

            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" id="bulk-action-form">

                <?php wp_nonce_field('epic_membership_bulk_update'); ?>

                <input type="hidden" name="action" value="epic_membership_bulk_update_memberships">

                

                <select name="bulk_action" id="bulk-action-selector">

                    <option value=""><?php _e('Bulk Actions', 'epic-membership'); ?></option>

                    <option value="update_tier"><?php _e('Update Tier', 'epic-membership'); ?></option>

                    <option value="extend_membership"><?php _e('Extend Membership', 'epic-membership'); ?></option>

                </select>

                

                <!-- Bulk Update Tier Options -->

                <div id="bulk-tier-options" style="display: none;">

                    <select name="bulk_tier_id">

                        <option value=""><?php _e('Remove Membership', 'epic-membership'); ?></option>

                        <?php foreach ($tiers as $tier): ?>

                            <option value="<?php echo esc_attr($tier->id); ?>">

                                <?php echo esc_html($tier->name); ?>

                            </option>

                        <?php endforeach; ?>

                    </select>

                    <input type="number" 

                           name="bulk_duration_days" 

                           placeholder="<?php esc_attr_e('Duration (days)', 'epic-membership'); ?>" 

                           min="1" 

                           style="width: 120px;">

                </div>

                

                <!-- Bulk Extend Options -->

                <div id="bulk-extend-options" style="display: none;">

                    <input type="number" 

                           name="bulk_extend_days" 

                           placeholder="<?php esc_attr_e('Extend by (days)', 'epic-membership'); ?>" 

                           value="30" 

                           min="1" 

                           style="width: 120px;">

                </div>

                

                <input type="submit" class="button" value="<?php esc_attr_e('Apply', 'epic-membership'); ?>" disabled>

            </form>

        </div>

    </div>



    <!-- Users Table -->

    <?php if (empty($users)): ?>

        <div class="notice notice-info">

            <p><?php _e('No users found matching your criteria.', 'epic-membership'); ?></p>

        </div>

    <?php else: ?>

        <form method="post" id="users-filter">

            <table class="wp-list-table widefat fixed striped">

                <thead>

                    <tr>

                        <td class="manage-column column-cb check-column">

                            <input type="checkbox" id="cb-select-all-1">

                        </td>

                        <th scope="col" class="manage-column column-user column-primary">

                            <?php _e('User', 'epic-membership'); ?>

                        </th>

                        <th scope="col" class="manage-column column-membership">

                            <?php _e('Current Membership', 'epic-membership'); ?>

                        </th>

                        <th scope="col" class="manage-column column-start-date">

                            <?php _e('Start Date', 'epic-membership'); ?>

                        </th>

                        <th scope="col" class="manage-column column-end-date">

                            <?php _e('End Date', 'epic-membership'); ?>

                        </th>

                        <th scope="col" class="manage-column column-status">

                            <?php _e('Status', 'epic-membership'); ?>

                        </th>

                        <th scope="col" class="manage-column column-actions">

                            <?php _e('Actions', 'epic-membership'); ?>

                        </th>

                    </tr>

                </thead>

                <tbody>

                    <?php foreach ($users as $user): 

                        $is_active = $user->membership_active && 

                                   (!$user->end_date || strtotime($user->end_date) > time());

                        $is_expiring = $user->end_date && 

                                     strtotime($user->end_date) > time() && 

                                     strtotime($user->end_date) < strtotime('+7 days');

                    ?>

                        <tr class="<?php echo $is_active ? 'active' : 'inactive'; ?>">

                            <th scope="row" class="check-column">

                                <input type="checkbox" name="user_ids[]" value="<?php echo esc_attr($user->ID); ?>">

                            </th>

                            <td class="column-user column-primary">

                                <strong>

                                    <a href="<?php echo esc_url(get_edit_user_link($user->ID)); ?>">

                                        <?php echo esc_html($user->display_name); ?>

                                    </a>

                                </strong>

                                <div class="user-details">

                                    <div><?php echo esc_html($user->user_login); ?></div>

                                    <div><a href="mailto:<?php echo esc_attr($user->user_email); ?>"><?php echo esc_html($user->user_email); ?></a></div>

                                </div>

                                <div class="row-actions">

                                    <span class="edit">

                                        <a href="#" class="edit-membership" data-user-id="<?php echo esc_attr($user->ID); ?>">

                                            <?php _e('Edit Membership', 'epic-membership'); ?>

                                        </a>

                                    </span>

                                    <?php if ($user->end_date && $is_active): ?>

                                        | <span class="extend">

                                            <a href="#" class="extend-membership" data-user-id="<?php echo esc_attr($user->ID); ?>">

                                                <?php _e('Extend', 'epic-membership'); ?>

                                            </a>

                                        </span>

                                    <?php endif; ?>

                                </div>

                                <button type="button" class="toggle-row">

                                    <span class="screen-reader-text"><?php _e('Show more details', 'epic-membership'); ?></span>

                                </button>

                            </td>

                            <td class="column-membership" data-colname="<?php esc_attr_e('Current Membership', 'epic-membership'); ?>">

                                <?php if ($user->tier_name): ?>

                                    <span class="membership-tier tier-level-<?php echo esc_attr($user->tier_level); ?>">

                                        <?php echo esc_html($user->tier_name); ?>

                                    </span>

                                <?php else: ?>

                                    <span class="no-membership"><?php _e('No Membership', 'epic-membership'); ?></span>

                                <?php endif; ?>

                            </td>

                            <td class="column-start-date" data-colname="<?php esc_attr_e('Start Date', 'epic-membership'); ?>">

                                <?php if ($user->start_date): ?>

                                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($user->start_date))); ?>

                                <?php else: ?>

                                    <span class="na">—</span>

                                <?php endif; ?>

                            </td>

                            <td class="column-end-date" data-colname="<?php esc_attr_e('End Date', 'epic-membership'); ?>">

                                <?php if ($user->end_date): ?>

                                    <span class="<?php echo $is_expiring ? 'expiring-soon' : ''; ?>">

                                        <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($user->end_date))); ?>

                                        <?php if ($is_expiring): ?>

                                            <span class="expiring-indicator">⚠</span>

                                        <?php endif; ?>

                                    </span>

                                <?php else: ?>

                                    <span class="lifetime"><?php _e('Lifetime', 'epic-membership'); ?></span>

                                <?php endif; ?>

                            </td>

                            <td class="column-status" data-colname="<?php esc_attr_e('Status', 'epic-membership'); ?>">

                                <?php if ($is_active): ?>

                                    <span class="status-badge active"><?php _e('Active', 'epic-membership'); ?></span>

                                <?php else: ?>

                                    <span class="status-badge expired"><?php _e('Expired', 'epic-membership'); ?></span>

                                <?php endif; ?>

                            </td>

                            <td class="column-actions" data-colname="<?php esc_attr_e('Actions', 'epic-membership'); ?>">

                                <button type="button" class="button button-small edit-membership" data-user-id="<?php echo esc_attr($user->ID); ?>">

                                    <?php _e('Edit', 'epic-membership'); ?>

                                </button>

                            </td>

                        </tr>

                    <?php endforeach; ?>

                </tbody>

            </table>

        </form>

    <?php endif; ?>



    <!-- Pagination -->

    <?php if ($total_pages > 1): ?>

        <div class="tablenav bottom">

            <div class="tablenav-pages">

                <?php

                $pagination_args = array(

                    'base' => add_query_arg('paged', '%#%'),

                    'format' => '',

                    'prev_text' => __('&laquo;'),

                    'next_text' => __('&raquo;'),

                    'total' => $total_pages,

                    'current' => $current_page

                );

                echo paginate_links($pagination_args);

                ?>

            </div>

        </div>

    <?php endif; ?>

</div>



<!-- Edit Membership Modal -->

<div id="edit-membership-modal" class="epic-membership-modal" style="display: none;">

    <div class="modal-content">

        <div class="modal-header">

            <h3><?php _e('Edit User Membership', 'epic-membership'); ?></h3>

            <button type="button" class="modal-close">&times;</button>

        </div>

        <div class="modal-body">

            <form id="edit-membership-form" method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">

                <?php wp_nonce_field('epic_membership_update_user_membership'); ?>

                <input type="hidden" name="action" value="epic_membership_update_user_membership">

                <input type="hidden" name="user_id" id="edit-user-id">

                

                <table class="form-table">

                    <tr>

                        <th><label for="edit-tier-id"><?php _e('Membership Tier', 'epic-membership'); ?></label></th>

                        <td>

                            <select name="tier_id" id="edit-tier-id" class="widefat">

                                <option value=""><?php _e('No Membership', 'epic-membership'); ?></option>

                                <?php foreach ($tiers as $tier): ?>

                                    <option value="<?php echo esc_attr($tier->id); ?>">

                                        <?php echo esc_html($tier->name); ?> (Level <?php echo esc_html($tier->level); ?>)

                                    </option>

                                <?php endforeach; ?>

                            </select>

                        </td>

                    </tr>

                    <tr>

                        <th><label for="edit-duration-days"><?php _e('Duration (Days)', 'epic-membership'); ?></label></th>

                        <td>

                            <input type="number" name="duration_days" id="edit-duration-days" min="1" class="small-text">

                            <p class="description"><?php _e('Leave empty to use tier default duration.', 'epic-membership'); ?></p>

                        </td>

                    </tr>

                    <tr>

                        <th><label for="edit-notes"><?php _e('Notes', 'epic-membership'); ?></label></th>

                        <td>

                            <textarea name="notes" id="edit-notes" rows="3" class="widefat"></textarea>

                        </td>

                    </tr>

                </table>

                

                <div class="modal-actions">

                    <input type="submit" class="button button-primary" value="<?php esc_attr_e('Update Membership', 'epic-membership'); ?>">

                    <button type="button" class="button modal-close"><?php _e('Cancel', 'epic-membership'); ?></button>

                </div>

            </form>

        </div>

    </div>

</div>



<style>

.epic-membership-stats-cards {

    display: flex;

    gap: 20px;

    margin: 20px 0;

}



.stats-card {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    text-align: center;

    flex: 1;

    box-shadow: 0 1px 1px rgba(0,0,0,0.04);

}



.stats-card.warning {

    border-color: #dba617;

    background: #fcf9e8;

}



.stats-number {

    font-size: 32px;

    font-weight: bold;

    color: #1d2327;

    line-height: 1;

}



.stats-label {

    font-size: 13px;

    color: #646970;

    margin-top: 5px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.membership-tier {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

}



.membership-tier.tier-level-0 {

    background: #e0e0e0;

    color: #666;

}



.membership-tier.tier-level-10 {

    background: #d4edda;

    color: #155724;

}



.membership-tier.tier-level-20 {

    background: #d1ecf1;

    color: #0c5460;

}



.no-membership {

    color: #646970;

    font-style: italic;

}



.status-badge {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

}



.status-badge.active {

    background: #d4edda;

    color: #155724;

}



.status-badge.expired {

    background: #f8d7da;

    color: #721c24;

}



.expiring-soon {

    color: #856404;

    font-weight: bold;

}



.expiring-indicator {

    color: #856404;

    font-size: 14px;

}



.lifetime {

    color: #0073aa;

    font-weight: bold;

}



.user-details {

    font-size: 12px;

    color: #646970;

    margin-top: 5px;

}



.user-details div {

    margin: 2px 0;

}



tr.inactive {

    opacity: 0.6;

}



.na {

    color: #646970;

}



/* Modal Styles */

.epic-membership-modal {

    position: fixed;

    top: 0;

    left: 0;

    width: 100%;

    height: 100%;

    background: transparent; /* No background overlay */

    z-index: 100000;

    display: flex;

    align-items: center;

    justify-content: center;

}



.modal-content {

    background: white;

    border-radius: 4px;

    width: 90%;

    max-width: 600px;

    max-height: 90%;

    overflow-y: auto;

}



.modal-header {

    padding: 20px;

    border-bottom: 1px solid #ddd;

    display: flex;

    justify-content: space-between;

    align-items: center;

}



.modal-header h3 {

    margin: 0;

}



.modal-close {

    background: none;

    border: none;

    font-size: 24px;

    cursor: pointer;

    color: #666;

}



.modal-body {

    padding: 20px;

}



.modal-actions {

    margin-top: 20px;

    text-align: right;

}



.modal-actions .button {

    margin-left: 10px;

}



/* Bulk Actions */

#bulk-tier-options,

#bulk-extend-options {

    display: inline-block;

    margin-left: 10px;

}



#bulk-tier-options select,

#bulk-tier-options input,

#bulk-extend-options input {

    margin-right: 5px;

}

</style>



<script>

jQuery(document).ready(function($) {

    // Handle bulk action selector

    $('#bulk-action-selector').on('change', function() {

        var action = $(this).val();

        $('#bulk-tier-options, #bulk-extend-options').hide();



        if (action === 'update_tier') {

            $('#bulk-tier-options').show();

        } else if (action === 'extend_membership') {

            $('#bulk-extend-options').show();

        }



        // Enable/disable apply button

        $('#bulk-action-form input[type="submit"]').prop('disabled', !action);

    });



    // Handle checkbox selection

    $('#cb-select-all-1').on('change', function() {

        $('input[name="user_ids[]"]').prop('checked', this.checked);

        updateBulkActionButton();

    });



    $('input[name="user_ids[]"]').on('change', function() {

        updateBulkActionButton();

    });



    function updateBulkActionButton() {

        var checkedCount = $('input[name="user_ids[]"]:checked').length;

        var hasAction = $('#bulk-action-selector').val();

        $('#bulk-action-form input[type="submit"]').prop('disabled', !checkedCount || !hasAction);

    }



    // Handle edit membership modal

    $('.edit-membership').on('click', function(e) {

        e.preventDefault();

        var userId = $(this).data('user-id');

        openEditMembershipModal(userId);

    });



    function openEditMembershipModal(userId) {

        $('#edit-user-id').val(userId);



        // Get current membership data

        $.ajax({

            url: ajaxurl,

            type: 'POST',

            data: {

                action: 'epic_membership_get_user_membership',

                user_id: userId,

                nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'

            },

            success: function(response) {

                if (response.success && response.data) {

                    $('#edit-tier-id').val(response.data.tier_id || '');

                    $('#edit-duration-days').val('');

                    $('#edit-notes').val('');

                }

                $('#edit-membership-modal').show();

            },

            error: function() {

                $('#edit-membership-modal').show();

            }

        });

    }



    // Handle extend membership

    $('.extend-membership').on('click', function(e) {

        e.preventDefault();

        var userId = $(this).data('user-id');

        var days = prompt('<?php echo esc_js(__('Extend membership by how many days?', 'epic-membership')); ?>', '30');



        if (days && parseInt(days) > 0) {

            extendMembership(userId, parseInt(days));

        }

    });



    function extendMembership(userId, days) {

        $.ajax({

            url: ajaxurl,

            type: 'POST',

            data: {

                action: 'epic_membership_extend_membership',

                user_id: userId,

                days: days,

                nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'

            },

            success: function(response) {

                if (response.success) {

                    location.reload();

                } else {

                    alert('Error: ' + response.data);

                }

            },

            error: function() {

                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'epic-membership')); ?>');

            }

        });

    }



    // Handle modal close

    $('.modal-close').on('click', function() {

        $('#edit-membership-modal').hide();

    });



    // Close modal on outside click

    $('#edit-membership-modal').on('click', function(e) {

        if (e.target === this) {

            $(this).hide();

        }

    });



    // Handle bulk form submission

    $('#bulk-action-form').on('submit', function(e) {

        var checkedUsers = $('input[name="user_ids[]"]:checked').length;

        var action = $('#bulk-action-selector').val();



        if (!checkedUsers) {

            e.preventDefault();

            alert('<?php echo esc_js(__('Please select at least one user.', 'epic-membership')); ?>');

            return false;

        }



        if (!action) {

            e.preventDefault();

            alert('<?php echo esc_js(__('Please select a bulk action.', 'epic-membership')); ?>');

            return false;

        }



        // Add selected user IDs to the form

        $('input[name="user_ids[]"]:checked').each(function() {

            $('#bulk-action-form').append('<input type="hidden" name="user_ids[]" value="' + $(this).val() + '">');

        });



        var confirmMessage = '<?php echo esc_js(__('Are you sure you want to apply this action to the selected users?', 'epic-membership')); ?>';

        return confirm(confirmMessage);

    });

});

</script>

