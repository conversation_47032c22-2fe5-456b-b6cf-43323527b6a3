<?php

/**

 * Float Over Settings Admin Page

 *

 * @package Epic_Membership

 * @since 1.0.0

 */



// Prevent direct access

if (!defined('ABSPATH')) {

    exit;

}



// Handle form submission

if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_float_over_settings')) {

    // Update settings

    update_option('epic_membership_float_over_enabled', isset($_POST['epic_membership_float_over_enabled']));

    update_option('epic_membership_float_over_links', sanitize_textarea_field($_POST['epic_membership_float_over_links']));

    update_option('epic_membership_float_over_unlimited_links', sanitize_textarea_field($_POST['epic_membership_float_over_unlimited_links']));

    update_option('epic_membership_float_over_default_limit', max(1, intval($_POST['epic_membership_float_over_default_limit'])));

    update_option('epic_membership_float_over_per_link_limits', sanitize_textarea_field($_POST['epic_membership_float_over_per_link_limits']));

    update_option('epic_membership_float_over_delay', intval($_POST['epic_membership_float_over_delay']));

    update_option('epic_membership_float_over_custom_text', sanitize_text_field($_POST['epic_membership_float_over_custom_text']));

    update_option('epic_membership_float_over_premium_only', isset($_POST['epic_membership_float_over_premium_only']));



    echo '<div class="notice notice-success is-dismissible"><p>' . __('Float Over settings saved successfully!', 'epic-membership') . '</p></div>';

}



// Get current settings

$enabled = get_option('epic_membership_float_over_enabled', false);

$links = get_option('epic_membership_float_over_links', '');

$unlimited_links = get_option('epic_membership_float_over_unlimited_links', '');

$default_limit = get_option('epic_membership_float_over_default_limit', 5);

$per_link_limits = get_option('epic_membership_float_over_per_link_limits', '');

$delay = get_option('epic_membership_float_over_delay', 5);

$custom_text = get_option('epic_membership_float_over_custom_text', 'Click the button below to continue');

$premium_only = get_option('epic_membership_float_over_premium_only', false);



// Get statistics

global $wpdb;

$table_name = $wpdb->prefix . 'epic_membership_float_over_links';

$total_clicks = $wpdb->get_var("SELECT SUM(count) FROM $table_name");

$unique_ips = $wpdb->get_var("SELECT COUNT(DISTINCT ip_address) FROM $table_name");

?>



<div class="wrap">

    <h1><?php _e('Float Over Settings', 'epic-membership'); ?></h1>

    

    <div class="epic-membership-admin-header">

        <p><?php _e('Configure the float-over overlay that appears on single posts to display promotional links to visitors.', 'epic-membership'); ?></p>

    </div>



    <?php if ($total_clicks > 0): ?>

    <div class="epic-membership-stats-cards">

        <div class="stats-card">

            <h3><?php _e('Total Clicks', 'epic-membership'); ?></h3>

            <div class="stats-number"><?php echo number_format($total_clicks); ?></div>

        </div>

        <div class="stats-card">

            <h3><?php _e('Unique Visitors', 'epic-membership'); ?></h3>

            <div class="stats-number"><?php echo number_format($unique_ips); ?></div>

        </div>

    </div>

    <?php endif; ?>



    <form method="post" action="">

        <?php wp_nonce_field('epic_membership_float_over_settings'); ?>

        

        <table class="form-table">

            <tbody>

                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_enabled"><?php _e('Enable Float Over', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <label>

                            <input type="checkbox" id="epic_membership_float_over_enabled" name="epic_membership_float_over_enabled" value="1" <?php checked($enabled); ?> />

                            <?php _e('Enable float-over overlay on single posts', 'epic-membership'); ?>

                        </label>

                        <p class="description"><?php _e('When enabled, a modal overlay will appear on single posts after the specified delay.', 'epic-membership'); ?></p>

                    </td>

                </tr>

                

                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_default_limit"><?php _e('Default View Limit', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <input type="number" id="epic_membership_float_over_default_limit" name="epic_membership_float_over_default_limit" value="<?php echo esc_attr($default_limit); ?>" min="1" max="999" class="small-text" />

                        <?php _e('times per IP address', 'epic-membership'); ?>

                        <p class="description"><?php _e('Default number of times each link can be shown to the same IP address before being excluded from rotation.', 'epic-membership'); ?></p>

                    </td>

                </tr>



                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_links"><?php _e('Limited Links', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <textarea id="epic_membership_float_over_links" name="epic_membership_float_over_links" rows="4" cols="50" class="large-text"><?php echo esc_textarea($links); ?></textarea>

                        <p class="description">

                            <?php _e('Enter links separated by commas. These links will use the default view limit or custom limits specified below.', 'epic-membership'); ?><br>

                            <?php _e('Example: https://example1.com, https://example2.com, https://example3.com', 'epic-membership'); ?>

                        </p>

                    </td>

                </tr>



                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_per_link_limits"><?php _e('Per-Link Custom Limits', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <textarea id="epic_membership_float_over_per_link_limits" name="epic_membership_float_over_per_link_limits" rows="4" cols="50" class="large-text" placeholder="https://example1.com=10&#10;https://example2.com=3&#10;https://example3.com=15"><?php echo esc_textarea($per_link_limits); ?></textarea>

                        <p class="description">

                            <?php _e('Set custom view limits for specific links. Format: one link per line with "URL=LIMIT".', 'epic-membership'); ?><br>

                            <?php _e('Example:', 'epic-membership'); ?><br>

                            <code>https://special-offer.com=10<br>https://premium-content.com=3</code><br>

                            <?php _e('Links not listed here will use the default limit above.', 'epic-membership'); ?>

                        </p>

                    </td>

                </tr>



                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_unlimited_links"><?php _e('Unlimited Links', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <textarea id="epic_membership_float_over_unlimited_links" name="epic_membership_float_over_unlimited_links" rows="3" cols="50" class="large-text"><?php echo esc_textarea($unlimited_links); ?></textarea>

                        <p class="description">

                            <?php _e('Enter links separated by commas. These links can be shown unlimited times without any view count restrictions.', 'epic-membership'); ?><br>

                            <?php _e('Example: https://always-show.com, https://unlimited-promo.com', 'epic-membership'); ?>

                        </p>

                    </td>

                </tr>

                

                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_delay"><?php _e('Delay Time', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <input type="number" id="epic_membership_float_over_delay" name="epic_membership_float_over_delay" value="<?php echo esc_attr($delay); ?>" min="1" max="60" class="small-text" />

                        <?php _e('seconds', 'epic-membership'); ?>

                        <p class="description"><?php _e('How long to wait before showing the continue button (1-60 seconds).', 'epic-membership'); ?></p>

                    </td>

                </tr>

                

                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_custom_text"><?php _e('Custom Text', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <input type="text" id="epic_membership_float_over_custom_text" name="epic_membership_float_over_custom_text" value="<?php echo esc_attr($custom_text); ?>" class="regular-text" />

                        <p class="description"><?php _e('Text displayed in the float-over overlay.', 'epic-membership'); ?></p>

                    </td>

                </tr>

                

                <tr>

                    <th scope="row">

                        <label for="epic_membership_float_over_premium_only"><?php _e('Show to Free Users Only', 'epic-membership'); ?></label>

                    </th>

                    <td>

                        <label>

                            <input type="checkbox" id="epic_membership_float_over_premium_only" name="epic_membership_float_over_premium_only" value="1" <?php checked($premium_only); ?> />

                            <?php _e('Only show float-over to free tier users (premium users won\'t see it)', 'epic-membership'); ?>

                        </label>

                        <p class="description"><?php _e('When enabled, the float-over will only be displayed to users with free membership or non-logged-in visitors.', 'epic-membership'); ?></p>

                    </td>

                </tr>

            </tbody>

        </table>

        

        <?php submit_button(__('Save Float Over Settings', 'epic-membership')); ?>

    </form>



    <div class="epic-membership-help-section">

        <h2><?php _e('How Float Over Works', 'epic-membership'); ?></h2>

        <ul>

            <li><?php _e('The float-over appears as a modal overlay on single posts/articles', 'epic-membership'); ?></li>

            <li><?php _e('Visitors must wait for the specified delay before the continue button appears', 'epic-membership'); ?></li>

            <li><?php _e('Clicking continue opens a random link from your configured lists in a new tab', 'epic-membership'); ?></li>

            <li><?php _e('Limited links are shown up to their configured limit per IP address', 'epic-membership'); ?></li>

            <li><?php _e('Unlimited links can be shown repeatedly without any restrictions', 'epic-membership'); ?></li>

            <li><?php _e('When all limited links reach their limits, the system resets and starts over', 'epic-membership'); ?></li>

            <li><?php _e('You can optionally restrict this feature to free users only', 'epic-membership'); ?></li>

        </ul>



        <h3><?php _e('Link Types Explained', 'epic-membership'); ?></h3>

        <ul>

            <li><strong><?php _e('Limited Links:', 'epic-membership'); ?></strong> <?php _e('Use the default view limit or custom per-link limits. Good for promotional offers that should have controlled exposure.', 'epic-membership'); ?></li>

            <li><strong><?php _e('Unlimited Links:', 'epic-membership'); ?></strong> <?php _e('Can be shown repeatedly without restrictions. Perfect for evergreen content or primary promotional pages.', 'epic-membership'); ?></li>

            <li><strong><?php _e('Per-Link Custom Limits:', 'epic-membership'); ?></strong> <?php _e('Override the default limit for specific links. Useful for high-value offers that need more exposure or sensitive content that should be shown less frequently.', 'epic-membership'); ?></li>

        </ul>

        

        <h3><?php _e('Best Practices', 'epic-membership'); ?></h3>

        <ul>

            <li><?php _e('Use a reasonable delay (3-10 seconds) to avoid annoying visitors', 'epic-membership'); ?></li>

            <li><?php _e('Mix limited and unlimited links based on your promotional strategy', 'epic-membership'); ?></li>

            <li><?php _e('Set higher limits for high-value offers and lower limits for general promotions', 'epic-membership'); ?></li>

            <li><?php _e('Use unlimited links for your most important evergreen content', 'epic-membership'); ?></li>

            <li><?php _e('Monitor click statistics to optimize your link selection and limits', 'epic-membership'); ?></li>

            <li><?php _e('Consider enabling "Free Users Only" to provide ad-free experience for premium members', 'epic-membership'); ?></li>

            <li><?php _e('Test different limit combinations to find what works best for your audience', 'epic-membership'); ?></li>

        </ul>

    </div>

</div>



<style>

.epic-membership-admin-header {

    background: #f9f9f9;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 15px;

    margin: 20px 0;

}



.epic-membership-stats-cards {

    display: flex;

    gap: 20px;

    margin: 20px 0;

}



.stats-card {

    background: #fff;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 20px;

    text-align: center;

    min-width: 150px;

}



.stats-card h3 {

    margin: 0 0 10px 0;

    font-size: 14px;

    color: #666;

    text-transform: uppercase;

}



.stats-number {

    font-size: 32px;

    font-weight: bold;

    color: #0073aa;

}



.epic-membership-help-section {

    background: #fff;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 20px;

    margin-top: 30px;

}



.epic-membership-help-section h2,

.epic-membership-help-section h3 {

    color: #333;

}



.epic-membership-help-section ul {

    padding-left: 20px;

}



.epic-membership-help-section li {

    margin-bottom: 8px;

}

</style>

