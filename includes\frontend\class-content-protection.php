<?php

/**

 * Content Protection Class for Epic Membership Plugin

 * Handles content filtering and access control

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Content_Protection {

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Timezone handler instance

     */

    private $timezone_handler;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->timezone_handler = new Epic_Membership_Timezone_Handler();

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Content filtering

        add_filter('the_content', array($this, 'filter_content'), 10);

        add_filter('the_excerpt', array($this, 'filter_excerpt'), 10);

        

        // Query modifications for protected content

        add_action('pre_get_posts', array($this, 'modify_query_for_protected_content'));

        

        // REST API protection

        add_filter('rest_prepare_post', array($this, 'filter_rest_content'), 10, 3);

        

        // Search results filtering

        add_filter('posts_where', array($this, 'exclude_protected_from_search'), 10, 2);

        

        // Add body classes for styling

        add_filter('body_class', array($this, 'add_membership_body_classes'));

        

        // Shortcode for membership-specific content

        add_shortcode('epic_membership', array($this, 'membership_shortcode'));

        

        // AJAX handlers

        add_action('wp_ajax_epic_membership_check_access', array($this, 'ajax_check_content_access'));

        add_action('wp_ajax_nopriv_epic_membership_check_access', array($this, 'ajax_check_content_access'));

    }

    

    /**

     * Filter main content based on access rules

     */

    public function filter_content($content) {

        global $post;



        if (!$post) {

            return $content;

        }



        // For RSS feeds, always check access regardless of context

        if (is_feed()) {

            $access_result = $this->check_content_access($post->ID, 0); // Check as non-logged-in user for RSS



            if (!$access_result['has_access']) {

                // For RSS feeds, return teaser content or empty string

                $access_settings = $this->database->get_content_access($post->ID);

                if ($access_settings && $access_settings->teaser_content) {

                    return wp_trim_words($access_settings->teaser_content, 55);

                }

                return ''; // Return empty content for RSS if no teaser

            }



            return $content;

        }



        // For regular pages, only filter on singular views

        if (!is_singular()) {

            return $content;

        }



        $access_result = $this->check_content_access($post->ID);



        if ($access_result['has_access']) {

            return $content;

        }



        // Log access attempt

        $this->log_access_attempt($post->ID, $access_result);



        // Return protected content message

        return $this->get_protected_content_message($post->ID, $access_result);

    }

    

    /**

     * Filter excerpt for protected content

     */

    public function filter_excerpt($excerpt) {

        global $post;



        if (!$post) {

            return $excerpt;

        }



        // For RSS feeds, check access as non-logged-in user

        $user_id = is_feed() ? 0 : null;

        $access_result = $this->check_content_access($post->ID, $user_id);



        if (!$access_result['has_access']) {

            // Return teaser or empty excerpt

            $access_settings = $this->database->get_content_access($post->ID);

            if ($access_settings && $access_settings->teaser_content) {

                return wp_trim_words($access_settings->teaser_content, 55);

            }



            // For RSS feeds, return empty string if no teaser

            if (is_feed()) {

                return '';

            }

        }



        return $excerpt;

    }

    

    /**

     * Check if user has access to specific content

     */

    public function check_content_access($post_id, $user_id = null) {

        if (!$user_id) {

            $user_id = get_current_user_id();

        }

        

        // Get content access settings

        $access_settings = $this->database->get_content_access($post_id);

        

        // If no access settings, content is public

        if (!$access_settings) {

            return array(

                'has_access' => true,

                'access_type' => 'public',

                'reason' => 'public_content'

            );

        }

        

        // Check access type

        switch ($access_settings->access_type) {

            case 'public':

                return array(

                    'has_access' => true,

                    'access_type' => 'public',

                    'reason' => 'public_content'

                );

                

            case 'members_only':

                return $this->check_membership_access($access_settings, $user_id);

                

            case 'scheduled':

                return $this->check_scheduled_access($access_settings, $user_id);

                

            default:

                return array(

                    'has_access' => false,

                    'access_type' => 'unknown',

                    'reason' => 'unknown_access_type'

                );

        }

    }

    

    /**

     * Check membership-based access

     */

    private function check_membership_access($access_settings, $user_id) {

        if (!$user_id) {

            return array(

                'has_access' => false,

                'access_type' => 'members_only',

                'reason' => 'not_logged_in',

                'required_tier' => $access_settings->tier_name,

                'required_level' => $access_settings->tier_level

            );

        }

        

        // Get user's membership

        $user_membership = $this->database->get_user_membership($user_id);

        

        if (!$user_membership) {

            return array(

                'has_access' => false,

                'access_type' => 'members_only',

                'reason' => 'no_membership',

                'required_tier' => $access_settings->tier_name,

                'required_level' => $access_settings->tier_level

            );

        }

        

        // Check if membership is active

        if (!$user_membership->is_active || 

            ($user_membership->end_date && strtotime($user_membership->end_date) <= time())) {

            return array(

                'has_access' => false,

                'access_type' => 'members_only',

                'reason' => 'membership_expired',

                'user_tier' => $user_membership->tier_name,

                'required_tier' => $access_settings->tier_name,

                'required_level' => $access_settings->tier_level

            );

        }

        

        // Check tier level (hierarchical access)

        // Higher level members can access lower level content

        if ($user_membership->tier_level < $access_settings->tier_level) {

            return array(

                'has_access' => false,

                'access_type' => 'members_only',

                'reason' => 'insufficient_tier',

                'user_tier' => $user_membership->tier_name,

                'user_level' => $user_membership->tier_level,

                'required_tier' => $access_settings->tier_name,

                'required_level' => $access_settings->tier_level

            );

        }

        

        return array(

            'has_access' => true,

            'access_type' => 'members_only',

            'reason' => 'valid_membership',

            'user_tier' => $user_membership->tier_name

        );

    }

    

    /**

     * Check scheduled access

     */

    private function check_scheduled_access($access_settings, $user_id) {

        // If tier restrictions are set, enforce them strictly

        if (!empty($access_settings->allowed_tier_ids)) {

            // Parse allowed tier IDs

            $allowed_tier_ids = json_decode($access_settings->allowed_tier_ids, true);

            if (!is_array($allowed_tier_ids) || empty($allowed_tier_ids)) {

                // Invalid tier data, fall back to time-based release

                return $this->check_time_based_release($access_settings);

            }



            // Check if user is logged in

            if (!$user_id) {

                // Guest users never have access when tier restrictions are set

                return $this->check_tier_restricted_access_denied($access_settings, 'not_logged_in');

            }



            // Get user's membership

            $user_membership = $this->database->get_user_membership($user_id);



            // Handle users without membership records

            if (!$user_membership) {

                // Check if Free tier (level 0) is in allowed tiers

                $free_tier_allowed = $this->is_free_tier_allowed($allowed_tier_ids);



                if ($free_tier_allowed) {

                    // User needs to upgrade to Free tier first - don't auto-assign

                    // This maintains consistency with members_only content behavior

                    return $this->check_tier_restricted_access_denied($access_settings, 'no_membership');

                } else {

                    // Free tier not allowed, deny access

                    return $this->check_tier_restricted_access_denied($access_settings, 'insufficient_tier');

                }

            }



            // User has membership record - check if active and tier is allowed

            if ($user_membership->is_active &&

                (!$user_membership->end_date || strtotime($user_membership->end_date) > time())) {



                $is_tier_allowed = $this->database->is_user_tier_allowed_for_scheduled(

                    $user_membership->tier_id,

                    $access_settings->allowed_tier_ids

                );



                if ($is_tier_allowed) {

                    // User has the required tier, but still need to check if content is released

                    $is_released = $this->timezone_handler->is_content_released(

                        $access_settings->scheduled_release,

                        $access_settings->release_timezone

                    );



                    if ($is_released) {

                        return array(

                            'has_access' => true,

                            'access_type' => 'scheduled',

                            'reason' => 'tier_access',

                            'user_tier' => $user_membership->tier_name,

                            'user_level' => $user_membership->tier_level,

                            'release_time' => $access_settings->scheduled_release

                        );

                    } else {

                        // Content not yet released - check for early access capability

                        $has_early_access = $this->database->user_has_early_access($user_id);



                        if ($has_early_access) {

                            // User has early access capability, grant access

                            return array(

                                'has_access' => true,

                                'access_type' => 'scheduled',

                                'reason' => 'early_access',

                                'user_tier' => $user_membership->tier_name,

                                'user_level' => $user_membership->tier_level,

                                'release_time' => $access_settings->scheduled_release

                            );

                        } else {

                            // Content not yet released and no early access

                            $time_remaining = $this->timezone_handler->get_time_until_release(

                                $access_settings->scheduled_release,

                                $access_settings->release_timezone

                            );



                            return array(

                                'has_access' => false,

                                'access_type' => 'scheduled',

                                'reason' => 'not_yet_released',

                                'user_tier' => $user_membership->tier_name,

                                'user_level' => $user_membership->tier_level,

                                'release_time' => $access_settings->scheduled_release,

                                'time_remaining' => $time_remaining

                            );

                        }

                    }

                } else {

                    // User's tier is not allowed

                    return $this->check_tier_restricted_access_denied($access_settings, 'insufficient_tier', $user_membership);

                }

            } else {

                // User's membership is inactive or expired

                return $this->check_tier_restricted_access_denied($access_settings, 'membership_expired', $user_membership);

            }

        }



        // No tier restrictions - use legacy behavior for backward compatibility

        if ($user_id) {

            $user_membership = $this->database->get_user_membership($user_id);



            // Check for early access capability first, then fall back to legacy premium bypass

            if ($user_membership &&

                $user_membership->is_active &&

                (!$user_membership->end_date || strtotime($user_membership->end_date) > time())) {



                $has_early_access = $this->database->user_has_early_access($user_id);



                if ($has_early_access) {

                    return array(

                        'has_access' => true,

                        'access_type' => 'scheduled',

                        'reason' => 'early_access',

                        'user_tier' => $user_membership->tier_name,

                        'user_level' => $user_membership->tier_level,

                        'release_time' => $access_settings->scheduled_release

                    );

                } elseif ($user_membership->tier_level > 0) {

                    // Legacy behavior: premium members (level > 0) can bypass schedule

                    return array(

                        'has_access' => true,

                        'access_type' => 'scheduled',

                        'reason' => 'premium_bypass',

                        'user_tier' => $user_membership->tier_name,

                        'user_level' => $user_membership->tier_level,

                        'release_time' => $access_settings->scheduled_release

                    );

                }

            }

        }



        // Check time-based release for everyone else

        return $this->check_time_based_release($access_settings);

    }

    

    /**

     * Get protected content message

     */

    private function get_protected_content_message($post_id, $access_result) {

        $access_settings = $this->database->get_content_access($post_id);

        

        // Start building the message

        $message = '<div class="epic-membership-protected-content">';

        $message .= '<div class="epic-membership-lock-icon">🔒</div>';

        

        switch ($access_result['reason']) {

            case 'not_logged_in':

                $message .= '<div class="epic-membership-protection-message">';

                $message .= __('Please log in to access this content.', 'epic-membership');

                $message .= '</div>';

                $message .= '<a href="' . wp_login_url(get_permalink()) . '" class="epic-membership-upgrade-button">';

                $message .= __('Log In', 'epic-membership');

                $message .= '</a>';

                break;



            case 'no_membership':

            case 'membership_expired':

                $message .= '<div class="epic-membership-protection-message">';

                if (isset($access_result['allowed_tiers']) && !empty($access_result['allowed_tiers'])) {

                    // Tier-restricted scheduled content

                    $tier_list = implode(', ', $access_result['allowed_tiers']);

                    $message .= sprintf(

                        __('This content is restricted to %s members only.', 'epic-membership'),

                        '<strong>' . esc_html($tier_list) . '</strong>'

                    );

                } else {

                    // Regular members-only content

                    $message .= sprintf(

                        __('This content requires %s membership or higher.', 'epic-membership'),

                        '<strong>' . esc_html($access_result['required_tier']) . '</strong>'

                    );

                }

                $message .= '</div>';



                // Get membership status page URL

                $membership_status_url = $this->get_membership_status_url();

                $message .= '<a href="' . esc_url($membership_status_url) . '" class="epic-membership-upgrade-button">';

                $message .= __('Upgrade Membership', 'epic-membership');

                $message .= '</a>';

                break;



            case 'insufficient_tier':

                $message .= '<div class="epic-membership-protection-message">';

                if (isset($access_result['allowed_tiers']) && !empty($access_result['allowed_tiers'])) {

                    // Tier-restricted scheduled content

                    $tier_list = implode(', ', $access_result['allowed_tiers']);

                    $current_tier = isset($access_result['user_tier']) ? $access_result['user_tier'] : 'Unknown';

                    $message .= sprintf(

                        __('This content is restricted to %s members. Your current tier: %s', 'epic-membership'),

                        '<strong>' . esc_html($tier_list) . '</strong>',

                        '<strong>' . esc_html($current_tier) . '</strong>'

                    );

                } else {

                    // Regular members-only content

                    $message .= sprintf(

                        __('This content requires %s membership or higher.', 'epic-membership'),

                        '<strong>' . esc_html($access_result['required_tier']) . '</strong>'

                    );

                }

                $message .= '</div>';



                // Get membership status page URL

                $membership_status_url = $this->get_membership_status_url();

                $message .= '<a href="' . esc_url($membership_status_url) . '" class="epic-membership-upgrade-button">';

                $message .= __('Upgrade Membership', 'epic-membership');

                $message .= '</a>';

                break;



            case 'not_logged_in':

                $message .= '<div class="epic-membership-protection-message">';

                if (isset($access_result['allowed_tiers']) && !empty($access_result['allowed_tiers'])) {

                    $tier_list = implode(', ', $access_result['allowed_tiers']);

                    $message .= sprintf(

                        __('This content is restricted to %s members. Please log in to access.', 'epic-membership'),

                        '<strong>' . esc_html($tier_list) . '</strong>'

                    );

                } else {

                    $message .= __('Please log in to access this content.', 'epic-membership');

                }

                $message .= '</div>';

                $message .= '<a href="' . wp_login_url(get_permalink()) . '" class="epic-membership-upgrade-button">';

                $message .= __('Log In', 'epic-membership');

                $message .= '</a>';

                break;



            case 'not_yet_released':

                // Get early access tier information (only paid tiers)

                $early_access_message = $this->get_early_access_message($access_settings);



                // Check if Free tier is required for this content

                $free_tier_required = $this->is_free_tier_required($access_settings);



                if (!get_current_user_id()) {

                    $message .= '<div class="epic-membership-protection-message">';

                    if ($early_access_message) {

                        $message .= sprintf(__('This content is scheduled for release. %s get early access!', 'epic-membership'), $early_access_message);

                    } elseif ($free_tier_required) {

                        $message .= __('This content is scheduled for release. Free membership required to access when released.', 'epic-membership');

                    } else {

                        $message .= __('This content is scheduled for release.', 'epic-membership');

                    }

                    $message .= '</div>';

                    if ($early_access_message) {

                        $message .= '<a href="' . wp_login_url(get_permalink()) . '" class="epic-membership-upgrade-button">';

                        $message .= __('Log In for Early Access', 'epic-membership');

                        $message .= '</a>';

                    } elseif ($free_tier_required) {

                        $message .= '<a href="' . wp_login_url(get_permalink()) . '" class="epic-membership-upgrade-button">';

                        $message .= __('Log In to Access When Released', 'epic-membership');

                        $message .= '</a>';

                    }

                } else {

                    // User is logged in but doesn't have early access

                    $message .= '<div class="epic-membership-protection-message">';

                    if ($early_access_message) {

                        $message .= sprintf(__('This content is scheduled for release. %s get early access!', 'epic-membership'), $early_access_message);

                        $message .= '</div>';

                        $message .= '<a href="#" class="epic-membership-upgrade-button" data-upgrade-url="">';

                        $message .= __('Upgrade for Early Access', 'epic-membership');

                        $message .= '</a>';

                    } else {

                        $message .= __('This content is scheduled for release.', 'epic-membership');

                        $message .= '</div>';

                    }

                }



                $message .= '<div class="epic-membership-protection-message" style="margin-top: 15px;">';

                $message .= __('Or wait until the scheduled release time:', 'epic-membership');

                $message .= '</div>';



                // Continue with countdown timer display

                $user_release_time = $this->timezone_handler->convert_to_user_timezone(

                    $access_result['release_time']

                );

                $formatted_time = $this->timezone_handler->format_datetime_for_user(

                    $access_result['release_time']

                );



                $message .= '<div class="epic-membership-protection-message">';

                $message .= __('This content will be available on:', 'epic-membership');

                $message .= '</div>';

                

                // Add countdown timer

                $countdown_data = $this->timezone_handler->generate_countdown_js(

                    $access_result['release_time'],

                    $access_result['release_timezone']

                );

                

                $message .= '<div class="epic-membership-countdown" 

                                  data-release-time="' . esc_attr($countdown_data['release_time']) . '"

                                  data-server-timezone="' . esc_attr($countdown_data['server_timezone']) . '">';

                $message .= '<div class="epic-membership-countdown-title">' . esc_html($formatted_time) . '</div>';

                $message .= '<div class="epic-membership-countdown-timer">';

                $message .= '<div class="epic-membership-countdown-unit">';

                $message .= '<span class="epic-membership-countdown-number epic-countdown-days">00</span>';

                $message .= '<span class="epic-membership-countdown-label">' . __('Days', 'epic-membership') . '</span>';

                $message .= '</div>';

                $message .= '<div class="epic-membership-countdown-unit">';

                $message .= '<span class="epic-membership-countdown-number epic-countdown-hours">00</span>';

                $message .= '<span class="epic-membership-countdown-label">' . __('Hours', 'epic-membership') . '</span>';

                $message .= '</div>';

                $message .= '<div class="epic-membership-countdown-unit">';

                $message .= '<span class="epic-membership-countdown-number epic-countdown-minutes">00</span>';

                $message .= '<span class="epic-membership-countdown-label">' . __('Minutes', 'epic-membership') . '</span>';

                $message .= '</div>';

                $message .= '<div class="epic-membership-countdown-unit">';

                $message .= '<span class="epic-membership-countdown-number epic-countdown-seconds">00</span>';

                $message .= '<span class="epic-membership-countdown-label">' . __('Seconds', 'epic-membership') . '</span>';

                $message .= '</div>';

                $message .= '</div>';

                $message .= '</div>';

                break;

                

            default:

                $message .= '<div class="epic-membership-protection-message">';

                $message .= __('This content is not available.', 'epic-membership');

                $message .= '</div>';

        }

        

        // Add teaser content if available

        if ($access_settings && $access_settings->teaser_content) {

            $message .= '<div class="epic-membership-content-teaser">';

            $message .= '<div class="epic-membership-teaser-content">';

            $message .= wp_kses_post($access_settings->teaser_content);

            $message .= '<div class="epic-membership-teaser-fade"></div>';

            $message .= '</div>';

            $message .= '</div>';

        }

        

        $message .= '</div>';

        

        return apply_filters('epic_membership_protected_content_message', $message, $post_id, $access_result);

    }

    

    /**

     * Log access attempt for analytics

     */

    private function log_access_attempt($post_id, $access_result) {

        if (!get_option('epic_membership_enable_logging', true)) {

            return;

        }

        

        $user_id = get_current_user_id();

        $user_tier_id = null;

        $required_tier_id = null;

        

        if (isset($access_result['user_tier_id'])) {

            $user_tier_id = $access_result['user_tier_id'];

        }

        

        if (isset($access_result['required_tier_id'])) {

            $required_tier_id = $access_result['required_tier_id'];

        }

        

        $this->database->log_access(

            $user_id,

            $post_id,

            $access_result['has_access'],

            $access_result['access_type'],

            $user_tier_id,

            $required_tier_id

        );

    }

    

    /**

     * Modify queries to exclude protected content from listings

     */

    public function modify_query_for_protected_content($query) {

        // Only modify main queries on frontend

        if (is_admin() || !$query->is_main_query()) {

            return;

        }



        // Don't modify single post queries

        if ($query->is_singular()) {

            return;

        }



        // Handle RSS feeds - exclude scheduled posts that haven't been released yet

        if ($query->is_feed()) {

            $this->filter_rss_feed_posts($query);

            return;

        }



        // For other listings, we'll allow protected content but filter it in the_content

        // This can be customized based on requirements

    }

    

    /**

     * Filter RSS feed posts to exclude scheduled content that hasn't been released

     */

    private function filter_rss_feed_posts($query) {

        global $wpdb;



        // Get the content access table

        $content_access_table = $this->database->get_table('content_access');



        // Create a unique identifier for this specific query

        $query_id = spl_object_hash($query);



        // Add a WHERE clause to exclude scheduled posts that haven't been released yet

        $filter_callback = function($where, $wp_query) use ($content_access_table, $query, $query_id) {

            // Only apply to the specific RSS feed query we're processing

            if (spl_object_hash($wp_query) !== $query_id || !$wp_query->is_feed()) {

                return $where;

            }



            global $wpdb;



            // Exclude posts that are scheduled but not yet released

            // This query excludes posts that:

            // 1. Have scheduled access type AND

            // 2. Have a scheduled_release time in the future

            $where .= " AND {$wpdb->posts}.ID NOT IN (

                SELECT ca.post_id

                FROM {$content_access_table} ca

                WHERE ca.access_type = 'scheduled'

                AND ca.scheduled_release > NOW()

                AND ca.is_active = 1

            )";



            // Remove this filter after it's been applied once to prevent affecting other queries

            remove_filter('posts_where', $filter_callback, 10);



            return $where;

        };



        // Add the filter

        add_filter('posts_where', $filter_callback, 10, 2);

    }



    /**

     * Filter REST API content

     */

    public function filter_rest_content($response, $post, $request) {

        $access_result = $this->check_content_access($post->ID);



        if (!$access_result['has_access']) {

            // Remove content from REST response

            $response->data['content']['rendered'] = $this->get_protected_content_message($post->ID, $access_result);



            // Also filter excerpt

            if (isset($response->data['excerpt']['rendered'])) {

                $access_settings = $this->database->get_content_access($post->ID);

                if ($access_settings && $access_settings->teaser_content) {

                    $response->data['excerpt']['rendered'] = wp_trim_words($access_settings->teaser_content, 55);

                }

            }

        }



        return $response;

    }

    

    /**

     * Exclude protected content from search results

     */

    public function exclude_protected_from_search($where, $query) {

        if (!$query->is_search() || is_admin()) {

            return $where;

        }

        

        // This is a simplified version - in production, you might want more sophisticated filtering

        return $where;

    }

    

    /**

     * Add membership-related body classes

     */

    public function add_membership_body_classes($classes) {

        $user_id = get_current_user_id();

        

        if ($user_id) {

            $membership = $this->database->get_user_membership($user_id);

            

            if ($membership) {

                $classes[] = 'has-membership';

                $classes[] = 'membership-' . $membership->tier_slug;

                $classes[] = 'membership-level-' . $membership->tier_level;

                

                // Check if membership is active

                if ($membership->is_active && (!$membership->end_date || strtotime($membership->end_date) > time())) {

                    $classes[] = 'membership-active';

                } else {

                    $classes[] = 'membership-expired';

                }

            } else {

                $classes[] = 'no-membership';

            }

        } else {

            $classes[] = 'not-logged-in';

        }

        

        return $classes;

    }

    

    /**

     * Membership shortcode for conditional content

     */

    public function membership_shortcode($atts, $content = '') {

        $atts = shortcode_atts(array(

            'tier' => '',

            'level' => '',

            'logged_in' => '',

            'capability' => '',

        ), $atts);



        $user_id = get_current_user_id();



        // Check logged in requirement

        if ($atts['logged_in'] === 'true' && !$user_id) {

            return '';

        }



        if ($atts['logged_in'] === 'false' && $user_id) {

            return '';

        }



        // Check membership requirements

        if ($atts['tier'] || $atts['level'] || $atts['capability']) {

            if (!$user_id) {

                return '';

            }



            $membership = $this->database->get_user_membership($user_id);



            if (!$membership || !$membership->is_active) {

                return '';

            }



            // Check specific tier

            if ($atts['tier'] && $membership->tier_slug !== $atts['tier']) {

                return '';

            }



            // Check minimum level

            if ($atts['level'] && $membership->tier_level < intval($atts['level'])) {

                return '';

            }



            // Check specific capability

            if ($atts['capability'] && !$this->database->user_has_capability($user_id, $atts['capability'])) {

                return '';

            }

        }



        return do_shortcode($content);

    }

    

    /**

     * AJAX handler for checking content access

     */

    public function ajax_check_content_access() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        $post_id = intval($_POST['post_id'] ?? 0);

        

        if (!$post_id) {

            wp_send_json_error('Invalid post ID');

        }

        

        $access_result = $this->check_content_access($post_id);

        

        wp_send_json_success($access_result);

    }



    /**

     * Get early access message for scheduled content

     */

    private function get_early_access_message($access_settings) {

        if (empty($access_settings->allowed_tier_ids)) {

            return '';

        }



        $allowed_tier_ids = json_decode($access_settings->allowed_tier_ids, true);

        if (!is_array($allowed_tier_ids) || empty($allowed_tier_ids)) {

            return '';

        }



        global $wpdb;

        $tiers_table = $this->database->get_table('tiers');

        $placeholders = implode(',', array_fill(0, count($allowed_tier_ids), '%d'));



        // Only get paid tiers (level > 0) for early access message

        // Free tier should not be mentioned as "early access" since it's the minimum requirement

        $tier_names = $wpdb->get_col($wpdb->prepare("

            SELECT name

            FROM $tiers_table

            WHERE id IN ($placeholders)

            AND level > 0

            ORDER BY level ASC

        ", $allowed_tier_ids));



        if (empty($tier_names)) {

            return '';

        }



        if (count($tier_names) === 1) {

            return sprintf('<strong>%s</strong> members', $tier_names[0]);

        } else {

            $last_tier = array_pop($tier_names);

            return sprintf('<strong>%s</strong> and <strong>%s</strong> members', implode(', ', $tier_names), $last_tier);

        }

    }



    /**

     * Check if Free tier is allowed in the tier list

     */

    private function is_free_tier_allowed($allowed_tier_ids) {

        $free_tier_id = $this->get_free_tier_id();

        return $free_tier_id && in_array($free_tier_id, $allowed_tier_ids);

    }



    /**

     * Check if Free tier is required for scheduled content

     */

    private function is_free_tier_required($access_settings) {

        if (empty($access_settings->allowed_tier_ids)) {

            return false;

        }



        $allowed_tier_ids = json_decode($access_settings->allowed_tier_ids, true);

        if (!is_array($allowed_tier_ids) || empty($allowed_tier_ids)) {

            return false;

        }



        return $this->is_free_tier_allowed($allowed_tier_ids);

    }



    /**

     * Get Free tier ID (level 0)

     */

    private function get_free_tier_id() {

        global $wpdb;

        $tiers_table = $this->database->get_table('tiers');



        $free_tier = $wpdb->get_row("SELECT id FROM $tiers_table WHERE level = 0 AND is_active = 1 LIMIT 1");

        return $free_tier ? $free_tier->id : null;

    }







    /**

     * Check time-based release (public release time)

     * This is only used for scheduled content WITHOUT tier restrictions

     */

    private function check_time_based_release($access_settings) {

        $is_released = $this->timezone_handler->is_content_released(

            $access_settings->scheduled_release,

            $access_settings->release_timezone

        );



        if ($is_released) {

            return array(

                'has_access' => true,

                'access_type' => 'scheduled',

                'reason' => 'content_released',

                'release_time' => $access_settings->scheduled_release

            );

        }



        $time_remaining = $this->timezone_handler->get_time_until_release(

            $access_settings->scheduled_release,

            $access_settings->release_timezone

        );



        return array(

            'has_access' => false,

            'access_type' => 'scheduled',

            'reason' => 'not_yet_released',

            'release_time' => $access_settings->scheduled_release,

            'release_timezone' => $access_settings->release_timezone,

            'time_remaining' => $time_remaining

        );

    }



    /**

     * Check tier-restricted access denial

     * For scheduled content WITH tier restrictions, access is always denied if user doesn't have the right tier

     */

    private function check_tier_restricted_access_denied($access_settings, $reason, $user_membership = null) {

        // Get allowed tier names for better error messages

        $allowed_tier_names = array();

        if (!empty($access_settings->allowed_tier_ids)) {

            $allowed_tier_ids = json_decode($access_settings->allowed_tier_ids, true);

            if (is_array($allowed_tier_ids)) {

                global $wpdb;

                $tiers_table = $this->database->get_table('tiers');

                $placeholders = implode(',', array_fill(0, count($allowed_tier_ids), '%d'));

                $tier_names = $wpdb->get_col($wpdb->prepare("

                    SELECT name

                    FROM $tiers_table

                    WHERE id IN ($placeholders)

                    ORDER BY level ASC

                ", $allowed_tier_ids));

                $allowed_tier_names = $tier_names;

            }

        }



        $result = array(

            'has_access' => false,

            'access_type' => 'scheduled',

            'reason' => $reason,

            'release_time' => $access_settings->scheduled_release,

            'allowed_tiers' => $allowed_tier_names

        );



        if ($user_membership) {

            $result['user_tier'] = $user_membership->tier_name;

            $result['user_level'] = $user_membership->tier_level;

        }



        return $result;

    }



    /**

     * Get membership status page URL

     */

    private function get_membership_status_url() {

        $page_id = get_option('epic_membership_status_page_id');

        return $page_id ? get_permalink($page_id) : home_url('/membership-status/');

    }

}

