<?php

/**

 * Membership Tiers Management Class

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Tiers {

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Handle form submissions

        add_action('admin_post_epic_membership_save_tier', array($this, 'save_tier'));

        add_action('admin_post_epic_membership_delete_tier', array($this, 'delete_tier'));

        

        // AJAX handlers

        add_action('wp_ajax_epic_membership_get_tier', array($this, 'ajax_get_tier'));

        add_action('wp_ajax_epic_membership_toggle_tier_status', array($this, 'ajax_toggle_tier_status'));

    }

    

    /**

     * Get all membership tiers

     */

    public function get_all_tiers($active_only = false) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');

        

        $where_clause = $active_only ? 'WHERE is_active = 1' : '';

        

        return $wpdb->get_results("SELECT * FROM $table_name $where_clause ORDER BY level ASC");

    }

    

    /**

     * Get single tier by ID

     */

    public function get_tier($tier_id) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');

        

        return $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $table_name WHERE id = %d",

            $tier_id

        ));

    }

    

    /**

     * Get tier by slug

     */

    public function get_tier_by_slug($slug) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');

        

        return $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $table_name WHERE slug = %s",

            $slug

        ));

    }

    

    /**

     * Create or update tier

     */

    public function save_tier_data($data) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');

        

        // Sanitize data

        $tier_data = array(

            'name' => sanitize_text_field($data['name']),

            'slug' => sanitize_title($data['slug'] ?: $data['name']),

            'description' => wp_kses_post($data['description']),

            'level' => intval($data['level']),

            'capabilities' => json_encode($data['capabilities'] ?? array()),

            'price' => floatval($data['price'] ?? 0),

            'currency' => sanitize_text_field($data['currency'] ?? 'USD'),

            'duration_days' => !empty($data['duration_days']) ? intval($data['duration_days']) : null,

            'is_active' => isset($data['is_active']) ? 1 : 0

        );

        

        // Check if tier exists

        if (!empty($data['id'])) {

            // Update existing tier

            $tier_id = intval($data['id']);



            $result = $wpdb->update($table_name, $tier_data, array('id' => $tier_id));



            if ($result === false) {

                error_log('Epic Membership: Tier update failed with error: ' . $wpdb->last_error);

            }



            return $result !== false ? $tier_id : false;

        } else {

            // Create new tier



            $result = $wpdb->insert($table_name, $tier_data);



            if ($result === false) {

                error_log('Epic Membership: Tier insert failed with error: ' . $wpdb->last_error);

            }



            return $result ? $wpdb->insert_id : false;

        }

    }

    

    /**

     * Delete tier

     */

    public function delete_tier_data($tier_id) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');

        

        // Check if tier has active memberships

        $memberships_table = $this->database->get_table('user_memberships');

        $active_memberships = $wpdb->get_var($wpdb->prepare(

            "SELECT COUNT(*) FROM $memberships_table WHERE tier_id = %d AND is_active = 1",

            $tier_id

        ));

        

        if ($active_memberships > 0) {

            return new WP_Error('tier_has_active_memberships', 

                __('Cannot delete tier with active memberships.', 'epic-membership'));

        }

        

        // Delete the tier

        $result = $wpdb->delete($table_name, array('id' => $tier_id));

        return $result !== false;

    }

    

    /**

     * Toggle tier status

     */

    public function toggle_tier_status($tier_id) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');

        

        // Get current status

        $current_status = $wpdb->get_var($wpdb->prepare(

            "SELECT is_active FROM $table_name WHERE id = %d",

            $tier_id

        ));

        

        if ($current_status === null) {

            return false;

        }

        

        // Toggle status

        $new_status = $current_status ? 0 : 1;

        

        return $wpdb->update(

            $table_name,

            array('is_active' => $new_status),

            array('id' => $tier_id)

        ) !== false;

    }

    

    /**

     * Get tier statistics

     */

    public function get_tier_stats($tier_id) {

        global $wpdb;

        $memberships_table = $this->database->get_table('user_memberships');

        

        return array(

            'active_members' => $wpdb->get_var($wpdb->prepare(

                "SELECT COUNT(*) FROM $memberships_table WHERE tier_id = %d AND is_active = 1",

                $tier_id

            )),

            'total_members' => $wpdb->get_var($wpdb->prepare(

                "SELECT COUNT(*) FROM $memberships_table WHERE tier_id = %d",

                $tier_id

            )),

            'expired_members' => $wpdb->get_var($wpdb->prepare(

                "SELECT COUNT(*) FROM $memberships_table WHERE tier_id = %d AND is_active = 0",

                $tier_id

            ))

        );

    }

    

    /**

     * Handle tier save form submission

     */

    public function save_tier() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['_wpnonce'], 'epic_membership_save_tier')) {

            wp_die(__('Security check failed.', 'epic-membership'));

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }

        

        // Validate required fields

        if (empty($_POST['name'])) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-tiers',

                'epic_membership_message' => 'error'

            ), admin_url('admin.php')));

            exit;

        }

        

        // Save tier

        $result = $this->save_tier_data($_POST);

        

        if ($result) {

            $message = !empty($_POST['id']) ? 'tier_updated' : 'tier_created';

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-tiers',

                'epic_membership_message' => $message

            ), admin_url('admin.php')));

        } else {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-tiers',

                'epic_membership_message' => 'error'

            ), admin_url('admin.php')));

        }

        exit;

    }

    

    /**

     * Handle tier deletion

     */

    public function delete_tier() {

        // Verify nonce

        if (!wp_verify_nonce($_GET['_wpnonce'], 'epic_membership_delete_tier_' . $_GET['tier_id'])) {

            wp_die(__('Security check failed.', 'epic-membership'));

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_die(__('You do not have sufficient permissions.', 'epic-membership'));

        }

        

        $tier_id = intval($_GET['tier_id']);

        $result = $this->delete_tier_data($tier_id);

        

        if (is_wp_error($result)) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-tiers',

                'epic_membership_message' => 'error'

            ), admin_url('admin.php')));

        } elseif ($result) {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-tiers',

                'epic_membership_message' => 'tier_deleted'

            ), admin_url('admin.php')));

        } else {

            wp_redirect(add_query_arg(array(

                'page' => 'epic-membership-tiers',

                'epic_membership_message' => 'error'

            ), admin_url('admin.php')));

        }

        exit;

    }

    

    /**

     * AJAX: Get tier data

     */

    public function ajax_get_tier() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $tier_id = intval($_POST['tier_id']);

        $tier = $this->get_tier($tier_id);

        

        if ($tier) {

            // Decode capabilities

            $tier->capabilities = json_decode($tier->capabilities, true);

            wp_send_json_success($tier);

        } else {

            wp_send_json_error('Tier not found');

        }

    }

    

    /**

     * AJAX: Toggle tier status

     */

    public function ajax_toggle_tier_status() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error('Security check failed');

        }

        

        // Check permissions

        if (!current_user_can('manage_options')) {

            wp_send_json_error('Insufficient permissions');

        }

        

        $tier_id = intval($_POST['tier_id']);

        $result = $this->toggle_tier_status($tier_id);

        

        if ($result) {

            wp_send_json_success('Status updated');

        } else {

            wp_send_json_error('Failed to update status');

        }

    }

    

    /**

     * Check if user can access tier level

     */

    public function user_can_access_tier_level($user_id, $required_level) {

        $user_membership = $this->database->get_user_membership($user_id);

        

        if (!$user_membership) {

            return $required_level <= 0; // Free tier access

        }

        

        return $user_membership->tier_level >= $required_level;

    }

    

    /**

     * Get available capabilities

     */

    public function get_available_capabilities() {

        return array(

            'read_free_content' => __('Read Free Content', 'epic-membership'),

            'read_premium_content' => __('Read Premium Content', 'epic-membership'),

            'read_vip_content' => __('Read VIP Content', 'epic-membership'),

            'ad_free_experience' => __('Ad-Free Experience', 'epic-membership'),

            'priority_support' => __('Priority Support', 'epic-membership'),

            'early_access' => __('Early Access to Content', 'epic-membership'),

            'exclusive_content' => __('Exclusive Content Access', 'epic-membership'),

            'download_content' => __('Download Content', 'epic-membership')

        );

    }

}

