/**

 * Admin CSS for Epic Membership Ad Units Management

 */



/* Container */

.epic-membership-ad-units-container {

    margin-top: 20px;

}



/* Ad Units Grid */

.ad-units-grid {

    display: grid;

    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));

    gap: 20px;

    margin-bottom: 30px;

}



/* Ad Unit Cards */

.ad-unit-card {

    background: #fff;

    border: 1px solid #ddd;

    border-radius: 8px;

    padding: 20px;

    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    transition: all 0.3s ease;

    cursor: move;

}



.ad-unit-card:hover {

    box-shadow: 0 4px 8px rgba(0,0,0,0.15);

    transform: translateY(-2px);

}



.ad-unit-card.ui-sortable-helper {

    transform: rotate(5deg);

    box-shadow: 0 8px 16px rgba(0,0,0,0.2);

}



/* Ad Unit Header */

.ad-unit-header {

    display: flex;

    align-items: center;

    margin-bottom: 15px;

    gap: 10px;

}



.ad-unit-drag-handle {

    color: #999;

    font-size: 16px;

    cursor: grab;

    padding: 5px;

}



.ad-unit-drag-handle:active {

    cursor: grabbing;

}



.ad-unit-name {

    flex: 1;

    margin: 0;

    font-size: 16px;

    font-weight: 600;

    color: #333;

}



/* Toggle Switch */

.toggle-switch {

    position: relative;

    display: inline-block;

    width: 50px;

    height: 24px;

}



.toggle-switch input {

    opacity: 0;

    width: 0;

    height: 0;

}



.toggle-slider {

    position: absolute;

    cursor: pointer;

    top: 0;

    left: 0;

    right: 0;

    bottom: 0;

    background-color: #ccc;

    transition: .4s;

    border-radius: 24px;

}



.toggle-slider:before {

    position: absolute;

    content: "";

    height: 18px;

    width: 18px;

    left: 3px;

    bottom: 3px;

    background-color: white;

    transition: .4s;

    border-radius: 50%;

}



input:checked + .toggle-slider {

    background-color: #2196F3;

}



input:checked + .toggle-slider:before {

    transform: translateX(26px);

}



/* Ad Unit Details */

.ad-unit-meta {

    display: flex;

    gap: 10px;

    margin-bottom: 10px;

    flex-wrap: wrap;

}



.ad-type-badge, .placement-badge {

    padding: 4px 8px;

    border-radius: 4px;

    font-size: 12px;

    font-weight: 500;

    text-transform: uppercase;

}



.ad-type-badge {

    background: #e3f2fd;

    color: #1976d2;

}



.ad-type-popunder { background: #fff3e0; color: #f57c00; }

.ad-type-social_bar { background: #e8f5e8; color: #388e3c; }

.ad-type-banner { background: #fce4ec; color: #c2185b; }

.ad-type-native { background: #f3e5f5; color: #7b1fa2; }

.ad-type-in_page_push { background: #e0f2f1; color: #00695c; }



.placement-badge {

    background: #f5f5f5;

    color: #666;

}



.ad-unit-code-preview {

    background: #f8f9fa;

    padding: 10px;

    border-radius: 4px;

    margin-bottom: 15px;

    font-family: monospace;

    font-size: 12px;

    color: #666;

    border-left: 3px solid #2196F3;

}



.ad-unit-actions {

    display: flex;

    gap: 10px;

}



.ad-unit-actions .button {

    flex: 1;

    text-align: center;

}



/* No Ad Units State */

.no-ad-units {

    text-align: center;

    padding: 60px 20px;

    background: #f9f9f9;

    border-radius: 8px;

    border: 2px dashed #ddd;

}



.no-ad-units p {

    font-size: 16px;

    color: #666;

    margin-bottom: 10px;

}



/* Modal Styles */

.epic-modal {

    position: fixed;

    z-index: 100000;

    left: 0;

    top: 0;

    width: 100%;

    height: 100%;

    background-color: transparent; /* No background overlay */

    display: flex;

    align-items: center;

    justify-content: center;

}



.epic-modal-content {

    background-color: #fff;

    border-radius: 8px;

    width: 90%;

    max-width: 600px;

    max-height: 90vh;

    overflow-y: auto;

    box-shadow: 0 10px 30px rgba(0,0,0,0.3);

}



.epic-modal-header {

    padding: 20px 30px;

    border-bottom: 1px solid #eee;

    display: flex;

    justify-content: space-between;

    align-items: center;

}



.epic-modal-header h2 {

    margin: 0;

    font-size: 20px;

}



.epic-modal-close {

    background: none;

    border: none;

    font-size: 24px;

    cursor: pointer;

    color: #999;

    padding: 0;

    width: 30px;

    height: 30px;

    display: flex;

    align-items: center;

    justify-content: center;

}



.epic-modal-close:hover {

    color: #333;

}



.epic-modal-body {

    padding: 30px;

}



.epic-modal-footer {

    padding: 20px 30px;

    border-top: 1px solid #eee;

    display: flex;

    gap: 10px;

    justify-content: flex-end;

}



/* Form Styles */

.epic-modal .form-table th {

    width: 150px;

    padding-left: 0;

}



.epic-modal .form-table td {

    padding-right: 0;

}



.epic-modal textarea.code {

    font-family: Consolas, Monaco, monospace;

    font-size: 13px;

}



/* Help Section */

.epic-membership-help-section {

    margin-top: 40px;

    padding: 20px;

    background: #f9f9f9;

    border-radius: 8px;

}



.help-columns {

    display: grid;

    grid-template-columns: 1fr 1fr;

    gap: 30px;

    margin-bottom: 20px;

}



.help-column h4 {

    margin-top: 0;

    color: #333;

}



.help-column ul {

    margin: 0;

    padding-left: 20px;

}



.help-column li {

    margin-bottom: 8px;

}



.help-note {

    padding: 15px;

    background: #e3f2fd;

    border-left: 4px solid #2196F3;

    border-radius: 4px;

}



.help-note p {

    margin: 0;

}



/* Sortable Placeholder */

.ad-unit-placeholder {

    background: #f0f0f0;

    border: 2px dashed #ccc;

    border-radius: 8px;

    height: 200px;

    display: flex;

    align-items: center;

    justify-content: center;

    color: #999;

}



.ad-unit-placeholder:before {

    content: "Drop here";

    font-weight: 500;

}



/* Responsive Design */

@media (max-width: 768px) {

    .ad-units-grid {

        grid-template-columns: 1fr;

    }

    

    .help-columns {

        grid-template-columns: 1fr;

        gap: 20px;

    }

    

    .epic-modal-content {

        width: 95%;

        margin: 20px;

    }

    

    .epic-modal-header,

    .epic-modal-body,

    .epic-modal-footer {

        padding: 20px;

    }

}



/* Loading States */

.button:disabled {

    opacity: 0.6;

    cursor: not-allowed;

}



/* Success/Error States */

.notice {

    margin: 15px 0;

}



/* Animation for new cards */

@keyframes slideInUp {

    from {

        opacity: 0;

        transform: translateY(20px);

    }

    to {

        opacity: 1;

        transform: translateY(0);

    }

}



.ad-unit-card.new {

    animation: slideInUp 0.3s ease-out;

}

