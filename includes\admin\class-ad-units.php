<?php

/**

 * Ad Units Admin Class for Epic Membership Plugin

 * Handles the admin interface for managing multiple ad units

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_Ad_Units_Admin {

    

    /**

     * Ad unit manager instance

     */

    private $ad_unit_manager;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->ad_unit_manager = new Epic_Membership_Ad_Unit_Manager();

        $this->init_hooks();

    }

    

    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // AJAX handlers for ad unit management

        add_action('wp_ajax_epic_membership_get_ad_unit', array($this, 'ajax_get_ad_unit'));

        add_action('wp_ajax_epic_membership_save_ad_unit', array($this, 'ajax_save_ad_unit'));

        add_action('wp_ajax_epic_membership_delete_ad_unit', array($this, 'ajax_delete_ad_unit'));

        add_action('wp_ajax_epic_membership_toggle_ad_unit', array($this, 'ajax_toggle_ad_unit'));

        add_action('wp_ajax_epic_membership_reorder_ad_units', array($this, 'ajax_reorder_ad_units'));

    }



    

    /**

     * Admin page

     */

    public function admin_page() {

        $ad_units = $this->ad_unit_manager->get_ad_units();

        $ad_types = $this->ad_unit_manager->get_supported_ad_types();

        $placements = $this->ad_unit_manager->get_supported_placements();



        include EPIC_MEMBERSHIP_PLUGIN_DIR . 'includes/admin/views/ad-units.php';

    }

    

    /**

     * AJAX: Get ad unit

     */

    public function ajax_get_ad_unit() {

        check_ajax_referer('epic_membership_ad_units', 'nonce');



        if (!current_user_can('manage_options')) {

            wp_die(__('Insufficient permissions.', 'epic-membership'));

        }



        $ad_unit_id = intval(isset($_POST['ad_unit_id']) ? $_POST['ad_unit_id'] : 0);



        if ($ad_unit_id <= 0) {

            wp_send_json_error(__('Invalid ad unit ID.', 'epic-membership'));

        }



        $ad_unit = $this->ad_unit_manager->get_ad_unit($ad_unit_id);



        if (!$ad_unit) {

            wp_send_json_error(__('Ad unit not found.', 'epic-membership'));

        }



        wp_send_json_success($ad_unit);

    }



    /**

     * AJAX: Save ad unit

     */

    public function ajax_save_ad_unit() {

        // Enhanced error logging for debugging (only in debug mode)

        if (defined('WP_DEBUG') && WP_DEBUG) {

            error_log('Epic Membership: ajax_save_ad_unit called');

            error_log('Epic Membership: POST data size: ' . strlen(serialize($_POST)) . ' bytes');

            error_log('Epic Membership: User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'));

            error_log('Epic Membership: Request Method: ' . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown'));

        }



        // Log ad_code length and first few characters for debugging

        if (isset($_POST['ad_code'])) {

            $ad_code = $_POST['ad_code'];

            error_log('Epic Membership: Ad code length: ' . strlen($ad_code));

            error_log('Epic Membership: Ad code preview: ' . substr($ad_code, 0, 100) . '...');



            // Check for common WAF triggers

            $waf_triggers = array(

                'script' => substr_count(strtolower($ad_code), '<script'),

                'eval' => substr_count(strtolower($ad_code), 'eval('),

                'document.write' => substr_count(strtolower($ad_code), 'document.write'),

                'innerHTML' => substr_count(strtolower($ad_code), 'innerhtml'),

                'src=' => substr_count(strtolower($ad_code), 'src='),

                'http://' => substr_count(strtolower($ad_code), 'http://'),

                'https://' => substr_count(strtolower($ad_code), 'https://'),

            );

            error_log('Epic Membership: WAF trigger analysis: ' . print_r($waf_triggers, true));

        }



        try {

            check_ajax_referer('epic_membership_ad_units', 'nonce');

        } catch (Exception $e) {

            error_log('Epic Membership: Nonce verification failed: ' . $e->getMessage());

            wp_send_json_error(__('Security check failed. Please refresh the page and try again.', 'epic-membership'));

            return;

        }



        if (!current_user_can('manage_options')) {

            error_log('Epic Membership: User lacks manage_options capability. User ID: ' . get_current_user_id());

            wp_send_json_error(__('Insufficient permissions.', 'epic-membership'));

            return;

        }



        // Use isset() for better PHP compatibility instead of null coalescing operator

        $ad_unit_id = intval(isset($_POST['ad_unit_id']) ? $_POST['ad_unit_id'] : 0);



        // Handle encoded ad code to bypass WAF issues

        $ad_code = '';

        if (isset($_POST['ad_code_encoded']) && $_POST['ad_code_encoded'] === '1') {

            $ad_code = base64_decode(isset($_POST['ad_code']) ? $_POST['ad_code'] : '');

            error_log('Epic Membership: Using base64 decoded ad code');

        } else {

            $ad_code = wp_unslash(isset($_POST['ad_code']) ? $_POST['ad_code'] : '');

            error_log('Epic Membership: Using plain ad code');

        }



        $ad_unit_data = array(

            'name' => sanitize_text_field(isset($_POST['name']) ? $_POST['name'] : ''),

            'ad_type' => sanitize_text_field(isset($_POST['ad_type']) ? $_POST['ad_type'] : ''),

            'placement' => sanitize_text_field(isset($_POST['placement']) ? $_POST['placement'] : ''),

            'ad_code' => $ad_code,

            'is_active' => intval(isset($_POST['is_active']) ? $_POST['is_active'] : 0)

        );



        error_log('Epic Membership: Processed ad unit data: ' . print_r($ad_unit_data, true));



        try {

            if ($ad_unit_id > 0) {

                // Update existing ad unit

                error_log('Epic Membership: Updating ad unit ID: ' . $ad_unit_id);

                $result = $this->ad_unit_manager->update_ad_unit($ad_unit_id, $ad_unit_data);

            } else {

                // Create new ad unit

                error_log('Epic Membership: Creating new ad unit');

                $result = $this->ad_unit_manager->create_ad_unit($ad_unit_data);

            }



            if (is_wp_error($result)) {

                error_log('Epic Membership: Ad unit save failed: ' . $result->get_error_message());

                wp_send_json_error($result->get_error_message());

            } else {

                error_log('Epic Membership: Ad unit saved successfully. Result: ' . print_r($result, true));

                wp_send_json_success(array(

                    'message' => __('Ad unit saved successfully.', 'epic-membership'),

                    'ad_unit_id' => $ad_unit_id > 0 ? $ad_unit_id : $result

                ));

            }

        } catch (Exception $e) {

            error_log('Epic Membership: Exception in ajax_save_ad_unit: ' . $e->getMessage());

            error_log('Epic Membership: Exception trace: ' . $e->getTraceAsString());

            wp_send_json_error(__('An unexpected error occurred. Please check the server logs.', 'epic-membership'));

        }

    }

    

    /**

     * AJAX: Delete ad unit

     */

    public function ajax_delete_ad_unit() {

        check_ajax_referer('epic_membership_ad_units', 'nonce');

        

        if (!current_user_can('manage_options')) {

            wp_die(__('Insufficient permissions.', 'epic-membership'));

        }

        

        $ad_unit_id = intval(isset($_POST['ad_unit_id']) ? $_POST['ad_unit_id'] : 0);

        

        if ($ad_unit_id <= 0) {

            wp_send_json_error(__('Invalid ad unit ID.', 'epic-membership'));

        }

        

        $result = $this->ad_unit_manager->delete_ad_unit($ad_unit_id);

        

        if (is_wp_error($result)) {

            wp_send_json_error($result->get_error_message());

        } else {

            wp_send_json_success(__('Ad unit deleted successfully.', 'epic-membership'));

        }

    }

    

    /**

     * AJAX: Toggle ad unit status

     */

    public function ajax_toggle_ad_unit() {

        check_ajax_referer('epic_membership_ad_units', 'nonce');

        

        if (!current_user_can('manage_options')) {

            wp_die(__('Insufficient permissions.', 'epic-membership'));

        }

        

        $ad_unit_id = intval(isset($_POST['ad_unit_id']) ? $_POST['ad_unit_id'] : 0);

        

        if ($ad_unit_id <= 0) {

            wp_send_json_error(__('Invalid ad unit ID.', 'epic-membership'));

        }

        

        $result = $this->ad_unit_manager->toggle_ad_unit_status($ad_unit_id);

        

        if (is_wp_error($result)) {

            wp_send_json_error($result->get_error_message());

        } else {

            wp_send_json_success(array(

                'message' => __('Ad unit status updated.', 'epic-membership'),

                'new_status' => $result

            ));

        }

    }

    

    /**

     * AJAX: Reorder ad units

     */

    public function ajax_reorder_ad_units() {

        check_ajax_referer('epic_membership_ad_units', 'nonce');

        

        if (!current_user_can('manage_options')) {

            wp_die(__('Insufficient permissions.', 'epic-membership'));

        }

        

        $ordered_ids = isset($_POST['ordered_ids']) ? $_POST['ordered_ids'] : array();

        

        if (empty($ordered_ids) || !is_array($ordered_ids)) {

            wp_send_json_error(__('Invalid order data.', 'epic-membership'));

        }

        

        $result = $this->ad_unit_manager->update_ad_units_order($ordered_ids);

        

        if ($result) {

            wp_send_json_success(__('Ad units reordered successfully.', 'epic-membership'));

        } else {

            wp_send_json_error(__('Failed to reorder ad units.', 'epic-membership'));

        }

    }

}

