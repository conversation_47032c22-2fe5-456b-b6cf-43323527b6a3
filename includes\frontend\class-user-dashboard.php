<?php

/**

 * User Dashboard Class for Epic Membership Plugin

 * Handles user-facing membership dashboard and profile integration

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_User_Dashboard {

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Timezone handler instance

     */

    private $timezone_handler;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->timezone_handler = new Epic_Membership_Timezone_Handler();

        $this->init_hooks();

    }



    /**

     * Get tier badge CSS classes based on tier properties

     */

    private function get_tier_badge_classes($tier) {

        $classes = array('epic-membership-tier-badge');



        // Add slug-based class for legacy support

        $classes[] = 'epic-membership-tier-' . $tier->slug;



        // Add level-based class

        $classes[] = 'tier-level-' . $tier->level;



        // Add price-based class

        if ($tier->price > 0) {

            $classes[] = 'tier-price-paid';

        } else {

            $classes[] = 'tier-price-free';

        }



        return implode(' ', $classes);

    }



    /**

     * Format tier price with proper currency symbol

     */

    private function format_tier_price($tier) {

        $currency = $tier->currency ?? 'USD';

        $price = floatval($tier->price);



        switch ($currency) {

            case 'IDR':

                return 'Rp ' . number_format($price, 0, ',', '.');

            case 'USD':

                return '$' . number_format($price, 2);

            case 'EUR':

                return '€' . number_format($price, 2);

            case 'GBP':

                return '£' . number_format($price, 2);

            default:

                return $currency . ' ' . number_format($price, 2);

        }

    }



    /**

     * Format payment amount with proper currency symbol

     */

    private function format_payment_amount($amount, $currency) {

        $price = floatval($amount);



        switch ($currency) {

            case 'IDR':

                return 'Rp ' . number_format($price, 0, ',', '.');

            case 'USD':

                return '$' . number_format($price, 2);

            case 'EUR':

                return '€' . number_format($price, 2);

            case 'GBP':

                return '£' . number_format($price, 2);

            default:

                return $currency . ' ' . number_format($price, 2);

        }

    }



    /**

     * Initialize hooks

     */

    private function init_hooks() {

        // Shortcode for membership dashboard

        add_shortcode('epic_membership_dashboard', array($this, 'dashboard_shortcode'));



        // AJAX handlers

        add_action('wp_ajax_epic_membership_get_status', array($this, 'ajax_get_membership_status'));

        add_action('wp_ajax_epic_membership_check_status', array($this, 'ajax_get_membership_status')); // Alias for compatibility

        add_action('wp_ajax_nopriv_epic_membership_check_status', array($this, 'ajax_get_membership_status')); // Non-logged in users

        add_action('wp_ajax_nopriv_epic_membership_get_status', array($this, 'ajax_get_membership_status')); // Non-logged in users

        add_action('wp_ajax_epic_membership_create_status_page', array($this, 'ajax_create_status_page'));

        add_action('wp_ajax_epic_membership_get_dashboard_content', array($this, 'ajax_get_dashboard_content'));

        add_action('wp_ajax_nopriv_epic_membership_get_dashboard_content', array($this, 'ajax_get_dashboard_content'));



        // Payment notification handlers

        add_action('wp_ajax_epic_membership_get_tier_info', array($this, 'ajax_get_tier_info'));

        add_action('wp_ajax_nopriv_epic_membership_get_tier_info', array($this, 'ajax_get_tier_info'));



        // Free tier activation handler

        add_action('wp_ajax_epic_membership_activate_free_tier', array($this, 'ajax_activate_free_tier'));



        // Add dashboard to user profile (if enabled)

        add_action('show_user_profile', array($this, 'show_user_dashboard_section'));

        add_action('edit_user_profile', array($this, 'show_user_dashboard_section'));



        // Create dedicated membership status page

        add_action('init', array($this, 'create_membership_status_page'));

        add_action('template_redirect', array($this, 'handle_membership_status_page'));

        add_action('init', array($this, 'handle_payment_notifications'));



        // Add admin hooks for page management

        add_action('admin_init', array($this, 'register_dashboard_settings'));

        add_filter('display_post_states', array($this, 'add_membership_page_state'), 10, 2);



        // Add membership status to user menu - DISABLED

        // add_filter('wp_nav_menu_items', array($this, 'add_membership_menu_item'), 10, 2);

        // add_filter('wp_nav_menu_objects', array($this, 'modify_membership_menu_item'), 10, 2);



        // Make page available in nav menu admin

        add_filter('nav_menu_meta_box_object', array($this, 'add_membership_page_to_nav_menu'), 10, 1);



        // Add security headers

        add_action('send_headers', array($this, 'add_security_headers'));



        // Enqueue frontend assets when shortcode is used

        add_action('wp_enqueue_scripts', array($this, 'maybe_enqueue_frontend_assets'));



        // Ensure shortcodes are processed on membership pages

        add_filter('the_content', array($this, 'process_membership_page_content'), 10);



        // Override template for membership status page

        add_filter('page_template', array($this, 'membership_page_template'));



        // Force content display on membership page

        add_action('wp', array($this, 'force_membership_page_content'));

    }

    

    /**

     * Dashboard shortcode

     */

    public function dashboard_shortcode($atts) {

        // Ensure frontend assets are loaded when shortcode is called

        $this->enqueue_frontend_assets();



        $atts = shortcode_atts(array(

            'show_upgrade' => 'true',

            'show_history' => 'false',

            'show_stats' => 'true'

        ), $atts);



        if (!is_user_logged_in()) {

            return $this->get_login_message();

        }



        $user_id = get_current_user_id();



        // Get membership data with error handling

        try {

            $membership = $this->database->get_user_membership($user_id);

        } catch (Exception $e) {

            error_log('Epic Membership: Error getting user membership: ' . $e->getMessage());

            $membership = null;

        }



        ob_start();

        ?>

        <div class="epic-membership-dashboard">

            <div class="epic-membership-dashboard-header">

                <h3 class="epic-membership-dashboard-title">

                    <?php _e('My Membership', 'epic-membership'); ?>

                </h3>

            </div>



            <div class="epic-membership-dashboard-content">

                <?php echo $this->get_membership_status_card($membership); ?>



                <?php echo $this->get_membership_benefits($membership); ?>



                <?php if ($atts['show_stats'] === 'true'): ?>

                    <?php echo $this->get_membership_stats($user_id); ?>

                <?php endif; ?>



                <?php if ($atts['show_upgrade'] === 'true'): ?>

                    <?php echo $this->get_upgrade_section($membership); ?>

                <?php endif; ?>



                <?php if ($atts['show_history'] === 'true'): ?>

                    <?php echo $this->get_membership_history($user_id); ?>

                    <?php echo $this->get_payment_history($user_id); ?>

                <?php endif; ?>

            </div>

        </div>

        <?php

        return ob_get_clean();

    }



    /**

     * Maybe enqueue frontend assets if shortcode is present

     */

    public function maybe_enqueue_frontend_assets() {

        global $post;



        // Check if we're on a page/post that contains our shortcode

        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'epic_membership_dashboard')) {

            $this->enqueue_frontend_assets();

        }



        // Also check if this is our dedicated status page

        $page_id = get_option('epic_membership_status_page_id');

        if ($page_id && is_page($page_id)) {

            $this->enqueue_frontend_assets();

        }

    }



    /**

     * Enqueue frontend assets

     */

    public function enqueue_frontend_assets() {

        // Enqueue frontend styles

        wp_enqueue_style(

            'epic-membership-frontend',

            EPIC_MEMBERSHIP_PLUGIN_URL . 'assets/css/frontend.css',

            array(),

            EPIC_MEMBERSHIP_VERSION

        );



        // Enqueue frontend scripts

        wp_enqueue_script(

            'epic-membership-frontend',

            EPIC_MEMBERSHIP_PLUGIN_URL . 'assets/js/frontend.js',

            array('jquery'),

            EPIC_MEMBERSHIP_VERSION,

            true

        );



        // Note: Script localization is now handled in the main plugin file to avoid conflicts

    }



    /**

     * Process membership page content to ensure shortcodes work

     */

    public function process_membership_page_content($content) {

        global $post;



        // Check if this is our membership status page

        $page_id = get_option('epic_membership_status_page_id');

        if ($page_id && is_page($page_id) && $post && $post->ID == $page_id) {

            // Ensure our assets are loaded

            $this->enqueue_frontend_assets();



            // If content is empty or just whitespace, use the post content

            if (empty(trim($content))) {

                $content = $post->post_content;

            }



            // Ensure shortcodes are processed

            $content = do_shortcode($content);

        }



        return $content;

    }



    /**

     * Get login message for non-logged-in users

     */

    private function get_login_message() {

        ob_start();

        ?>

        <div class="epic-membership-login-required">

            <div class="epic-membership-lock-icon">🔒</div>

            <div class="epic-membership-message">

                <?php _e('Please log in to view your membership dashboard.', 'epic-membership'); ?>

            </div>

            <a href="<?php echo wp_login_url(get_permalink()); ?>" class="epic-membership-upgrade-button">

                <?php _e('Log In', 'epic-membership'); ?>

            </a>

        </div>

        <?php

        return ob_get_clean();

    }

    

    /**

     * Get membership status card

     */

    private function get_membership_status_card($membership) {

        ob_start();



        // Calculate status information

        $status_info = $this->get_membership_status_info($membership);



        // Fallback if status info is not available

        if (!$status_info) {

            $status_info = array(

                'status' => 'inactive',

                'status_class' => 'status-inactive',

                'status_icon' => '❌',

                'status_text' => __('No Active Membership', 'epic-membership')

            );

        }

        ?>

        <div class="epic-membership-status-card <?php echo esc_attr($status_info['status_class']); ?>">

            <div class="epic-membership-status-header">

                <div class="epic-membership-status-title">

                    <?php _e('Membership Status', 'epic-membership'); ?>

                </div>

                <div class="epic-membership-status-indicator">

                    <span class="status-icon <?php echo esc_attr($status_info['status']); ?>">

                        <?php echo $status_info['status_icon']; ?>

                    </span>

                    <span class="status-text <?php echo esc_attr($status_info['status']); ?>">

                        <?php echo esc_html($status_info['status_text']); ?>

                    </span>

                </div>

            </div>



            <?php if ($membership): ?>

                <div class="epic-membership-status-grid">

                    <!-- Tier Information -->

                    <div class="epic-membership-status-item">

                        <div class="status-item-icon">🏆</div>

                        <div class="status-item-content">

                            <span class="status-item-label"><?php _e('Current Tier', 'epic-membership'); ?></span>

                            <span class="status-item-value">

                                <?php

                                $tier_obj = (object) array(

                                    'slug' => $membership->tier_slug,

                                    'level' => $membership->tier_level,

                                    'price' => $membership->tier_price ?? 0

                                );

                                ?>

                                <span class="<?php echo esc_attr($this->get_tier_badge_classes($tier_obj)); ?>">

                                    <?php echo esc_html($membership->tier_name); ?>

                                </span>

                            </span>

                        </div>

                    </div>



                    <!-- Start Date -->

                    <div class="epic-membership-status-item">

                        <div class="status-item-icon">📅</div>

                        <div class="status-item-content">

                            <span class="status-item-label"><?php _e('Member Since', 'epic-membership'); ?></span>

                            <span class="status-item-value" data-field="start_date">

                                <?php echo esc_html($this->timezone_handler->format_datetime_for_user($membership->start_date, get_option('date_format'))); ?>

                            </span>

                        </div>

                    </div>



                    <?php if ($membership->end_date): ?>

                        <!-- End Date -->

                        <div class="epic-membership-status-item">

                            <div class="status-item-icon">⏰</div>

                            <div class="status-item-content">

                                <span class="status-item-label"><?php _e('Expires On', 'epic-membership'); ?></span>

                                <span class="status-item-value <?php echo esc_attr($status_info['expiry_class']); ?>" data-field="end_date">

                                    <?php echo esc_html($this->timezone_handler->format_datetime_for_user($membership->end_date, get_option('date_format'))); ?>

                                </span>

                            </div>

                        </div>



                        <?php if ($status_info['days_remaining'] !== null): ?>

                            <!-- Days Remaining -->

                            <div class="epic-membership-status-item">

                                <div class="status-item-icon">⏳</div>

                                <div class="status-item-content">

                                    <span class="status-item-label"><?php _e('Time Remaining', 'epic-membership'); ?></span>

                                    <span class="status-item-value <?php echo esc_attr($status_info['remaining_class']); ?>" data-field="time_remaining">

                                        <?php echo esc_html($status_info['time_remaining_text']); ?>

                                    </span>

                                </div>

                            </div>

                        <?php endif; ?>

                    <?php else: ?>

                        <!-- Lifetime Membership -->

                        <div class="epic-membership-status-item">

                            <div class="status-item-icon">♾️</div>

                            <div class="status-item-content">

                                <span class="status-item-label"><?php _e('Duration', 'epic-membership'); ?></span>

                                <span class="status-item-value lifetime">

                                    <?php _e('Lifetime Access', 'epic-membership'); ?>

                                </span>

                            </div>

                        </div>

                    <?php endif; ?>



                    <!-- Payment Status -->

                    <div class="epic-membership-status-item">

                        <div class="status-item-icon">💳</div>

                        <div class="status-item-content">

                            <span class="status-item-label"><?php _e('Payment Status', 'epic-membership'); ?></span>

                            <span class="status-item-value payment-<?php echo esc_attr($membership->payment_status); ?>">

                                <?php echo esc_html($this->get_payment_status_text($membership->payment_status)); ?>

                            </span>

                        </div>

                    </div>



                    <!-- Auto Renewal -->

                    <div class="epic-membership-status-item">

                        <div class="status-item-icon"><?php echo $membership->auto_renew ? '🔄' : '⏹️'; ?></div>

                        <div class="status-item-content">

                            <span class="status-item-label"><?php _e('Auto Renewal', 'epic-membership'); ?></span>

                            <span class="status-item-value auto-renew-<?php echo $membership->auto_renew ? 'enabled' : 'disabled'; ?>">

                                <?php echo $membership->auto_renew ? __('Enabled', 'epic-membership') : __('Disabled', 'epic-membership'); ?>

                            </span>

                        </div>

                    </div>

                </div>



                <?php if ($status_info['show_warning']): ?>

                    <div class="epic-membership-status-warning">

                        <div class="warning-icon">⚠️</div>

                        <div class="warning-message">

                            <?php echo wp_kses_post($status_info['warning_message']); ?>

                        </div>

                    </div>

                <?php endif; ?>



            <?php else: ?>

                <div class="epic-membership-no-membership">

                    <div class="no-membership-icon">🔒</div>

                    <div class="no-membership-message">

                        <h4><?php _e('No Active Membership', 'epic-membership'); ?></h4>

                        <p><?php _e('You currently don\'t have an active membership. Upgrade to access premium content and features.', 'epic-membership'); ?></p>

                    </div>

                </div>

            <?php endif; ?>



            <div class="epic-membership-status-actions">

                <button type="button" class="epic-membership-refresh-status button-secondary">

                    <span class="refresh-icon">🔄</span>

                    <?php _e('Refresh Status', 'epic-membership'); ?>

                </button>

                <?php if ($membership && $membership->end_date && $status_info['days_remaining'] !== null && $status_info['days_remaining'] <= 7): ?>

                    <a href="#" class="epic-membership-renew-button button-primary">

                        <?php _e('Renew Membership', 'epic-membership'); ?>

                    </a>

                <?php endif; ?>

            </div>

        </div>

        <?php

        return ob_get_clean();

    }



    /**

     * Get tier by ID (without admin dependency)

     */

    private function get_tier_by_id($tier_id) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');



        return $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $table_name WHERE id = %d",

            $tier_id

        ));

    }



    /**

     * Get all tiers (without admin dependency)

     */

    private function get_all_tiers($active_only = false) {

        global $wpdb;

        $table_name = $this->database->get_table('tiers');



        $where_clause = $active_only ? 'WHERE is_active = 1' : '';



        return $wpdb->get_results("SELECT * FROM $table_name $where_clause ORDER BY level ASC");

    }



    /**

     * Get available capabilities (simplified version)

     */

    private function get_available_capabilities() {

        return array(

            'read_free_content' => __('Access to free content', 'epic-membership'),

            'read_premium_content' => __('Access to premium content', 'epic-membership'),

            'read_vip_content' => __('Access to VIP content', 'epic-membership'),

            'ad_free_experience' => __('Ad-free browsing experience', 'epic-membership'),

            'priority_support' => __('Priority customer support', 'epic-membership'),

            'exclusive_content' => __('Access to exclusive content', 'epic-membership'),

            'early_access' => __('Early access to new features', 'epic-membership'),

            'download_content' => __('Download content for offline viewing', 'epic-membership')

        );

    }



    /**

     * Get membership benefits overview

     */

    private function get_membership_benefits($membership) {

        $all_tiers = $this->get_all_tiers(true);

        $available_capabilities = $this->get_available_capabilities();



        ob_start();

        ?>

        <div class="epic-membership-benefits-section">

            <div class="epic-membership-section-header">

                <h3 class="epic-membership-section-title">

                    <?php _e('Your Benefits & Features', 'epic-membership'); ?>

                </h3>

                <p class="epic-membership-section-description">

                    <?php _e('Here\'s what you have access to with your current membership:', 'epic-membership'); ?>

                </p>

            </div>



            <?php if ($membership): ?>

                <?php

                $current_capabilities = json_decode($membership->capabilities, true) ?: array();

                ?>

                <div class="epic-membership-current-benefits">

                    <div class="benefits-grid">

                        <?php foreach ($current_capabilities as $capability): ?>

                            <?php if (isset($available_capabilities[$capability])): ?>

                                <div class="benefit-item active">

                                    <div class="benefit-icon">

                                        <?php echo $this->get_capability_icon($capability); ?>

                                    </div>

                                    <div class="benefit-content">

                                        <h4 class="benefit-title">

                                            <?php echo esc_html($available_capabilities[$capability]); ?>

                                        </h4>

                                        <p class="benefit-description">

                                            <?php echo esc_html($this->get_capability_description($capability)); ?>

                                        </p>

                                    </div>

                                    <div class="benefit-status">

                                        <span class="status-badge included">✓ <?php _e('Included', 'epic-membership'); ?></span>

                                    </div>

                                </div>

                            <?php endif; ?>

                        <?php endforeach; ?>

                    </div>

                </div>



                <?php

                // Show tier comparison

                $higher_tiers = array();

                foreach ($all_tiers as $tier) {

                    if ($tier->level > $membership->tier_level) {

                        $higher_tiers[] = $tier;

                    }

                }

                ?>



                <?php if (!empty($higher_tiers)): ?>

                    <div class="epic-membership-tier-comparison">

                        <div class="comparison-header">

                            <h4><?php _e('Upgrade to Get More', 'epic-membership'); ?></h4>

                            <p><?php _e('See what additional benefits you could unlock:', 'epic-membership'); ?></p>

                        </div>



                        <div class="tier-comparison-grid">

                            <?php foreach ($higher_tiers as $tier): ?>

                                <?php

                                $tier_capabilities = json_decode($tier->capabilities, true) ?: array();

                                $additional_capabilities = array_diff($tier_capabilities, $current_capabilities);

                                ?>

                                <?php if (!empty($additional_capabilities)): ?>

                                    <div class="tier-comparison-card">

                                        <div class="tier-header">

                                            <span class="<?php echo esc_attr($this->get_tier_badge_classes($tier)); ?>">

                                                <?php echo esc_html($tier->name); ?>

                                            </span>

                                            <div class="tier-price">

                                                <?php if ($tier->price > 0): ?>

                                                    <?php echo $this->format_tier_price($tier); ?>

                                                    <?php if ($tier->duration_days): ?>

                                                        <span class="price-period">/ <?php printf(_n('%d day', '%d days', $tier->duration_days, 'epic-membership'), $tier->duration_days); ?></span>

                                                    <?php endif; ?>

                                                <?php else: ?>

                                                    <?php _e('Free', 'epic-membership'); ?>

                                                <?php endif; ?>

                                            </div>

                                        </div>



                                        <div class="additional-benefits">

                                            <h5><?php _e('Additional Benefits:', 'epic-membership'); ?></h5>

                                            <?php foreach ($additional_capabilities as $capability): ?>

                                                <?php if (isset($available_capabilities[$capability])): ?>

                                                    <div class="additional-benefit">

                                                        <span class="benefit-icon"><?php echo $this->get_capability_icon($capability); ?></span>

                                                        <span class="benefit-name"><?php echo esc_html($available_capabilities[$capability]); ?></span>

                                                    </div>

                                                <?php endif; ?>

                                            <?php endforeach; ?>

                                        </div>



                                        <div class="tier-action">

                                            <a href="#" class="epic-membership-upgrade-button" data-tier-id="<?php echo esc_attr($tier->id); ?>">

                                                <?php _e('Upgrade Now', 'epic-membership'); ?>

                                            </a>

                                        </div>

                                    </div>

                                <?php endif; ?>

                            <?php endforeach; ?>

                        </div>

                    </div>

                <?php endif; ?>



            <?php else: ?>

                <div class="epic-membership-no-benefits">

                    <div class="no-benefits-content">

                        <div class="no-benefits-icon">🔒</div>

                        <h4><?php _e('No Active Benefits', 'epic-membership'); ?></h4>

                        <p><?php _e('You don\'t currently have an active membership. Upgrade to unlock premium features and content.', 'epic-membership'); ?></p>

                    </div>



                    <div class="available-tiers">

                        <h4><?php _e('Available Membership Tiers', 'epic-membership'); ?></h4>

                        <div class="tier-options-grid">

                            <?php foreach ($all_tiers as $tier): ?>

                                    <div class="tier-option-card">

                                        <div class="tier-header">

                                            <span class="<?php echo esc_attr($this->get_tier_badge_classes($tier)); ?>">

                                                <?php echo esc_html($tier->name); ?>

                                            </span>

                                            <div class="tier-price">

                                                <?php if ($tier->price > 0): ?>

                                                    <?php echo $this->format_tier_price($tier); ?>

                                                    <?php if ($tier->duration_days): ?>

                                                        <span class="price-period">/ <?php printf(_n('%d day', '%d days', $tier->duration_days, 'epic-membership'), $tier->duration_days); ?></span>

                                                    <?php endif; ?>

                                                <?php else: ?>

                                                    <span class="free-tier"><?php _e('Free', 'epic-membership'); ?></span>

                                                <?php endif; ?>

                                            </div>

                                        </div>



                                        <div class="tier-benefits">

                                            <?php

                                            $tier_capabilities = json_decode($tier->capabilities, true) ?: array();

                                            foreach ($tier_capabilities as $capability):

                                                if (isset($available_capabilities[$capability])):

                                            ?>

                                                <div class="tier-benefit">

                                                    <span class="benefit-icon"><?php echo $this->get_capability_icon($capability); ?></span>

                                                    <span class="benefit-name"><?php echo esc_html($available_capabilities[$capability]); ?></span>

                                                </div>

                                            <?php endif; endforeach; ?>

                                        </div>





                                    </div>

                            <?php endforeach; ?>

                        </div>

                        <div class="tier-upgrade-notice">

                            <p><?php _e('To upgrade your membership, please scroll down to the "Upgrade Options" section below.', 'epic-membership'); ?></p>

                        </div>

                    </div>

                </div>

            <?php endif; ?>

        </div>

        <?php

        return ob_get_clean();

    }



    /**

     * Get membership statistics

     */

    private function get_membership_stats($user_id) {

        global $wpdb;

        

        // Get user's content access stats

        $content_access_table = $this->database->get_table('content_access');

        $access_logs_table = $this->database->get_table('access_logs');

        

        $stats = array(

            'total_protected_content' => $wpdb->get_var("SELECT COUNT(*) FROM $content_access_table WHERE access_type != 'public'"),

            'user_access_count' => $wpdb->get_var($wpdb->prepare(

                "SELECT COUNT(*) FROM $access_logs_table WHERE user_id = %d AND access_granted = 1",

                $user_id

            )),

            'user_blocked_count' => $wpdb->get_var($wpdb->prepare(

                "SELECT COUNT(*) FROM $access_logs_table WHERE user_id = %d AND access_granted = 0",

                $user_id

            ))

        );

        

        ob_start();

        ?>

        <div class="epic-membership-stats-section">

            <div class="epic-membership-status-title">

                <?php _e('Your Activity', 'epic-membership'); ?>

            </div>

            

            <div class="epic-membership-stats-grid">

                <div class="epic-membership-stat-item">

                    <div class="stat-number"><?php echo intval($stats['user_access_count']); ?></div>

                    <div class="stat-label"><?php _e('Content Accessed', 'epic-membership'); ?></div>

                </div>

                

                <div class="epic-membership-stat-item">

                    <div class="stat-number"><?php echo intval($stats['total_protected_content']); ?></div>

                    <div class="stat-label"><?php _e('Protected Content', 'epic-membership'); ?></div>

                </div>

            </div>

        </div>

        <?php

        return ob_get_clean();

    }

    

    /**

     * Get upgrade section

     */

    private function get_upgrade_section($membership) {

        $available_tiers = $this->get_all_tiers(true);

        

        $current_level = $membership ? $membership->tier_level : -1;

        $upgrade_tiers = array();

        

        foreach ($available_tiers as $tier) {

            if ($tier->level > $current_level) {

                $upgrade_tiers[] = $tier;

            }

        }

        

        if (empty($upgrade_tiers)) {

            return '';

        }

        

        ob_start();

        ?>

        <div class="epic-membership-upgrade-section">

            <h3 class="epic-membership-status-title">

                <?php _e('Upgrade Options', 'epic-membership'); ?>

            </h3>



            <div class="epic-membership-upgrade-options">

                <?php foreach ($upgrade_tiers as $tier): ?>

                    <div class="epic-membership-upgrade-option" data-tier-id="<?php echo esc_attr($tier->id); ?>" data-tier-slug="<?php echo esc_attr($tier->slug); ?>">

                        <div class="upgrade-tier-name">

                            <span class="<?php echo esc_attr($this->get_tier_badge_classes($tier)); ?>">

                                <?php echo esc_html($tier->name); ?>

                            </span>

                        </div>



                        <div class="upgrade-tier-description">

                            <?php if (!empty($tier->description)): ?>

                                <?php echo wp_kses_post($tier->description); ?>

                            <?php else: ?>

                                <p><?php printf(__('Upgrade to %s membership to unlock additional features and benefits.', 'epic-membership'), esc_html($tier->name)); ?></p>

                            <?php endif; ?>

                        </div>



                        <div class="upgrade-tier-price">

                            <?php if ($tier->price > 0): ?>

                                <span class="price-amount"><?php echo $this->format_tier_price($tier); ?></span>

                                <?php if ($tier->duration_days): ?>

                                    <small class="price-duration">/ <?php printf(_n('%d day', '%d days', $tier->duration_days, 'epic-membership'), $tier->duration_days); ?></small>

                                <?php endif; ?>

                            <?php else: ?>

                                <span class="price-amount"><?php _e('Free', 'epic-membership'); ?></span>

                            <?php endif; ?>

                        </div>

                        

                        <div class="upgrade-tier-action">

                            <?php if ($tier->price > 0): ?>

                                <?php

                                $paypal_enabled = get_option('epic_membership_paypal_enabled', false);

                                $kofi_enabled = get_option('epic_membership_kofi_enabled', false);

                                $trakteer_enabled = get_option('epic_membership_trakteer_enabled', false);

                                ?>



                                <?php if ($paypal_enabled || $kofi_enabled || $trakteer_enabled): ?>

                                    <div class="epic-membership-payment-options">

                                        <?php if (($paypal_enabled + $kofi_enabled + $trakteer_enabled) > 1): ?>

                                            <!-- Both payment methods available - show unified selection -->

                                            <div class="epic-membership-payment-methods-info">

                                                <p class="payment-methods-text"><?php _e('Choose your preferred payment method:', 'epic-membership'); ?></p>

                                            </div>

                                        <?php endif; ?>



                                        <?php if ($paypal_enabled): ?>

                                            <button class="epic-membership-upgrade-button epic-membership-paypal-button"

                                                    data-tier-id="<?php echo esc_attr($tier->id); ?>"

                                                    data-tier-name="<?php echo esc_attr($tier->name); ?>"

                                                    data-tier-price="<?php echo esc_attr($tier->price); ?>">

                                                <span class="button-icon">💳</span>

                                                <?php _e('Pay with PayPal', 'epic-membership'); ?>

                                            </button>

                                            <div class="epic-membership-paypal-container" data-tier-id="<?php echo esc_attr($tier->id); ?>" style="display: none; margin-top: 10px;"></div>

                                        <?php endif; ?>



                                        <?php if ($kofi_enabled): ?>

                                            <button class="epic-membership-kofi-button epic-membership-button"

                                                    data-tier-id="<?php echo esc_attr($tier->id); ?>"

                                                    data-tier-name="<?php echo esc_attr($tier->name); ?>"

                                                    data-tier-price="<?php echo esc_attr($tier->price); ?>">

                                                <span class="button-icon">☕</span>

                                                <?php _e('Support via Ko-fi', 'epic-membership'); ?>

                                            </button>

                                        <?php endif; ?>



                                        <?php if ($trakteer_enabled): ?>

                                            <button class="epic-membership-trakteer-button epic-membership-button"

                                                    data-tier-id="<?php echo esc_attr($tier->id); ?>"

                                                    data-tier-name="<?php echo esc_attr($tier->name); ?>"

                                                    data-tier-price="<?php echo esc_attr($tier->price); ?>"

                                                    data-currency="<?php echo esc_attr($tier->currency ?? 'IDR'); ?>">

                                                <span class="button-icon">🥤</span>

                                                <?php _e('Dukung via Trakteer', 'epic-membership'); ?>

                                            </button>

                                        <?php endif; ?>



                                        <?php if (($paypal_enabled + $kofi_enabled + $trakteer_enabled) > 1): ?>

                                            <div class="epic-membership-payment-methods-note">

                                                <p class="payment-note"><?php _e('All payment methods will automatically activate your membership upon successful payment.', 'epic-membership'); ?></p>

                                            </div>

                                        <?php endif; ?>



                                        <?php

                                        // Allow other payment gateways to add their buttons

                                        do_action('epic_membership_tier_payment_buttons', $tier->id, $tier);

                                        ?>

                                    </div>

                                <?php else: ?>

                                    <button type="button" class="epic-membership-upgrade-button"

                                            data-tier-id="<?php echo esc_attr($tier->id); ?>"

                                            data-tier-name="<?php echo esc_attr($tier->name); ?>"

                                            data-tier-price="<?php echo esc_attr($tier->price); ?>">

                                        <span class="button-icon">⭐</span>

                                        <?php _e('Upgrade Now', 'epic-membership'); ?>

                                    </button>

                                <?php endif; ?>

                            <?php else: ?>

                                <button type="button" class="epic-membership-upgrade-button epic-membership-free-tier"

                                        data-tier-id="<?php echo esc_attr($tier->id); ?>"

                                        data-tier-name="<?php echo esc_attr($tier->name); ?>">

                                    <span class="button-icon">🎯</span>

                                    <?php _e('Activate Free Tier', 'epic-membership'); ?>

                                </button>

                            <?php endif; ?>

                        </div>

                    </div>

                <?php endforeach; ?>

            </div>

        </div>

        <?php

        return ob_get_clean();

    }

    

    /**

     * Get membership history

     */

    private function get_membership_history($user_id) {

        global $wpdb;

        

        $memberships_table = $this->database->get_table('user_memberships');

        $tiers_table = $this->database->get_table('tiers');

        

        $history = $wpdb->get_results($wpdb->prepare("

            SELECT m.*, t.name as tier_name, t.slug as tier_slug, t.level as tier_level, t.price as tier_price

            FROM $memberships_table m

            LEFT JOIN $tiers_table t ON m.tier_id = t.id

            WHERE m.user_id = %d

            ORDER BY m.created_at DESC

            LIMIT 10

        ", $user_id));

        

        if (empty($history)) {

            return '';

        }

        

        ob_start();

        ?>

        <div class="epic-membership-history-section">

            <div class="epic-membership-status-title">

                <?php _e('Membership History', 'epic-membership'); ?>

            </div>

            

            <div class="epic-membership-history-list">

                <?php foreach ($history as $record): ?>

                    <div class="epic-membership-history-item <?php echo $record->is_active ? 'active' : 'inactive'; ?>">

                        <div class="history-tier">

                            <?php

                            $tier_obj = (object) array(

                                'slug' => $record->tier_slug,

                                'level' => $record->tier_level,

                                'price' => $record->tier_price ?? 0

                            );

                            ?>

                            <span class="<?php echo esc_attr($this->get_tier_badge_classes($tier_obj)); ?>">

                                <?php echo esc_html($record->tier_name); ?>

                            </span>

                        </div>

                        

                        <div class="history-dates">

                            <div class="history-start">

                                <?php echo esc_html($this->timezone_handler->format_datetime_for_user($record->start_date)); ?>

                            </div>

                            <?php if ($record->end_date): ?>

                                <div class="history-end">

                                    - <?php echo esc_html($this->timezone_handler->format_datetime_for_user($record->end_date)); ?>

                                </div>

                            <?php endif; ?>

                        </div>

                        

                        <div class="history-status">

                            <?php if ($record->is_active): ?>

                                <span class="status-active"><?php _e('Active', 'epic-membership'); ?></span>

                            <?php else: ?>

                                <span class="status-inactive"><?php _e('Inactive', 'epic-membership'); ?></span>

                            <?php endif; ?>

                        </div>

                    </div>

                <?php endforeach; ?>

            </div>

        </div>

        <?php

        return ob_get_clean();

    }



    /**

     * Get payment history

     */

    private function get_payment_history($user_id) {

        global $wpdb;



        $transactions_table = $this->database->get_table('payment_transactions');

        $tiers_table = $this->database->get_table('tiers');



        if (!$transactions_table) {

            return ''; // Payment transactions table not available

        }



        $payments = $wpdb->get_results($wpdb->prepare("

            SELECT p.*, t.name as tier_name, t.slug as tier_slug

            FROM $transactions_table p

            LEFT JOIN $tiers_table t ON p.tier_id = t.id

            WHERE p.user_id = %d

            ORDER BY p.created_at DESC

            LIMIT 10

        ", $user_id));



        if (empty($payments)) {

            return '';

        }



        ob_start();

        ?>

        <div class="epic-membership-payment-history-section">

            <div class="epic-membership-status-title">

                <?php _e('Payment History', 'epic-membership'); ?>

            </div>



            <div class="epic-membership-payment-history-list">

                <?php foreach ($payments as $payment): ?>

                    <div class="epic-membership-payment-history-item <?php echo esc_attr($payment->payment_status); ?>">

                        <div class="payment-tier">

                            <span class="epic-membership-tier-badge epic-membership-tier-<?php echo esc_attr($payment->tier_slug); ?>">

                                <?php echo esc_html($payment->tier_name); ?>

                            </span>

                        </div>



                        <div class="payment-details">

                            <div class="payment-amount">

                                <?php echo $this->format_payment_amount($payment->amount, $payment->currency); ?>

                            </div>

                            <div class="payment-date">

                                <?php echo esc_html($this->timezone_handler->format_datetime_for_user($payment->created_at)); ?>

                            </div>

                            <div class="payment-method">

                                <?php echo esc_html(ucfirst($payment->payment_gateway)); ?>

                            </div>

                        </div>



                        <div class="payment-status">

                            <span class="status-<?php echo esc_attr($payment->payment_status); ?>">

                                <?php echo esc_html($this->get_payment_status_display($payment->payment_status)); ?>

                            </span>

                        </div>



                        <div class="payment-transaction">

                            <small><?php _e('Transaction ID:', 'epic-membership'); ?> <?php echo esc_html($payment->transaction_id); ?></small>

                        </div>

                    </div>

                <?php endforeach; ?>

            </div>

        </div>

        <?php

        return ob_get_clean();

    }



    /**

     * Get payment status display text

     */

    private function get_payment_status_display($status) {

        $statuses = array(

            'pending' => __('Pending', 'epic-membership'),

            'completed' => __('Completed', 'epic-membership'),

            'failed' => __('Failed', 'epic-membership'),

            'cancelled' => __('Cancelled', 'epic-membership'),

            'refunded' => __('Refunded', 'epic-membership'),

            'processing' => __('Processing', 'epic-membership')

        );



        return isset($statuses[$status]) ? $statuses[$status] : ucfirst($status);

    }



    /**

     * Get comprehensive membership status information

     */

    private function get_membership_status_info($membership) {

        if (!$membership) {

            return array(

                'status' => 'none',

                'status_class' => 'no-membership',

                'status_text' => __('No Membership', 'epic-membership'),

                'status_icon' => '🔒',

                'days_remaining' => null,

                'time_remaining_text' => '',

                'expiry_class' => '',

                'remaining_class' => '',

                'show_warning' => false,

                'warning_message' => ''

            );

        }



        $current_time = time();

        $end_time = $membership->end_date ? strtotime($membership->end_date) : null;

        $is_expired = $end_time && $end_time < $current_time;

        $days_remaining = $end_time ? floor(($end_time - $current_time) / (24 * 60 * 60)) : null;



        // Determine status

        if (!$membership->is_active) {

            $status = 'inactive';

            $status_text = __('Inactive', 'epic-membership');

            $status_icon = '⏸️';

            $status_class = 'membership-inactive';

        } elseif ($is_expired) {

            $status = 'expired';

            $status_text = __('Expired', 'epic-membership');

            $status_icon = '❌';

            $status_class = 'membership-expired';

        } elseif ($membership->payment_status === 'pending') {

            $status = 'pending';

            $status_text = __('Pending Payment', 'epic-membership');

            $status_icon = '⏳';

            $status_class = 'membership-pending';

        } elseif ($days_remaining !== null && $days_remaining <= 3) {

            $status = 'expiring';

            $status_text = __('Expiring Soon', 'epic-membership');

            $status_icon = '⚠️';

            $status_class = 'membership-expiring';

        } elseif ($days_remaining !== null && $days_remaining <= 7) {

            $status = 'warning';

            $status_text = __('Active', 'epic-membership');

            $status_icon = '⚡';

            $status_class = 'membership-warning';

        } else {

            $status = 'active';

            $status_text = __('Active', 'epic-membership');

            $status_icon = '✅';

            $status_class = 'membership-active';

        }



        // Time remaining text

        $time_remaining_text = $this->get_detailed_time_remaining($end_time);



        // Expiry and remaining classes

        $expiry_class = '';

        $remaining_class = '';

        if ($days_remaining !== null) {

            if ($days_remaining <= 1) {

                $expiry_class = 'expires-today';

                $remaining_class = 'critical';

            } elseif ($days_remaining <= 3) {

                $expiry_class = 'expires-soon';

                $remaining_class = 'urgent';

            } elseif ($days_remaining <= 7) {

                $expiry_class = 'expires-week';

                $remaining_class = 'warning';

            }

        }



        // Warning messages

        $show_warning = false;

        $warning_message = '';



        if ($status === 'expired') {

            $show_warning = true;

            $warning_message = __('Your membership has expired. Renew now to continue accessing premium content.', 'epic-membership');

        } elseif ($status === 'expiring' && $days_remaining <= 3) {

            $show_warning = true;

            $warning_message = sprintf(

                _n(

                    'Your membership expires in %d day. Renew now to avoid interruption.',

                    'Your membership expires in %d days. Renew now to avoid interruption.',

                    $days_remaining,

                    'epic-membership'

                ),

                $days_remaining

            );

        } elseif ($membership->payment_status === 'pending') {

            $show_warning = true;

            $warning_message = __('Your payment is pending. Please complete your payment to activate your membership.', 'epic-membership');

        }



        return array(

            'status' => $status,

            'status_class' => $status_class,

            'status_text' => $status_text,

            'status_icon' => $status_icon,

            'days_remaining' => $days_remaining,

            'time_remaining_text' => $time_remaining_text,

            'expiry_class' => $expiry_class,

            'remaining_class' => $remaining_class,

            'show_warning' => $show_warning,

            'warning_message' => $warning_message

        );

    }



    /**

     * Get detailed time remaining text

     */

    private function get_detailed_time_remaining($end_time) {

        if (!$end_time) {

            return __('Lifetime', 'epic-membership');

        }



        $current_time = time();

        $diff = $end_time - $current_time;



        if ($diff <= 0) {

            return __('Expired', 'epic-membership');

        }



        $days = floor($diff / (24 * 60 * 60));

        $hours = floor(($diff % (24 * 60 * 60)) / (60 * 60));

        $minutes = floor(($diff % (60 * 60)) / 60);



        if ($days > 30) {

            $months = floor($days / 30);

            $remaining_days = $days % 30;

            if ($remaining_days > 0) {

                return sprintf(

                    _n('%d month, %d day', '%d months, %d days', $months, 'epic-membership'),

                    $months, $remaining_days

                );

            } else {

                return sprintf(_n('%d month', '%d months', $months, 'epic-membership'), $months);

            }

        } elseif ($days > 0) {

            if ($hours > 0 && $days <= 7) {

                return sprintf(

                    _n('%d day, %d hour', '%d days, %d hours', $days, 'epic-membership'),

                    $days, $hours

                );

            } else {

                return sprintf(_n('%d day', '%d days', $days, 'epic-membership'), $days);

            }

        } elseif ($hours > 0) {

            return sprintf(_n('%d hour', '%d hours', $hours, 'epic-membership'), $hours);

        } else {

            return sprintf(_n('%d minute', '%d minutes', $minutes, 'epic-membership'), $minutes);

        }

    }



    /**

     * Get payment status text

     */

    private function get_payment_status_text($payment_status) {

        $statuses = array(

            'pending' => __('Pending', 'epic-membership'),

            'completed' => __('Completed', 'epic-membership'),

            'failed' => __('Failed', 'epic-membership'),

            'cancelled' => __('Cancelled', 'epic-membership'),

            'refunded' => __('Refunded', 'epic-membership')

        );



        return isset($statuses[$payment_status]) ? $statuses[$payment_status] : ucfirst($payment_status);

    }



    /**

     * Get capability icon

     */

    private function get_capability_icon($capability) {

        $icons = array(

            'read_free_content' => '📖',

            'read_premium_content' => '⭐',

            'read_vip_content' => '💎',

            'ad_free_experience' => '🚫',

            'priority_support' => '🎧',

            'early_access' => '⚡',

            'exclusive_content' => '🔥',

            'download_content' => '⬇️'

        );



        return isset($icons[$capability]) ? $icons[$capability] : '✨';

    }



    /**

     * Get capability description

     */

    private function get_capability_description($capability) {

        $descriptions = array(

            'read_free_content' => __('Access to all free content and articles', 'epic-membership'),

            'read_premium_content' => __('Access to premium articles and exclusive content', 'epic-membership'),

            'read_vip_content' => __('Access to VIP-only content and special features', 'epic-membership'),

            'ad_free_experience' => __('Browse the site without any advertisements', 'epic-membership'),

            'priority_support' => __('Get priority customer support and faster response times', 'epic-membership'),

            'early_access' => __('Get early access to new content before other members', 'epic-membership'),

            'exclusive_content' => __('Access to members-only exclusive content and resources', 'epic-membership'),

            'download_content' => __('Download content for offline reading and reference', 'epic-membership')

        );



        return isset($descriptions[$capability]) ? $descriptions[$capability] : __('Special membership benefit', 'epic-membership');

    }



    /**

     * Get time remaining text (legacy method for backward compatibility)

     */

    private function get_time_remaining_text($end_date) {

        return $this->get_detailed_time_remaining(strtotime($end_date));

    }

    

    /**

     * Show user dashboard section in profile

     */

    public function show_user_dashboard_section($user) {

        if ($user->ID !== get_current_user_id() && !current_user_can('manage_options')) {

            return;

        }

        

        echo '<h3>' . __('Membership Dashboard', 'epic-membership') . '</h3>';

        echo $this->dashboard_shortcode(array());

    }

    

    /**

     * AJAX handler for getting membership status

     */

    public function ajax_get_membership_status() {

        // Security checks

        if (!$this->verify_ajax_request()) {

            wp_send_json_error(__('Security check failed', 'epic-membership'));

        }



        if (!is_user_logged_in()) {

            wp_send_json_error(__('Authentication required', 'epic-membership'));

        }



        $user_id = get_current_user_id();



        // Rate limiting check

        if (!$this->check_rate_limit($user_id, 'status_refresh')) {

            wp_send_json_error(__('Too many requests. Please wait before refreshing again.', 'epic-membership'));

        }



        // Get cached membership data first

        $cache_key = 'epic_membership_status_' . $user_id;

        $cached_data = wp_cache_get($cache_key, 'epic_membership');



        if ($cached_data !== false && !isset($_POST['force_refresh'])) {

            wp_send_json_success($cached_data);

        }



        // Get fresh membership data

        $membership = $this->database->get_user_membership($user_id);

        $status_info = $this->get_membership_status_info($membership);



        $data = array();



        if ($membership) {

            $data = array(

                'start_date' => $this->timezone_handler->format_datetime_for_user($membership->start_date, get_option('date_format')),

                'end_date' => $membership->end_date ? $this->timezone_handler->format_datetime_for_user($membership->end_date, get_option('date_format')) : null,

                'time_remaining' => $status_info['time_remaining_text'],

                'status' => $status_info['status'],

                'status_text' => $status_info['status_text'],

                'days_remaining' => $status_info['days_remaining'],

                'payment_status' => $this->get_payment_status_text($membership->payment_status),

                'auto_renew' => $membership->auto_renew ? __('Enabled', 'epic-membership') : __('Disabled', 'epic-membership'),

                'tier_name' => $membership->tier_name,

                'tier_slug' => $membership->tier_slug

            );

        } else {

            $data = array(

                'status' => 'none',

                'status_text' => __('No Membership', 'epic-membership'),

                'message' => __('You do not have an active membership.', 'epic-membership')

            );

        }



        // Cache the data for 5 minutes

        wp_cache_set($cache_key, $data, 'epic_membership', 300);



        // Log the status check for analytics

        $this->log_status_check($user_id);



        wp_send_json_success($data);

    }



    /**

     * AJAX handler for creating status page

     */

    public function ajax_create_status_page() {

        // Security checks

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'epic_membership_admin_nonce')) {

            wp_send_json_error(__('Security check failed', 'epic-membership'));

        }



        if (!current_user_can('manage_options')) {

            wp_send_json_error(__('Insufficient permissions', 'epic-membership'));

        }



        $result = $this->create_status_page_manually();



        if ($result['success']) {

            wp_send_json_success($result['message']);

        } else {

            wp_send_json_error($result['message']);

        }

    }



    /**

     * AJAX handler for getting dashboard content

     */

    public function ajax_get_dashboard_content() {

        if (!is_user_logged_in()) {

            wp_send_json_error(__('Please log in to view your membership dashboard.', 'epic-membership'));

        }



        $content = $this->dashboard_shortcode(array());

        wp_send_json_success($content);

    }



    /**

     * AJAX handler for getting tier information

     */

    public function ajax_get_tier_info() {

        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'epic_membership_nonce')) {

            wp_send_json_error(__('Security check failed', 'epic-membership'));

        }



        $tier_id = intval($_POST['tier_id'] ?? 0);



        if (!$tier_id) {

            wp_send_json_error(__('Invalid tier ID', 'epic-membership'));

        }



        global $wpdb;

        $tiers_table = $this->database->get_table('tiers');



        $tier = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $tiers_table WHERE id = %d AND is_active = 1",

            $tier_id

        ));



        if (!$tier) {

            wp_send_json_error(__('Tier not found', 'epic-membership'));

        }



        $tier_data = array(

            'id' => $tier->id,

            'name' => $tier->name,

            'description' => $tier->description,

            'price' => number_format($tier->price, 2),

            'currency' => get_option('epic_membership_paypal_currency', 'USD'),

            'duration_days' => $tier->duration_days,

            'capabilities' => json_decode($tier->capabilities, true)

        );



        wp_send_json_success($tier_data);

    }



    /**

     * AJAX handler for activating free tier memberships

     */

    public function ajax_activate_free_tier() {

        // Verify nonce

        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'epic_membership_nonce')) {

            wp_send_json_error(__('Security check failed', 'epic-membership'));

        }



        // Check if user is logged in

        if (!is_user_logged_in()) {

            wp_send_json_error(__('Please log in to activate membership', 'epic-membership'));

        }



        $tier_id = intval($_POST['tier_id'] ?? 0);

        $user_id = get_current_user_id();



        if (!$tier_id) {

            wp_send_json_error(__('Invalid tier ID', 'epic-membership'));

        }



        // Get tier information

        global $wpdb;

        $tiers_table = $this->database->get_table('tiers');

        $tier = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $tiers_table WHERE id = %d AND is_active = 1",

            $tier_id

        ));



        if (!$tier) {

            wp_send_json_error(__('Tier not found or inactive', 'epic-membership'));

        }



        // Verify this is actually a free tier

        if ($tier->price > 0) {

            wp_send_json_error(__('This tier requires payment. Please use the payment option.', 'epic-membership'));

        }



        // Check if user already has this tier

        $memberships_table = $this->database->get_table('user_memberships');

        $existing_membership = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $memberships_table WHERE user_id = %d AND tier_id = %d AND is_active = 1",

            $user_id,

            $tier_id

        ));



        if ($existing_membership) {

            wp_send_json_error(__('You already have this membership tier active', 'epic-membership'));

        }



        // Deactivate existing memberships

        $wpdb->update(

            $memberships_table,

            array('is_active' => 0),

            array('user_id' => $user_id, 'is_active' => 1)

        );



        // Calculate end date

        $start_date = current_time('mysql');

        $end_date = null;



        if ($tier->duration_days) {

            $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + {$tier->duration_days} days"));

        }



        // Create new membership

        $membership_data = array(

            'user_id' => $user_id,

            'tier_id' => $tier->id,

            'start_date' => $start_date,

            'end_date' => $end_date,

            'is_active' => 1,

            'payment_status' => 'free',

            'notes' => sprintf(__('Free tier activated on %s', 'epic-membership'), current_time('Y-m-d H:i:s'))

        );



        $result = $wpdb->insert($memberships_table, $membership_data);



        if ($result === false) {

            wp_send_json_error(__('Failed to activate membership. Please try again.', 'epic-membership'));

        }



        // Clear membership cache to ensure fresh data

        $cache_key = 'epic_membership_status_' . $user_id;

        wp_cache_delete($cache_key, 'epic_membership');



        // Also clear any object cache for this user

        wp_cache_delete('user_membership_' . $user_id, 'epic_membership');



        // Fire action hook for other plugins/themes

        do_action('epic_membership_user_upgraded', $user_id, null, $tier);



        wp_send_json_success(array(

            'message' => sprintf(__('Successfully activated %s membership!', 'epic-membership'), $tier->name),

            'tier_name' => $tier->name,

            'membership_id' => $wpdb->insert_id

        ));

    }



    /**

     * Handle payment notifications and redirects

     */

    public function handle_payment_notifications() {

        // Handle payment success

        if (isset($_GET['payment_success'])) {

            add_action('wp_footer', array($this, 'show_payment_success_notification'));

        }



        // Handle payment error

        if (isset($_GET['payment_error'])) {

            add_action('wp_footer', array($this, 'show_payment_error_notification'));

        }



        // Handle payment cancelled

        if (isset($_GET['payment_cancelled'])) {

            add_action('wp_footer', array($this, 'show_payment_cancelled_notification'));

        }

    }



    /**

     * Show payment success notification

     */

    public function show_payment_success_notification() {

        ?>

        <script>

        jQuery(document).ready(function($) {

            if (typeof EpicMembershipPayPal !== 'undefined') {

                EpicMembershipPayPal.showMessage('<?php echo esc_js(__('Payment successful! Your membership has been activated.', 'epic-membership')); ?>', 'success');



                // Refresh membership status after a short delay

                setTimeout(function() {

                    if (typeof epicMembership !== 'undefined' && epicMembership.refreshMembershipStatus) {

                        epicMembership.refreshMembershipStatus();

                    }

                }, 2000);

            }

        });

        </script>

        <?php

    }



    /**

     * Show payment error notification

     */

    public function show_payment_error_notification() {

        $error_type = sanitize_text_field($_GET['payment_error']);

        $error_messages = array(

            'invalid_params' => __('Invalid payment parameters. Please try again.', 'epic-membership'),

            'payment_failed' => __('Payment failed. Please check your payment details and try again.', 'epic-membership'),

            'processing_failed' => __('Payment processing failed. Please contact support if the issue persists.', 'epic-membership')

        );



        $message = isset($error_messages[$error_type]) ? $error_messages[$error_type] : __('Payment error occurred. Please try again.', 'epic-membership');

        ?>

        <script>

        jQuery(document).ready(function($) {

            if (typeof EpicMembershipPayPal !== 'undefined') {

                EpicMembershipPayPal.showMessage('<?php echo esc_js($message); ?>', 'error');

            }

        });

        </script>

        <?php

    }



    /**

     * Show payment cancelled notification

     */

    public function show_payment_cancelled_notification() {

        ?>

        <script>

        jQuery(document).ready(function($) {

            if (typeof EpicMembershipPayPal !== 'undefined') {

                EpicMembershipPayPal.showMessage('<?php echo esc_js(__('Payment was cancelled. You can try again anytime.', 'epic-membership')); ?>', 'warning');

            }

        });

        </script>

        <?php

    }



    /**

     * Verify AJAX request security

     */

    private function verify_ajax_request() {

        // Check nonce

        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {

            return false;

        }



        // Check referer

        if (!wp_get_referer()) {

            return false;

        }



        // Additional security checks can be added here

        return true;

    }



    /**

     * Check rate limiting for user actions

     */

    private function check_rate_limit($user_id, $action) {

        $transient_key = 'epic_membership_rate_limit_' . $action . '_' . $user_id;

        $last_request = get_transient($transient_key);



        if ($last_request !== false) {

            // Allow one request per 30 seconds for status refresh

            return false;

        }



        // Set rate limit transient

        set_transient($transient_key, time(), 30);

        return true;

    }



    /**

     * Log status check for analytics

     */

    private function log_status_check($user_id) {

        // Simple logging - could be enhanced with proper analytics

        $log_data = array(

            'user_id' => $user_id,

            'action' => 'status_check',

            'timestamp' => current_time('mysql'),

            'ip_address' => $this->get_user_ip(),

            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? sanitize_text_field($_SERVER['HTTP_USER_AGENT']) : ''

        );



        // Store in option for now - could be moved to custom table

        $existing_logs = get_option('epic_membership_status_logs', array());

        $existing_logs[] = $log_data;



        // Keep only last 100 logs

        if (count($existing_logs) > 100) {

            $existing_logs = array_slice($existing_logs, -100);

        }



        update_option('epic_membership_status_logs', $existing_logs);

    }



    /**

     * Get user IP address safely

     */

    private function get_user_ip() {

        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');



        foreach ($ip_keys as $key) {

            if (array_key_exists($key, $_SERVER) === true) {

                $ip = $_SERVER[$key];

                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {

                    return $ip;

                }

            }

        }



        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';

    }



    /**

     * Create dedicated membership status page

     */

    public function create_membership_status_page() {

        // Only create page if it doesn't exist and we're not in admin

        if (is_admin()) {

            return;

        }



        // Check if page creation is enabled in settings

        $auto_create_page = get_option('epic_membership_auto_create_status_page', true);

        if (!$auto_create_page) {

            return;

        }



        // Check if page already exists

        $page_slug = 'membership-status';

        $existing_page = get_page_by_path($page_slug);



        if (!$existing_page) {

            // Get current user or default to admin

            $current_user = wp_get_current_user();

            $author_id = $current_user->ID ? $current_user->ID : 1;



            // Create the page

            $page_data = array(

                'post_title' => __('Membership Status', 'epic-membership'),

                'post_content' => '[epic_membership_dashboard show_upgrade="true" show_history="true" show_stats="true"]',

                'post_status' => 'publish',

                'post_type' => 'page',

                'post_name' => $page_slug,

                'post_author' => $author_id,

                'comment_status' => 'closed',

                'ping_status' => 'closed',

                'menu_order' => 0,

                'post_excerpt' => __('View your membership status, benefits, and manage your account.', 'epic-membership')

            );



            $page_id = wp_insert_post($page_data);



            if ($page_id && !is_wp_error($page_id)) {

                // Store page ID for future reference

                update_option('epic_membership_status_page_id', $page_id);



                // Add custom meta to identify this as a membership page

                update_post_meta($page_id, '_epic_membership_page_type', 'status_dashboard');

                update_post_meta($page_id, '_epic_membership_auto_created', true);



                // Don't set custom template - use default theme template



                // Make sure the page appears in menus

                update_post_meta($page_id, '_epic_membership_show_in_nav', true);



                // Log the creation

                error_log('Epic Membership: Created membership status page with ID: ' . $page_id);

            }

        } else {

            // Update existing page ID option

            update_option('epic_membership_status_page_id', $existing_page->ID);



            // Ensure existing page has proper meta

            update_post_meta($existing_page->ID, '_epic_membership_page_type', 'status_dashboard');

        }

    }



    /**

     * Handle membership status page template

     */

    public function handle_membership_status_page() {

        $page_id = get_option('epic_membership_status_page_id');



        if (is_page($page_id)) {

            // Ensure user is logged in

            if (!is_user_logged_in()) {

                wp_redirect(wp_login_url(get_permalink()));

                exit;

            }



            // Add custom body class

            add_filter('body_class', function($classes) {

                $classes[] = 'epic-membership-status-page';

                return $classes;

            });



            // Enqueue additional styles for the page

            add_action('wp_enqueue_scripts', array($this, 'enqueue_status_page_assets'));



            // Force content display instead of excerpt

            add_filter('the_excerpt', array($this, 'force_content_on_membership_page'), 999);

            add_filter('get_the_excerpt', array($this, 'force_content_on_membership_page'), 999);



            // Ensure shortcodes are processed

            add_filter('the_content', 'do_shortcode', 11);

            add_filter('the_excerpt', 'do_shortcode', 11);



            // Force full content display (not excerpt)

            add_filter('the_content_more_link', '__return_empty_string');

            add_filter('excerpt_more', '__return_empty_string');

        }

    }



    /**

     * Force content display instead of excerpt on membership page

     */

    public function force_content_on_membership_page($excerpt) {

        global $post;



        $page_id = get_option('epic_membership_status_page_id');

        if ($post && $post->ID == $page_id) {

            // Return the full content with shortcodes processed

            return do_shortcode($post->post_content);

        }



        return $excerpt;

    }



    /**

     * Force content display on membership page

     */

    public function force_membership_page_content() {

        global $post;



        $page_id = get_option('epic_membership_status_page_id');

        if (is_page($page_id) && $post && $post->ID == $page_id) {

            // Override ALL content display methods to show our shortcode

            $shortcode_content = do_shortcode($post->post_content);



            add_filter('the_content', function($content) use ($shortcode_content, $post) {

                if ($post->ID == get_option('epic_membership_status_page_id')) {

                    return $shortcode_content;

                }

                return $content;

            }, 999);



            add_filter('the_excerpt', function($excerpt) use ($shortcode_content, $post) {

                if ($post->ID == get_option('epic_membership_status_page_id')) {

                    return $shortcode_content;

                }

                return $excerpt;

            }, 999);



            add_filter('get_the_excerpt', function($excerpt) use ($shortcode_content, $post) {

                if ($post->ID == get_option('epic_membership_status_page_id')) {

                    return $shortcode_content;

                }

                return $excerpt;

            }, 999);



            // Also override post content directly

            add_filter('post_content', function($content) use ($shortcode_content, $post) {

                if ($post->ID == get_option('epic_membership_status_page_id')) {

                    return $shortcode_content;

                }

                return $content;

            }, 999);



            // Force the post object to have our content

            $post->post_content = $shortcode_content;

            $post->post_excerpt = '';

        }

    }



    /**

     * Override template for membership status page

     */

    public function membership_page_template($template) {

        global $post;



        $page_id = get_option('epic_membership_status_page_id');

        if ($post && $post->ID == $page_id) {

            // Create a custom template that ensures content is displayed

            $custom_template = $this->create_membership_page_template();

            if ($custom_template && file_exists($custom_template)) {

                return $custom_template;

            }

        }



        return $template;

    }



    /**

     * Create custom template for membership page

     */

    private function create_membership_page_template() {

        $upload_dir = wp_upload_dir();

        $template_dir = $upload_dir['basedir'] . '/epic-membership-templates/';



        // Create directory if it doesn't exist

        if (!file_exists($template_dir)) {

            wp_mkdir_p($template_dir);

        }



        $template_file = $template_dir . 'page-membership-status.php';



        // Create template file if it doesn't exist

        if (!file_exists($template_file)) {

            $template_content = $this->get_membership_page_template_content();

            file_put_contents($template_file, $template_content);

        }



        return $template_file;

    }



    /**

     * Get template content for membership page

     */

    private function get_membership_page_template_content() {

        return '<?php

/**

 * Template for Epic Membership Status Page

 * Auto-generated by Epic Membership Plugin

 */



get_header(); ?>



<div class="epic-membership-page-wrapper">

    <?php while (have_posts()) : the_post(); ?>

        <div class="epic-membership-page-content">

            <header class="entry-header">

                <h1 class="entry-title"><?php the_title(); ?></h1>

            </header>



            <div class="entry-content">

                <?php

                // Force display of shortcode content

                $content = get_the_content();

                echo do_shortcode($content);

                ?>

            </div>

        </div>

    <?php endwhile; ?>

</div>



<style>

.epic-membership-page-wrapper {

    max-width: 1200px;

    margin: 0 auto;

    padding: 20px;

}



.epic-membership-page-content {

    background: #fff;

    padding: 30px;

    border-radius: 8px;

    box-shadow: 0 2px 10px rgba(0,0,0,0.1);

}



.entry-header {

    margin-bottom: 30px;

    text-align: center;

}



.entry-title {

    font-size: 2.5em;

    margin: 0;

    color: #333;

}



.entry-content {

    line-height: 1.6;

}

</style>



<?php get_footer(); ?>';

    }



    /**

     * Enqueue assets for status page

     */

    public function enqueue_status_page_assets() {

        // Use the main enqueue function

        $this->enqueue_frontend_assets();

    }



    /**

     * Add membership status item to navigation menu

     */

    public function add_membership_menu_item($items, $args) {

        // Debug: Log the function call

        error_log('Epic Membership: add_membership_menu_item called');

        error_log('Epic Membership: Menu position setting = ' . get_option('epic_membership_dashboard_menu_position', 'auto'));



        // Only add to primary menu and if user is logged in

        if (isset($args->theme_location) && $args->theme_location === 'primary' && is_user_logged_in()) {

            // Check menu position setting - don't add if set to 'none' or 'user_profile'

            $menu_position = get_option('epic_membership_dashboard_menu_position', 'auto');

            error_log('Epic Membership: Menu position = ' . $menu_position);

            if ($menu_position !== 'auto') {

                error_log('Epic Membership: Menu position is not auto, returning items unchanged');

                return $items;

            }



            $page_id = get_option('epic_membership_status_page_id');



            if ($page_id) {

                $membership_url = get_permalink($page_id);

                $user_id = get_current_user_id();

                $membership = $this->database->get_user_membership($user_id);



                // Get status info for menu indicator

                $status_info = $this->get_membership_status_info($membership);

                $status_class = '';

                $status_indicator = '';



                if ($membership) {

                    switch ($status_info['status']) {

                        case 'expired':

                            $status_class = 'membership-expired';

                            $status_indicator = ' <span class="menu-status-indicator expired">!</span>';

                            break;

                        case 'expiring':

                            $status_class = 'membership-expiring';

                            $status_indicator = ' <span class="menu-status-indicator expiring">⚠</span>';

                            break;

                        case 'active':

                            $status_class = 'membership-active';

                            $status_indicator = ' <span class="menu-status-indicator active">✓</span>';

                            break;

                    }

                }



                $menu_item = '<li class="menu-item menu-item-membership-status ' . esc_attr($status_class) . '">';

                $menu_item .= '<a href="' . esc_url($membership_url) . '">';

                $menu_item .= __('My Membership', 'epic-membership') . $status_indicator;

                $menu_item .= '</a></li>';



                $items .= $menu_item;

            }

        }



        return $items;

    }



    /**

     * Modify membership menu item for better styling

     */

    public function modify_membership_menu_item($items, $args) {

        if (isset($args->theme_location) && $args->theme_location === 'primary' && is_user_logged_in()) {

            foreach ($items as $item) {

                if (strpos($item->url, 'membership-status') !== false) {

                    $item->classes[] = 'epic-membership-menu-item';

                }

            }

        }



        return $items;

    }



    /**

     * Get membership status page URL

     */

    public function get_membership_status_url() {

        $page_id = get_option('epic_membership_status_page_id');

        return $page_id ? get_permalink($page_id) : home_url('/membership-status/');

    }



    /**

     * Check if current page is membership status page

     */

    public function is_membership_status_page() {

        $page_id = get_option('epic_membership_status_page_id');

        return is_page($page_id);

    }



    /**

     * Add membership page to nav menu admin

     */

    public function add_membership_page_to_nav_menu($object) {

        // This ensures the membership status page appears in the nav menu admin

        if (is_admin() && isset($_GET['page']) && $_GET['page'] === 'nav-menus.php') {

            $page_id = get_option('epic_membership_status_page_id');

            if ($page_id) {

                // Force refresh the page cache so it appears in menu admin

                clean_post_cache($page_id);

            }

        }

        return $object;

    }



    /**

     * Clear membership status cache for user

     */

    public function clear_membership_cache($user_id) {

        $cache_key = 'epic_membership_status_' . $user_id;

        wp_cache_delete($cache_key, 'epic_membership');



        // Also clear any related transients

        delete_transient('epic_membership_benefits_' . $user_id);

        delete_transient('epic_membership_stats_' . $user_id);

    }



    /**

     * Get cached membership benefits

     */

    private function get_cached_membership_benefits($membership) {

        if (!$membership) {

            return null;

        }



        $cache_key = 'epic_membership_benefits_' . $membership->user_id;

        $cached_benefits = get_transient($cache_key);



        if ($cached_benefits !== false) {

            return $cached_benefits;

        }



        // Generate benefits data

        $benefits_html = $this->get_membership_benefits($membership);



        // Cache for 1 hour

        set_transient($cache_key, $benefits_html, HOUR_IN_SECONDS);



        return $benefits_html;

    }



    /**

     * Sanitize and validate dashboard shortcode attributes

     */

    private function sanitize_dashboard_attributes($atts) {

        $defaults = array(

            'show_upgrade' => 'true',

            'show_history' => 'false',

            'show_stats' => 'true'

        );



        $atts = shortcode_atts($defaults, $atts);



        // Validate boolean values

        foreach ($atts as $key => $value) {

            $atts[$key] = in_array(strtolower($value), array('true', '1', 'yes', 'on')) ? 'true' : 'false';

        }



        return $atts;

    }



    /**

     * Register dashboard settings

     */

    public function register_dashboard_settings() {

        register_setting('epic_membership_dashboard', 'epic_membership_auto_create_status_page');

        register_setting('epic_membership_dashboard', 'epic_membership_status_page_id');

        register_setting('epic_membership_dashboard', 'epic_membership_dashboard_menu_position');

        register_setting('epic_membership_dashboard', 'epic_membership_dashboard_require_login');

    }



    /**

     * Add membership page state indicator in admin

     */

    public function add_membership_page_state($post_states, $post) {

        $membership_page_id = get_option('epic_membership_status_page_id');



        if ($post->ID == $membership_page_id) {

            $post_states['epic_membership_status'] = __('Membership Status Page', 'epic-membership');

        }



        return $post_states;

    }



    /**

     * Get membership status page info for admin

     */

    public function get_status_page_info() {

        $page_id = get_option('epic_membership_status_page_id');



        if (!$page_id) {

            return array(

                'exists' => false,

                'message' => __('No membership status page found.', 'epic-membership')

            );

        }



        $page = get_post($page_id);



        if (!$page) {

            return array(

                'exists' => false,

                'message' => __('Membership status page was deleted.', 'epic-membership')

            );

        }



        return array(

            'exists' => true,

            'page' => $page,

            'url' => get_permalink($page_id),

            'edit_url' => get_edit_post_link($page_id),

            'status' => $page->post_status,

            'message' => sprintf(

                __('Membership status page exists: <a href="%s" target="_blank">%s</a>', 'epic-membership'),

                get_permalink($page_id),

                $page->post_title

            )

        );

    }



    /**

     * Create status page manually (for admin use)

     */

    public function create_status_page_manually() {

        // Remove the admin check temporarily

        $page_slug = 'membership-status';



        // Check for existing page (including trashed ones)

        $existing_page = get_page_by_path($page_slug);

        if (!$existing_page) {

            // Also check by post_name in case get_page_by_path doesn't find it

            $existing_posts = get_posts(array(

                'name' => $page_slug,

                'post_type' => 'page',

                'post_status' => array('publish', 'draft', 'private', 'trash'),

                'numberposts' => 1

            ));

            if (!empty($existing_posts)) {

                $existing_page = $existing_posts[0];

            }

        }



        if ($existing_page) {

            // If page is in trash, restore it

            if ($existing_page->post_status === 'trash') {

                wp_untrash_post($existing_page->ID);

                update_option('epic_membership_status_page_id', $existing_page->ID);

                return array(

                    'success' => true,

                    'page_id' => $existing_page->ID,

                    'message' => __('Membership status page restored from trash.', 'epic-membership')

                );

            }



            return array(

                'success' => false,

                'message' => __('Membership status page already exists.', 'epic-membership')

            );

        }



        $current_user = wp_get_current_user();

        $author_id = $current_user->ID ? $current_user->ID : 1;



        // Create comprehensive page content that includes both shortcode and fallback

        $page_content = '[epic_membership_dashboard show_upgrade="true" show_history="true" show_stats="true"]



<!-- Fallback content in case shortcode fails -->

<div id="epic-membership-fallback" style="display:none;">

    <h2>Membership Dashboard</h2>

    <p>Loading your membership information...</p>

    <script>

    // Show fallback if shortcode content is not present

    setTimeout(function() {

        var dashboard = document.querySelector(".epic-membership-dashboard");

        var fallback = document.getElementById("epic-membership-fallback");

        if (!dashboard && fallback) {

            fallback.style.display = "block";

            // Try to load shortcode content via AJAX

            if (typeof jQuery !== "undefined") {

                jQuery.post("' . admin_url('admin-ajax.php') . '", {

                    action: "epic_membership_get_dashboard_content"

                }, function(response) {

                    if (response.success) {

                        fallback.innerHTML = response.data;

                    }

                });

            }

        }

    }, 1000);

    </script>

</div>';



        $page_data = array(

            'post_title' => __('Membership Status', 'epic-membership'),

            'post_content' => $page_content,

            'post_status' => 'publish',

            'post_type' => 'page',

            'post_name' => $page_slug,

            'post_author' => $author_id,

            'comment_status' => 'closed',

            'ping_status' => 'closed',

            'menu_order' => 0,

            'post_excerpt' => ''  // Remove excerpt to force content display

        );







        $page_id = wp_insert_post($page_data);



        if ($page_id && !is_wp_error($page_id)) {

            update_option('epic_membership_status_page_id', $page_id);

            update_post_meta($page_id, '_epic_membership_page_type', 'status_dashboard');

            update_post_meta($page_id, '_epic_membership_auto_created', false);

            update_post_meta($page_id, '_epic_membership_show_in_nav', true);



            return array(

                'success' => true,

                'page_id' => $page_id,

                'message' => sprintf(

                    __('Membership status page created successfully. <a href="%s">Edit page</a> | <a href="%s" target="_blank">View page</a>', 'epic-membership'),

                    get_edit_post_link($page_id),

                    get_permalink($page_id)

                )

            );

        } else {

            $error_message = is_wp_error($page_id) ? $page_id->get_error_message() : 'Unknown error';

            error_log('Epic Membership: Failed to create status page. Error: ' . $error_message);

            return array(

                'success' => false,

                'message' => __('Failed to create membership status page.', 'epic-membership') . ' Error: ' . $error_message

            );

        }

    }



    /**

     * Add security headers for dashboard pages

     */

    public function add_security_headers() {

        if ($this->is_membership_status_page()) {

            // Prevent clickjacking

            header('X-Frame-Options: SAMEORIGIN');



            // Prevent MIME type sniffing

            header('X-Content-Type-Options: nosniff');



            // Enable XSS protection

            header('X-XSS-Protection: 1; mode=block');



            // Referrer policy

            header('Referrer-Policy: strict-origin-when-cross-origin');

        }

    }

}

