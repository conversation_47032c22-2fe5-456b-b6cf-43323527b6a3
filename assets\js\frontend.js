/**

 * Epic Membership Plugin - Frontend JavaScript

 */



(function($) {

    'use strict';

    

    // Plugin object

    var EpicMembership = {



        // Flag to prevent multiple modals

        modalOpen: false,



        /**

         * Initialize the plugin

         */

        init: function() {

            this.initTimezoneDetection();

            this.initCountdownTimers();

            this.initContentProtection();

            this.initUserDashboard();

            this.bindEvents();

            this.initAdvancedFallbacks();

        },

        

        /**

         * Initialize timezone detection

         */

        initTimezoneDetection: function() {

            // Detect user's timezone

            var userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            

            // Store in session storage for server-side access

            if (typeof(Storage) !== "undefined") {

                sessionStorage.setItem('epic_membership_timezone', userTimezone);

            }

            

            // Send to server via AJAX for persistent storage

            this.updateUserTimezone(userTimezone);

        },

        

        /**

         * Update user timezone on server

         */

        updateUserTimezone: function(timezone) {

            $.ajax({

                url: epicMembership.ajaxUrl,

                type: 'POST',

                data: {

                    action: 'epic_membership_update_timezone',

                    timezone: timezone,

                    nonce: epicMembership.nonce

                },

                success: function(response) {

                    if (response.success) {

                        console.log('Timezone updated:', timezone);

                    }

                }

            });

        },

        

        /**

         * Initialize countdown timers

         */

        initCountdownTimers: function() {

            var self = this;

            

            $('.epic-membership-countdown').each(function() {

                var $countdown = $(this);

                var releaseTime = $countdown.data('release-time');

                var serverTimezone = $countdown.data('server-timezone');

                

                if (releaseTime) {

                    self.startCountdown($countdown, releaseTime, serverTimezone);

                }

            });

        },

        

        /**

         * Start countdown timer

         */

        startCountdown: function($countdown, releaseTime, serverTimezone) {

            var self = this;



            // Parse the ISO date string directly - it should already be in the correct format

            var releaseDate = new Date(releaseTime);



            // Validate the date

            if (isNaN(releaseDate.getTime())) {

                console.error('Invalid release date:', releaseTime);

                return;

            }



            var timer = setInterval(function() {

                var now = new Date().getTime();

                var distance = releaseDate.getTime() - now;



                if (distance < 0) {

                    clearInterval(timer);

                    self.handleCountdownExpired($countdown);

                    return;

                }



                var days = Math.floor(distance / (1000 * 60 * 60 * 24));

                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

                var seconds = Math.floor((distance % (1000 * 60)) / 1000);



                $countdown.find('.epic-countdown-days').text(days.toString().padStart(2, '0'));

                $countdown.find('.epic-countdown-hours').text(hours.toString().padStart(2, '0'));

                $countdown.find('.epic-countdown-minutes').text(minutes.toString().padStart(2, '0'));

                $countdown.find('.epic-countdown-seconds').text(seconds.toString().padStart(2, '0'));



            }, 1000);

        },

        

        /**

         * Format time for display in user's local timezone

         */

        formatTimeForUser: function(date) {

            try {

                return date.toLocaleString(undefined, {

                    year: 'numeric',

                    month: '2-digit',

                    day: '2-digit',

                    hour: '2-digit',

                    minute: '2-digit',

                    second: '2-digit',

                    hour12: false

                });

            } catch (e) {

                console.warn('Time formatting failed:', e);

                return date.toString();

            }

        },

        

        /**

         * Handle countdown expiration

         */

        handleCountdownExpired: function($countdown) {

            $countdown.addClass('epic-membership-countdown-expired');

            var contentLocked = (epicMembership.strings && epicMembership.strings.contentLocked) || 'This content will be available on:';

            $countdown.find('.epic-membership-countdown-title').text(contentLocked);



            // Refresh the page to show unlocked content

            setTimeout(function() {

                window.location.reload();

            }, 2000);

        },

        

        /**

         * Initialize content protection

         */

        initContentProtection: function() {

            var self = this;



            // Handle upgrade button clicks ONLY on content protection pages (not dashboard)
            // DISABLED: This was causing page reloads - upgrade-tier-modal.js now handles all upgrade buttons

            // if (!$('.epic-membership-status-page').length) {
            //     $('.epic-membership-upgrade-button').on('click', function(e) {
            //         e.preventDefault();
            //         self.handleUpgradeClick($(this));
            //     });
            // }



            // Handle protected content interactions

            $('.epic-membership-protected-content').on('click', function() {

                self.showUpgradeModal(true); // Skip status check since content is already protected

            });

        },

        

        /**

         * Handle upgrade button click

         */

        handleUpgradeClick: function($button) {

            // Always redirect to membership status page instead of showing modal or using upgrade URL

            var membershipStatusUrl = this.getMembershipStatusUrl();

            window.location.href = membershipStatusUrl;

        },



        /**

         * Get membership status page URL

         */

        getMembershipStatusUrl: function() {

            // Try to get the URL from localized data first

            if (epicMembership && epicMembership.membershipStatusUrl) {

                return epicMembership.membershipStatusUrl;

            }



            // Fallback to default URL pattern

            return window.location.origin + '/membership-status/';

        },



        /**

         * Show upgrade modal

         */

        showUpgradeModal: function(skipStatusCheck) {

            var self = this;



            // Prevent multiple modals

            if (this.modalOpen) {

                console.log('Modal already open, ignoring request');

                return;

            }



            // Skip status check if explicitly requested (e.g., from protected content clicks)

            if (skipStatusCheck) {

                this.displayUpgradeModal();

                return;

            }



            // First check current membership status to avoid showing modal unnecessarily

            this.checkMembershipStatus(function(hasAccess) {

                if (hasAccess) {

                    // User has access, don't show modal and refresh page to update content

                    console.log('User has access, refreshing page...');

                    window.location.reload();

                    return;

                }



                // User doesn't have access, show modal

                self.displayUpgradeModal();

            });

        },



        /**

         * Check current membership status

         */

        checkMembershipStatus: function(callback) {

            console.log('Checking membership status with action: epic_membership_get_status');

            $.ajax({

                url: epicMembership.ajaxUrl,

                type: 'POST',

                data: {

                    action: 'epic_membership_get_status',

                    nonce: epicMembership.nonce,

                    force_refresh: true // Force fresh data, not cached

                },

                success: function(response) {

                    console.log('Membership status response:', response);

                    if (response.success) {

                        var hasAccess = response.data.status !== 'none' && response.data.status !== 'expired';

                        callback(hasAccess);

                    } else {

                        callback(false);

                    }

                },

                error: function(xhr, status, error) {

                    console.log('Membership status check failed:', error, 'Status:', xhr.status, 'Response:', xhr.responseText);



                    // If the first action fails, try the old action name as fallback

                    if (xhr.status === 400) {

                        console.log('Trying fallback action: epic_membership_check_status');

                        $.ajax({

                            url: epicMembership.ajaxUrl,

                            type: 'POST',

                            data: {

                                action: 'epic_membership_check_status',

                                nonce: epicMembership.nonce,

                                force_refresh: true

                            },

                            success: function(response) {

                                console.log('Fallback action succeeded:', response);

                                if (response.success) {

                                    var hasAccess = response.data.status !== 'none' && response.data.status !== 'expired';

                                    callback(hasAccess);

                                } else {

                                    callback(false);

                                }

                            },

                            error: function() {

                                console.log('Both actions failed');

                                callback(false);

                            }

                        });

                    } else {

                        callback(false);

                    }

                }

            });

        },



        /**

         * Display the actual upgrade modal

         */

        displayUpgradeModal: function() {

            var self = this;



            // Set modal open flag

            this.modalOpen = true;



            // Create a simple modal for upgrade information

            var modal = $('<div class="epic-membership-modal-overlay">' +

                '<div class="epic-membership-modal">' +

                '<div class="epic-membership-modal-header">' +

                '<h3>Upgrade Required</h3>' +

                '<button class="epic-membership-modal-close">&times;</button>' +

                '</div>' +

                '<div class="epic-membership-modal-content">' +

                '<p>' + ((epicMembership.strings && epicMembership.strings.upgradeRequired) || 'Premium membership required to access this content.') + '</p>' +

                '<div class="epic-membership-modal-actions">' +

                '<button class="epic-membership-upgrade-button">Upgrade Now</button>' +

                '</div>' +

                '</div>' +

                '</div>' +

                '</div>');



            $('body').append(modal);



            // No need to prevent scrolling since there's no background overlay



            // Handle modal close

            modal.find('.epic-membership-modal-close').on('click', function() {

                self.cleanupModals();

            });



            // Handle overlay click

            modal.on('click', function(e) {

                if (e.target === this) {

                    self.cleanupModals();

                }

            });



            // Handle upgrade button click

            modal.find('.epic-membership-upgrade-button').on('click', function() {

                // Redirect to membership page or show tier selection

                window.location.href = epicMembership.membershipUrl || '#';

            });

        },

        

        /**

         * Initialize user dashboard

         */

        initUserDashboard: function() {

            var self = this;



            // Handle membership status refresh

            $('.epic-membership-refresh-status').on('click', function(e) {

                e.preventDefault();

                self.refreshMembershipStatus($(this));

            });



            // Handle tier upgrade buttons (only on dashboard pages)
            // Note: Tier upgrade handling is now managed by upgrade-tier-modal.js
            // This handler is removed to prevent conflicts with the new modal system



            // Handle renew membership button

            $('.epic-membership-renew-button').on('click', function(e) {

                e.preventDefault();

                self.handleMembershipRenewal($(this));

            });



            // Auto-refresh membership status periodically (only on status page)

            if ($('.epic-membership-status-page').length > 0) {

                setInterval(function() {

                    self.refreshMembershipStatus();

                }, 300000); // 5 minutes

            }



            // Initialize real-time countdown for expiring memberships

            this.initExpirationCountdown();



            // Initialize benefit tooltips

            this.initBenefitTooltips();

        },

        

        /**

         * Refresh membership status

         */

        refreshMembershipStatus: function($button) {

            var self = this;



            // Show loading state

            if ($button) {

                var refreshing = (epicMembership.strings && epicMembership.strings.refreshing) || 'Refreshing...';

                self.showButtonLoading($button, refreshing);

            }



            $.ajax({

                url: epicMembership.ajaxUrl,

                type: 'POST',

                data: {

                    action: 'epic_membership_get_status',

                    nonce: epicMembership.nonce

                },

                success: function(response) {

                    if (response.success && response.data) {

                        // Update dashboard with new data

                        $('.epic-membership-status-value').each(function() {

                            var field = $(this).data('field');

                            if (response.data[field]) {

                                $(this).text(response.data[field]);

                            }

                        });



                        // Update status classes

                        self.updateStatusClasses(response.data);



                        // Show success message

                        if ($button) {

                            var refreshed = (epicMembership.strings && epicMembership.strings.refreshed) || 'Status refreshed';

                            self.showButtonSuccess($button, refreshed);

                        }



                        // Trigger custom event

                        $(document).trigger('epic-membership-status-updated', [response.data]);

                    }

                },

                error: function() {

                    if ($button) {

                        var error = (epicMembership.strings && epicMembership.strings.error) || 'An error occurred. Please try again.';

                        self.showButtonError($button, error);

                    }

                },

                complete: function() {

                    if ($button) {

                        setTimeout(function() {

                            self.hideButtonLoading($button);

                        }, 2000);

                    }

                }

            });

        },



        /**

         * Handle tier upgrade

         */

        handleTierUpgrade: function($button) {

            var tierId = $button.data('tier-id');



            // Try multiple selectors to find tier name

            var tierName = $button.closest('.tier-comparison-card, .tier-option-card').find('.epic-membership-tier-badge').text();



            // If not found, try other common selectors

            if (!tierName) {

                tierName = $button.closest('.upgrade-tier-action').siblings().find('.tier-name, .tier-title, h3, h4').first().text();

            }



            // If still not found, try getting from button text or use generic name

            if (!tierName) {

                tierName = $button.text().replace('Activate ', '').replace('Upgrade to ', '') || 'Membership Tier';

            }



            console.log('Tier upgrade clicked:', tierId, tierName); // Debug log



            // Show upgrade confirmation modal

            this.showUpgradeConfirmation(tierId, tierName);

        },



        /**

         * Handle membership renewal

         */

        handleMembershipRenewal: function($button) {

            // Show renewal modal or redirect to payment

            this.showRenewalModal();

        },



        /**

         * Show upgrade confirmation modal

         */

        showUpgradeConfirmation: function(tierId, tierName) {
            // This function will be replaced with new clean implementation
            console.log('Epic Membership: showUpgradeConfirmation - placeholder for new implementation');
        },



        /**

         * Debug modal environment for troubleshooting

         */

        debugModalEnvironment: function() {

            if (!window.console || !console.log) return;



            var debugInfo = {

                userAgent: navigator.userAgent,

                viewport: {

                    width: window.innerWidth,

                    height: window.innerHeight

                },

                bodyClasses: $('body').attr('class'),

                htmlClasses: $('html').attr('class'),

                existingModals: $('.epic-membership-modal-overlay').length,

                bodyStyles: {

                    position: $('body').css('position'),

                    overflow: $('body').css('overflow'),

                    transform: $('body').css('transform'),

                    zIndex: $('body').css('z-index')

                },

                jQueryVersion: $.fn.jquery,

                hasAdminBar: $('#wpadminbar').length > 0,

                isRTL: $('body').hasClass('rtl'),

                timestamp: new Date().toISOString()

            };



            console.log('Epic Membership: Modal Environment Debug Info:', debugInfo);



            // Store debug info for potential support requests

            window.epicMembershipDebugInfo = debugInfo;

        },



        /**

         * Display the upgrade confirmation modal

         */

        displayUpgradeConfirmationModal: function(tierId, tierName) {

            var self = this;



            console.log('Epic Membership: displayUpgradeConfirmationModal called', {tierId: tierId, tierName: tierName});



            try {

                // Fallback strings in case localization fails

                var strings = epicMembership.strings || {};

                var upgradeConfirmTitle = strings.upgradeConfirmTitle || 'Confirm Membership Upgrade';

                var upgradeConfirmMessage = strings.upgradeConfirmMessage || 'Are you sure you want to upgrade to %s membership?';

                var confirmUpgrade = strings.confirmUpgrade || 'Confirm Upgrade';

                var cancel = strings.cancel || 'Cancel';



                var modal = $('<div class="epic-membership-modal-overlay">' +

                    '<div class="epic-membership-modal upgrade-modal">' +

                    '<div class="epic-membership-modal-header">' +

                    '<h3>' + upgradeConfirmTitle + '</h3>' +

                    '<button class="epic-membership-modal-close">&times;</button>' +

                    '</div>' +

                    '<div class="epic-membership-modal-content">' +

                    '<p>' + upgradeConfirmMessage.replace('%s', tierName || 'Premium') + '</p>' +

                    '<div class="epic-membership-modal-actions">' +

                    '<button class="epic-membership-upgrade-confirm button-primary" data-tier-id="' + tierId + '">' +

                    confirmUpgrade + '</button>' +

                    '<button class="epic-membership-modal-cancel button-secondary">' +

                    cancel + '</button>' +

                    '</div>' +

                    '</div>' +

                    '</div>' +

                    '</div>');



                console.log('Epic Membership: Modal HTML created, appending to body');

                $('body').append(modal);



                // No need to prevent scrolling since there's no background overlay



                console.log('Epic Membership: Body classes added, forcing modal positioning');



                // Force modal positioning after DOM insertion for theme compatibility

                setTimeout(function() {

                    self.forceModalPositioning(modal);

                }, 10);



                // Handle modal interactions

                this.bindModalEvents(modal);



                console.log('Epic Membership: Modal display completed successfully');



            } catch (error) {

                console.error('Epic Membership: Error displaying modal:', error);



                // Fallback to simple alert if modal creation fails

                if (confirm('Upgrade to ' + (tierName || 'Premium') + ' membership?')) {

                    // Trigger upgrade process directly

                    this.processUpgrade(tierId);

                }



                // Reset modal state

                this.modalOpen = false;

            }

        },



        /**

         * Test modal functionality (for debugging)

         */

        testModal: function() {

            console.log('Epic Membership: Testing modal functionality...');



            // Reset any existing state

            this.cleanupModals();



            // Test with dummy data

            this.showUpgradeConfirmation('test-tier', 'Test Tier');



            // Log test completion

            setTimeout(function() {

                console.log('Epic Membership: Modal test completed. Check if modal is visible.');

            }, 500);

        },



        /**

         * Show renewal modal

         */

        showRenewalModal: function() {

            // Fallback strings in case localization fails

            var strings = epicMembership.strings || {};

            var renewalTitle = strings.renewalTitle || 'Renew Membership';

            var renewalMessage = strings.renewalMessage || 'Your membership is about to expire. Would you like to renew it?';

            var proceedRenewal = strings.proceedRenewal || 'Proceed with Renewal';

            var cancel = strings.cancel || 'Cancel';



            var modal = $('<div class="epic-membership-modal-overlay">' +

                '<div class="epic-membership-modal renewal-modal">' +

                '<div class="epic-membership-modal-header">' +

                '<h3>' + renewalTitle + '</h3>' +

                '<button class="epic-membership-modal-close">&times;</button>' +

                '</div>' +

                '<div class="epic-membership-modal-content">' +

                '<p>' + renewalMessage + '</p>' +

                '<div class="epic-membership-modal-actions">' +

                '<button class="epic-membership-renew-confirm button-primary">' +

                proceedRenewal + '</button>' +

                '<button class="epic-membership-modal-cancel button-secondary">' +

                cancel + '</button>' +

                '</div>' +

                '</div>' +

                '</div>' +

                '</div>');



            $('body').append(modal);



            // No need to prevent scrolling since there's no background overlay



            // Handle modal interactions

            this.bindModalEvents(modal);

        },

        

        /**

         * Bind global events

         */

        bindEvents: function() {

            var self = this;

            

            // Handle window resize for responsive adjustments

            $(window).on('resize', function() {

                self.handleResize();

            });

            

            // Handle visibility change to pause/resume timers

            $(document).on('visibilitychange', function() {

                if (document.hidden) {

                    self.pauseTimers();

                } else {

                    self.resumeTimers();

                }

            });

        },

        

        /**

         * Handle window resize

         */

        handleResize: function() {

            // Adjust countdown timer layout on mobile

            $('.epic-membership-countdown-timer').each(function() {

                var $timer = $(this);

                if ($(window).width() < 768) {

                    $timer.addClass('mobile-layout');

                } else {

                    $timer.removeClass('mobile-layout');

                }

            });

        },

        

        /**

         * Pause timers when page is hidden

         */

        pauseTimers: function() {

            this.timersPaused = true;

        },

        

        /**

         * Resume timers when page is visible

         */

        resumeTimers: function() {

            this.timersPaused = false;

            // Reinitialize countdown timers to sync with current time

            this.initCountdownTimers();

        },

        

        /**

         * Utility function to show loading state

         */

        showLoading: function($element) {

            $element.addClass('epic-membership-loading');

            $element.append('<span class="epic-membership-spinner"></span>');

        },

        

        /**

         * Bind modal events

         */

        bindModalEvents: function(modal) {

            var self = this;



            // Handle modal close

            modal.find('.epic-membership-modal-close, .epic-membership-modal-cancel').on('click', function() {

                self.cleanupModals();

            });



            // Handle overlay click

            modal.on('click', function(e) {

                if (e.target === this) {

                    self.cleanupModals();

                }

            });



            // Handle escape key

            $(document).on('keydown.epic-modal', function(e) {

                if (e.keyCode === 27) {

                    self.modalOpen = false;

                    // No body class to remove since we don't prevent scrolling

                    modal.remove();

                    $(document).off('keydown.epic-modal');

                }

            });



            // Handle upgrade confirmation

            modal.find('.epic-membership-upgrade-confirm').on('click', function() {

                var $button = $(this);

                var tierId = $button.data('tier-id');



                console.log('Upgrade confirmation clicked:', tierId); // Debug log



                if (!tierId) {

                    self.showError('Invalid tier selection');

                    return;

                }



                // Show loading state

                self.showButtonLoading($button);



                // Check if tier is free

                self.isTierFree(tierId).then(function(isFree) {

                    console.log('Tier', tierId, 'is free:', isFree); // Debug log

                    if (isFree) {

                        // Activate free tier directly

                        return self.activateFreeTier(tierId);

                    } else {

                        // Close modal and redirect to PayPal payment

                        modal.remove();

                        // Trigger PayPal payment flow

                        if (typeof EpicMembershipPayPal !== 'undefined') {

                            EpicMembershipPayPal.showPaymentOptions(tierId);

                        } else {

                            throw new Error('PayPal integration not available');

                        }

                        return Promise.resolve();

                    }

                }).then(function(result) {

                    if (result && result.message) {

                        // Free tier was activated successfully

                        self.modalOpen = false;

                        modal.remove();

                        self.showSuccess(result.message);



                        // Refresh membership status

                        setTimeout(function() {

                            self.refreshMembershipStatus();

                        }, 1000);

                    }

                }).catch(function(error) {

                    self.showError(error.message || 'An error occurred');

                }).finally(function() {

                    self.hideButtonLoading($button);

                });

            });

        },



        /**

         * Initialize expiration countdown

         */

        initExpirationCountdown: function() {

            var $expirationElement = $('.status-item-value[data-field="time_remaining"]');



            if ($expirationElement.length > 0) {

                var self = this;



                // Update countdown every minute

                setInterval(function() {

                    self.updateExpirationCountdown($expirationElement);

                }, 60000);

            }

        },



        /**

         * Update expiration countdown

         */

        updateExpirationCountdown: function($element) {

            // This would need server-side support to get accurate remaining time

            // For now, we'll just refresh the status

            this.refreshMembershipStatus();

        },



        /**

         * Initialize benefit tooltips

         */

        initBenefitTooltips: function() {

            $('.benefit-item').each(function() {

                var $item = $(this);

                var description = $item.find('.benefit-description').text();



                if (description) {

                    $item.attr('title', description);

                }

            });

        },



        /**

         * Update status classes based on new data

         */

        updateStatusClasses: function(data) {

            var $statusCard = $('.epic-membership-status-card');



            // Remove existing status classes

            $statusCard.removeClass('membership-active membership-expired membership-expiring membership-warning membership-pending');



            // Add new status class based on data

            if (data.status) {

                switch (data.status) {

                    case 'active':

                        $statusCard.addClass('membership-active');

                        break;

                    case 'expired':

                        $statusCard.addClass('membership-expired');

                        break;

                    case 'expiring':

                        $statusCard.addClass('membership-expiring');

                        break;

                    case 'warning':

                        $statusCard.addClass('membership-warning');

                        break;

                    case 'pending':

                        $statusCard.addClass('membership-pending');

                        break;

                }

            }

        },



        /**

         * Show button loading state

         */

        showButtonLoading: function($button, text) {

            $button.data('original-text', $button.text());

            $button.text(text || 'Loading...');

            $button.prop('disabled', true);

            $button.addClass('loading');

        },



        /**

         * Show button success state

         */

        showButtonSuccess: function($button, text) {

            $button.text(text || 'Success!');

            $button.removeClass('loading').addClass('success');

        },



        /**

         * Show button error state

         */

        showButtonError: function($button, text) {

            $button.text(text || 'Error');

            $button.removeClass('loading').addClass('error');

        },



        /**

         * Show general success message

         */

        showSuccess: function(message) {

            this.showMessage(message, 'success');

        },



        /**

         * Show general error message

         */

        showError: function(message) {

            this.showMessage(message, 'error');

        },



        /**

         * Show general message

         */

        showMessage: function(message, type) {

            // Create message element

            var $message = $('<div class="epic-membership-message epic-membership-' + type + '">' + message + '</div>');



            // Add to page

            $('body').append($message);



            // Show with animation

            $message.fadeIn();



            // Auto-hide after 5 seconds

            setTimeout(function() {

                $message.fadeOut(function() {

                    $message.remove();

                });

            }, 5000);

        },



        /**

         * Hide button loading state

         */

        hideButtonLoading: function($button) {

            var originalText = $button.data('original-text') || 'Refresh Status';

            $button.text(originalText);

            $button.prop('disabled', false);

            $button.removeClass('loading success error');

        },



        /**

         * Utility function to show loading state

         */

        showLoading: function($element) {

            $element.addClass('epic-membership-loading');

            $element.append('<span class="epic-membership-spinner"></span>');

        },



        /**

         * Utility function to hide loading state

         */

        hideLoading: function($element) {

            $element.removeClass('epic-membership-loading');

            $element.find('.epic-membership-spinner').remove();

        },



        /**

         * Get tier information via AJAX

         */

        getTierInfo: function(tierId) {

            var self = this;



            return new Promise(function(resolve, reject) {

                $.ajax({

                    url: epicMembership.ajaxUrl,

                    type: 'POST',

                    data: {

                        action: 'epic_membership_get_tier_info',

                        tier_id: tierId,

                        nonce: epicMembership.nonce

                    },

                    success: function(response) {

                        if (response.success) {

                            resolve(response.data);

                        } else {

                            reject(new Error(response.data || 'Failed to get tier information'));

                        }

                    },

                    error: function() {

                        reject(new Error('Network error while fetching tier information'));

                    }

                });

            });

        },



        /**

         * Check if a tier is free (price = 0)

         */

        isTierFree: function(tierId) {

            var self = this;



            return this.getTierInfo(tierId).then(function(tierData) {

                return parseFloat(tierData.price) === 0;

            });

        },



        /**

         * Activate free tier membership

         */

        activateFreeTier: function(tierId) {

            var self = this;



            return new Promise(function(resolve, reject) {

                $.ajax({

                    url: epicMembership.ajaxUrl,

                    type: 'POST',

                    data: {

                        action: 'epic_membership_activate_free_tier',

                        tier_id: tierId,

                        nonce: epicMembership.nonce

                    },

                    success: function(response) {

                        if (response.success) {

                            // Show success message briefly, then refresh page

                            self.showSuccess(response.data.message || 'Membership activated successfully!');



                            // Refresh page after 2 seconds to show updated content

                            setTimeout(function() {

                                window.location.reload();

                            }, 2000);



                            resolve(response.data);

                        } else {

                            reject(new Error(response.data || 'Failed to activate free tier'));

                        }

                    },

                    error: function() {

                        reject(new Error('Network error while activating free tier'));

                    }

                });

            });

        },



        /**

         * Ensure theme compatibility for modals

         */

        ensureThemeCompatibility: function() {

            // Detect current theme for specific handling

            this.detectAndHandleTheme();



            // Remove any conflicting CSS that might hide modals

            var conflictingStyles = [

                'overflow: hidden',

                'display: none',

                'visibility: hidden',

                'opacity: 0',

                'z-index: -1'

            ];



            // Ensure body can show modals

            $('body').css({

                'position': 'relative',

                'overflow-x': 'hidden'

            });



            // Remove any theme-specific modal hiding classes

            $('body').removeClass('modal-hidden theme-modal-hidden no-modal no-scroll modal-disabled');



            // Clear any existing modals to prevent conflicts

            $('.epic-membership-modal-overlay').remove();



            // Reset modal state

            this.modalOpen = false;



            // No body classes to remove since we don't prevent scrolling



            // Handle theme-specific CSS conflicts

            this.handleThemeSpecificConflicts();

        },



        /**

         * Detect current theme and apply specific handling

         */

        detectAndHandleTheme: function() {

            var bodyClasses = $('body').attr('class') || '';

            var htmlClasses = $('html').attr('class') || '';

            var allClasses = bodyClasses + ' ' + htmlClasses;



            // Store detected theme info

            this.themeInfo = {

                classes: allClasses,

                isBlockTheme: $('body').hasClass('wp-site-blocks'),

                hasCustomizer: typeof wp !== 'undefined' && wp.customize,

                isRTL: $('body').hasClass('rtl'),

                hasAdminBar: $('#wpadminbar').length > 0

            };



            // Log theme detection for debugging

            console.log('Epic Membership: Detected theme info:', this.themeInfo);



            // Apply theme-specific fixes

            if (allClasses.includes('elementor')) {

                this.handleElementorTheme();

            }

            if (allClasses.includes('divi')) {

                this.handleDiviTheme();

            }

            if (allClasses.includes('avada')) {

                this.handleAvadaTheme();

            }

            if (allClasses.includes('astra')) {

                this.handleAstraTheme();

            }

            if (allClasses.includes('generatepress')) {

                this.handleGeneratePressTheme();

            }

            if (allClasses.includes('oceanwp')) {

                this.handleOceanWPTheme();

            }

        },



        /**

         * Handle theme-specific CSS conflicts

         */

        handleThemeSpecificConflicts: function() {

            // Remove transform properties that can hide modals

            var problematicSelectors = [

                'body', 'html', '#page', '#main', '.site', '.site-content',

                '.entry-content', '.post-content', '.page-content'

            ];



            problematicSelectors.forEach(function(selector) {

                var $element = $(selector);

                if ($element.length) {

                    // Store original transform for restoration later

                    var originalTransform = $element.css('transform');

                    if (originalTransform && originalTransform !== 'none') {

                        $element.data('epic-original-transform', originalTransform);

                        $element.css('transform', 'none');

                    }

                }

            });



            // Handle CSS Grid containers that might interfere

            $('.wp-site-blocks, .wp-block-group, .entry-content').each(function() {

                var $this = $(this);

                var display = $this.css('display');

                if (display === 'grid' || display === 'subgrid') {

                    $this.data('epic-original-display', display);

                    $this.css('display', 'block');

                }

            });

        },



        /**

         * Handle Elementor theme specific issues

         */

        handleElementorTheme: function() {

            // Elementor can interfere with fixed positioning

            $('.elementor-section, .elementor-container').css('transform', 'none');

            $('body').addClass('epic-modal-elementor-fix');

        },



        /**

         * Handle Divi theme specific issues

         */

        handleDiviTheme: function() {

            // Divi uses transforms that can hide modals

            $('#et-main-area, .et_pb_section').css('transform', 'none');

            $('body').addClass('epic-modal-divi-fix');

        },



        /**

         * Handle Avada theme specific issues

         */

        handleAvadaTheme: function() {

            // Avada can have z-index conflicts

            $('.fusion-header, .fusion-main').css('z-index', '999');

            $('body').addClass('epic-modal-avada-fix');

        },



        /**

         * Handle Astra theme specific issues

         */

        handleAstraTheme: function() {

            // Astra generally works well, but ensure no conflicts

            $('.ast-container, .site-content').css('transform', 'none');

            $('body').addClass('epic-modal-astra-fix');

        },



        /**

         * Handle GeneratePress theme specific issues

         */

        handleGeneratePressTheme: function() {

            // GeneratePress can have container issues

            $('.site-content, .inside-article').css('transform', 'none');

            $('body').addClass('epic-modal-generatepress-fix');

        },



        /**

         * Handle OceanWP theme specific issues

         */

        handleOceanWPTheme: function() {

            // OceanWP can have overlay conflicts

            $('.oceanwp-modal, .sidr-overlay').css('z-index', '999');

            $('body').addClass('epic-modal-oceanwp-fix');

        },



        /**

         * Force modal positioning for maximum theme compatibility

         */

        forceModalPositioning: function(modal) {

            if (!modal || !modal.length) return;



            var overlay = modal.closest('.epic-membership-modal-overlay');

            if (!overlay.length) return;



            // Enhanced positioning with fallbacks

            this.applyModalPositioning(overlay, modal);



            // Verify modal visibility and apply fallbacks if needed

            this.verifyModalVisibility(overlay, modal);

        },



        /**

         * Apply modal positioning without background overlay

         */

        applyModalPositioning: function(overlay, modal) {

            var self = this;



            // Positioning overlay with flexbox for centering

            overlay.css({

                'position': 'fixed',

                'top': '0',

                'left': '0',

                'right': '0',

                'bottom': '0',

                'width': '100vw',

                'height': '100vh',

                'z-index': '2147483647',

                'display': 'flex',

                'align-items': 'center',

                'justify-content': 'center',

                'background': 'transparent', // No background overlay

                'visibility': 'visible',

                'opacity': '1',

                'pointer-events': 'auto',

                'margin': '0',

                'padding': '20px',

                'box-sizing': 'border-box',

                'transform': 'none'

            });



            // Simple modal popup - flexbox will handle centering

            modal.css({

                'position': 'relative',

                'z-index': '2147483647',

                'background': 'white',

                'border-radius': '12px',

                'max-width': '500px',

                'width': '100%',

                'max-height': '90vh',

                'margin': '0',

                'display': 'block',

                'visibility': 'visible',

                'opacity': '1',

                'transform': 'none',

                'float': 'none',

                'clear': 'both',

                'overflow-y': 'auto',

                'top': 'auto',

                'left': 'auto'

            });



            // Only apply admin bar adjustment if needed

            if (this.themeInfo && this.themeInfo.hasAdminBar && $('#wpadminbar').is(':visible')) {

                // For admin bar, we still want full coverage but account for it

                overlay.css({

                    'top': '0', // Keep full coverage

                    'height': '100vh' // Keep full height

                });

            }



            // RTL support

            if (this.themeInfo && this.themeInfo.isRTL) {

                modal.css('direction', 'rtl');

            }

        },



        /**

         * Verify modal visibility and apply fallbacks

         */

        verifyModalVisibility: function(overlay, modal) {

            var self = this;



            // Multiple verification attempts with increasing delays

            var verificationAttempts = [10, 50, 100, 250];



            verificationAttempts.forEach(function(delay, index) {

                setTimeout(function() {

                    self.performVisibilityCheck(overlay, modal, index === verificationAttempts.length - 1);

                }, delay);

            });

        },



        /**

         * Perform visibility check and apply fixes

         */

        performVisibilityCheck: function(overlay, modal, isFinalAttempt) {

            // Check if elements are hidden

            if (overlay.is(':hidden') || modal.is(':hidden')) {

                console.log('Epic Membership: Modal hidden, forcing visibility');

                overlay.show().css('display', 'block');

                modal.show();

            }



            // Check computed styles

            var overlayDisplay = overlay.css('display');

            var modalDisplay = modal.css('display');

            var overlayVisibility = overlay.css('visibility');

            var modalVisibility = modal.css('visibility');



            if (overlayDisplay === 'none' || modalDisplay === 'none' ||

                overlayVisibility === 'hidden' || modalVisibility === 'hidden') {



                console.log('Epic Membership: Modal styles overridden, applying !important fixes');



                // Apply nuclear option - inline styles with !important

                overlay.attr('style', overlay.attr('style') + '; display: block !important; visibility: visible !important; opacity: 1 !important;');

                modal.attr('style', modal.attr('style') + '; display: block !important; visibility: visible !important; opacity: 1 !important;');

            }



            // Final fallback - create new modal if still not visible

            if (isFinalAttempt && (overlay.is(':hidden') || modal.is(':hidden'))) {

                console.log('Epic Membership: Modal still hidden, triggering fallback');

                this.triggerModalFallback(overlay, modal);

            }

        },



        /**

         * Trigger fallback modal creation

         */

        triggerModalFallback: function(originalOverlay, originalModal) {

            console.log('Epic Membership: Creating fallback modal');



            // Remove original modal

            originalOverlay.remove();



            // Create a simple fallback modal with transparent overlay

            var fallbackModal = $('<div class="epic-membership-modal-overlay epic-membership-modal-fallback">' +

                '<div class="epic-membership-modal">' +

                '<div class="epic-membership-modal-header">' +

                '<h3>Upgrade Required</h3>' +

                '<button class="epic-membership-modal-close">&times;</button>' +

                '</div>' +

                '<div class="epic-membership-modal-content">' +

                '<p>Premium membership required to access this content.</p>' +

                '<div class="epic-membership-modal-actions">' +

                '<button class="epic-membership-upgrade-button">Upgrade Now</button>' +

                '</div>' +

                '</div>' +

                '</div>' +

                '</div>');



            $('body').append(fallbackModal);



            // Apply positioning without background overlay

            fallbackModal.css({

                'position': 'fixed',

                'top': '0',

                'left': '0',

                'right': '0',

                'bottom': '0',

                'width': '100vw',

                'height': '100vh',

                'background': 'transparent', // No background overlay

                'z-index': '2147483647',

                'display': 'block'

            });



            // Bind events to fallback modal

            this.bindModalEvents(fallbackModal);

        },



        /**

         * Create fallback modal HTML

         */

        createFallbackModal: function() {

            var strings = epicMembership.strings || {};

            var upgradeConfirmTitle = strings.upgradeConfirmTitle || 'Confirm Membership Upgrade';

            var upgradeConfirmMessage = strings.upgradeConfirmMessage || 'Please confirm your membership upgrade.';

            var confirmUpgrade = strings.confirmUpgrade || 'Confirm Upgrade';

            var cancel = strings.cancel || 'Cancel';



            return $('<div class="epic-membership-modal-fallback">' +

                '<h3>' + upgradeConfirmTitle + '</h3>' +

                '<p>' + upgradeConfirmMessage + '</p>' +

                '<div style="text-align: right; margin-top: 20px;">' +

                '<button class="epic-membership-modal-cancel" style="margin-right: 10px; padding: 8px 16px;">' + cancel + '</button>' +

                '<button class="epic-membership-upgrade-confirm" style="padding: 8px 16px; background: #0073aa; color: white; border: none;">' + confirmUpgrade + '</button>' +

                '</div>' +

                '</div>');

        },



        // Removed problematic universal modal system - will be replaced with Ko-fi style implementation



        /**

         * Check if modal is actually visible

         */

        isModalVisible: function() {

            var modal = $('.epic-membership-modal-overlay, .epic-membership-modal-fallback, .epic-membership-modal-table, .epic-membership-modal-absolute');



            if (modal.length === 0) {

                return false;

            }



            // Check if element is in viewport and visible

            var rect = modal[0].getBoundingClientRect();

            var isInViewport = rect.top >= 0 && rect.left >= 0 &&

                              rect.bottom <= window.innerHeight &&

                              rect.right <= window.innerWidth;



            var isVisible = modal.is(':visible') &&

                           modal.css('visibility') !== 'hidden' &&

                           modal.css('opacity') !== '0' &&

                           modal.css('display') !== 'none';



            return isVisible && (isInViewport || rect.width > 0 || rect.height > 0);

        },



        // Removed problematic strategy functions - will be replaced with Ko-fi style implementation



        // Removed remaining problematic strategy functions



        /**

         * Advanced fallback system for extreme edge cases

         */

        initAdvancedFallbacks: function() {

            var self = this;



            // Monitor for theme changes

            this.monitorThemeChanges();



            // Set up emergency modal system

            this.setupEmergencyModal();



            // Monitor viewport changes

            this.monitorViewportChanges();



            // Set up accessibility fallbacks

            this.setupAccessibilityFallbacks();

        },



        /**

         * Monitor for theme changes that might affect modals

         */

        monitorThemeChanges: function() {

            var self = this;

            var lastBodyClass = $('body').attr('class');



            setInterval(function() {

                var currentBodyClass = $('body').attr('class');

                if (currentBodyClass !== lastBodyClass) {

                    console.log('Epic Membership: Theme change detected, updating compatibility');

                    self.ensureThemeCompatibility();

                    lastBodyClass = currentBodyClass;

                }

            }, 5000);

        },



        /**

         * Set up emergency modal system for critical failures

         */

        setupEmergencyModal: function() {

            var self = this;



            // Create emergency modal container

            if ($('#epic-emergency-modal-container').length === 0) {

                $('body').append('<div id="epic-emergency-modal-container" style="display: none;"></div>');

            }



            // Emergency modal function

            window.epicEmergencyModal = function(message, callback) {

                var container = $('#epic-emergency-modal-container');



                var emergencyModal = $('<div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,0,0,0.9); z-index: 2147483647; display: table; font-family: Arial, sans-serif;">' +

                    '<div style="display: table-cell; vertical-align: middle; text-align: center;">' +

                    '<div style="display: inline-block; background: white; padding: 40px; border: 5px solid red; max-width: 500px; width: 90%;">' +

                    '<h2 style="color: red; margin: 0 0 20px 0;">Epic Membership - Emergency Modal</h2>' +

                    '<p style="margin: 0 0 30px 0; font-size: 16px;">' + message + '</p>' +

                    '<button onclick="epicEmergencyModalClose()" style="padding: 10px 20px; margin-right: 10px; background: #ccc; border: none; cursor: pointer;">Cancel</button>' +

                    '<button onclick="epicEmergencyModalConfirm()" style="padding: 10px 20px; background: red; color: white; border: none; cursor: pointer;">Confirm</button>' +

                    '</div>' +

                    '</div>' +

                    '</div>');



                container.html(emergencyModal).show();



                window.epicEmergencyModalConfirm = function() {

                    container.hide();

                    if (callback) callback(true);

                };



                window.epicEmergencyModalClose = function() {

                    container.hide();

                    if (callback) callback(false);

                };

            };

        },



        /**

         * Monitor viewport changes for responsive adjustments

         */

        monitorViewportChanges: function() {

            var self = this;

            var lastViewport = {

                width: window.innerWidth,

                height: window.innerHeight

            };



            $(window).on('resize orientationchange', function() {

                var currentViewport = {

                    width: window.innerWidth,

                    height: window.innerHeight

                };



                // Significant viewport change detected

                if (Math.abs(currentViewport.width - lastViewport.width) > 100 ||

                    Math.abs(currentViewport.height - lastViewport.height) > 100) {



                    console.log('Epic Membership: Significant viewport change detected');



                    // Reposition any open modals

                    var openModals = $('.epic-membership-modal-overlay, .epic-membership-modal-absolute, .epic-membership-modal-table');

                    if (openModals.length > 0) {

                        setTimeout(function() {

                            self.repositionModals(openModals);

                        }, 100);

                    }

                }



                lastViewport = currentViewport;

            });

        },



        /**

         * Reposition modals after viewport changes

         */

        repositionModals: function(modals) {

            var self = this;



            modals.each(function() {

                var modal = $(this);



                if (modal.hasClass('epic-membership-modal-overlay')) {

                    self.forceModalPositioning(modal.find('.epic-membership-modal'));

                } else if (modal.hasClass('epic-membership-modal-absolute')) {

                    // Reapply absolute positioning

                    modal.css({

                        'width': window.innerWidth + 'px',

                        'height': window.innerHeight + 'px'

                    });

                }

            });

        },



        /**

         * Set up accessibility fallbacks

         */

        setupAccessibilityFallbacks: function() {

            var self = this;



            // Keyboard navigation fallback

            $(document).on('keydown', function(e) {

                if (self.modalOpen) {

                    // Tab trapping for modals

                    if (e.keyCode === 9) { // Tab key

                        var modal = $('.epic-membership-modal-overlay, .epic-membership-modal-absolute, .epic-membership-modal-table, .epic-membership-modal-fallback').filter(':visible');

                        if (modal.length > 0) {

                            var focusableElements = modal.find('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');

                            var firstElement = focusableElements.first();

                            var lastElement = focusableElements.last();



                            if (e.shiftKey) {

                                if (document.activeElement === firstElement[0]) {

                                    e.preventDefault();

                                    lastElement.focus();

                                }

                            } else {

                                if (document.activeElement === lastElement[0]) {

                                    e.preventDefault();

                                    firstElement.focus();

                                }

                            }

                        }

                    }

                }

            });



            // Screen reader announcements

            this.setupScreenReaderSupport();

        },



        /**

         * Set up screen reader support

         */

        setupScreenReaderSupport: function() {

            // Create aria-live region for announcements

            if ($('#epic-membership-announcements').length === 0) {

                $('body').append('<div id="epic-membership-announcements" aria-live="polite" aria-atomic="true" style="position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;"></div>');

            }

        },



        /**

         * Announce to screen readers

         */

        announceToScreenReader: function(message) {

            $('#epic-membership-announcements').text(message);

        },



        /**

         * Detect if user prefers reduced motion

         */

        prefersReducedMotion: function() {

            return window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        },



        /**

         * Detect if user is using high contrast mode

         */

        isHighContrastMode: function() {

            // Create test element to detect high contrast

            var testElement = $('<div style="position: absolute; left: -10000px; background-color: rgb(31, 31, 31); color: rgb(255, 255, 255);"></div>');

            $('body').append(testElement);



            var computedStyle = window.getComputedStyle(testElement[0]);

            var isHighContrast = computedStyle.backgroundColor === computedStyle.color;



            testElement.remove();

            return isHighContrast;

        },



        /**

         * Clean up modals and reset state

         */

        cleanupModals: function() {

            // Remove all modal elements (all strategies)

            $('.epic-membership-modal-overlay, .epic-membership-modal-fallback, .epic-membership-modal-absolute, .epic-membership-modal-table, .epic-test-element').remove();



            // No body classes to remove since we don't prevent scrolling

            $('body').removeClass('epic-modal-elementor-fix epic-modal-divi-fix epic-modal-avada-fix epic-modal-astra-fix epic-modal-generatepress-fix epic-modal-oceanwp-fix');



            // Restore original styles

            this.restoreOriginalStyles();



            // Reset modal state

            this.modalOpen = false;



            // Remove event listeners

            $(document).off('keydown.epic-modal');



            console.log('Epic Membership: All modals cleaned up');

        },



        /**

         * Restore original element styles

         */

        restoreOriginalStyles: function() {

            // Restore transforms

            var problematicSelectors = [

                'body', 'html', '#page', '#main', '.site', '.site-content',

                '.entry-content', '.post-content', '.page-content'

            ];



            problematicSelectors.forEach(function(selector) {

                var $element = $(selector);

                if ($element.length) {

                    var originalTransform = $element.data('epic-original-transform');

                    if (originalTransform) {

                        $element.css('transform', originalTransform);

                        $element.removeData('epic-original-transform');

                    }

                }

            });



            // Restore display properties

            $('.wp-site-blocks, .wp-block-group, .entry-content').each(function() {

                var $this = $(this);

                var originalDisplay = $this.data('epic-original-display');

                if (originalDisplay) {

                    $this.css('display', originalDisplay);

                    $this.removeData('epic-original-display');

                }

            });

        }

    };

    

    // Initialize when document is ready

    $(document).ready(function() {

        EpicMembership.init();

    });



    // Global testing functions for console access

    window.EpicMembershipTester = {

        testModal: function() {

            console.log('=== Epic Membership Modal Test ===');

            EpicMembership.testModal();

        },



        debugEnvironment: function() {

            console.log('=== Epic Membership Environment Debug ===');

            EpicMembership.debugModalEnvironment();

        },



        forceModal: function(tierId, tierName) {

            console.log('=== Epic Membership Force Modal ===');

            EpicMembership.cleanupModals();

            EpicMembership.showUpgradeConfirmation(tierId || 'test', tierName || 'Test Tier');

        },



        analyzeTheme: function() {

            console.log('=== Epic Membership Theme Analysis ===');



            var analysis = {

                theme: {

                    bodyClasses: $('body').attr('class'),

                    htmlClasses: $('html').attr('class')

                },

                viewport: {

                    width: window.innerWidth,

                    height: window.innerHeight

                },

                potentialConflicts: {

                    highZIndex: $('*').filter(function() {

                        var zIndex = parseInt($(this).css('z-index'));

                        return zIndex > 1000000;

                    }).length,

                    transforms: $('body, html, #page, #main').filter(function() {

                        var transform = $(this).css('transform');

                        return transform && transform !== 'none';

                    }).length,

                    overflowHidden: $('body, html').filter(function() {

                        return $(this).css('overflow') === 'hidden';

                    }).length

                },

                modalElements: {

                    existing: $('.epic-membership-modal-overlay').length,

                    bodyClass: $('body').hasClass('epic-membership-modal-open')

                }

            };



            console.log('Theme Analysis Results:', analysis);

            return analysis;

        },



        testCSSOverrides: function() {

            console.log('=== Epic Membership CSS Override Test ===');



            // Create test element

            var testElement = $('<div class="epic-membership-modal-overlay epic-test-element">' +

                '<div class="epic-membership-modal">' +

                '<div style="padding: 20px; text-align: center;">' +

                '<h3>CSS Override Test</h3>' +

                '<p>If you can see this, CSS overrides are working.</p>' +

                '<button onclick="EpicMembershipTester.removeTestElement()">Close Test</button>' +

                '</div>' +

                '</div>' +

                '</div>');



            $('body').append(testElement);



            // Apply positioning

            testElement.css({

                'position': 'fixed',

                'top': '0',

                'left': '0',

                'width': '100vw',

                'height': '100vh',

                'z-index': '2147483647',

                'display': 'block',

                'background': 'rgba(255, 0, 0, 0.8)'

            });



            console.log('CSS test element created. It should appear as a red overlay.');

        },



        removeTestElement: function() {

            $('.epic-test-element').remove();

            console.log('Test element removed.');

        },



        getDebugInfo: function() {

            return window.epicMembershipDebugInfo || 'No debug info available. Run a modal test first.';

        }

    };



    // Make EpicMembership globally accessible for debugging

    window.EpicMembership = EpicMembership;

    

    // Make EpicMembership globally available

    // Removed problematic enhanced modal implementation - will be replaced with clean Ko-fi style implementation

    // Removed enhanced modal event binding functions

    // Removed problematic upgrade confirmation processing and overrides

    window.EpicMembership = EpicMembership;



})(jQuery);

