/**

 * Epic Membership Float Over JavaScript

 * 

 * @package Epic_Membership

 * @since 1.0.0

 */



jQuery(document).ready(function($) {

    var delay = epicFloatOverData.delay * 1000; // Convert to milliseconds

    var scrollPosition = 0;

    var floatOverShown = false;



    // Store scroll position before locking

    scrollPosition = window.pageYOffset;

    

    // Show the float over immediately

    $('#epic-float-over').fadeIn();

    $('#epic-float-over-text').text(epicFloatOverData.customText);

    $('body').addClass('epic-no-scroll');

    floatOverShown = true;



    // Show the continue button after delay

    setTimeout(function() {

        $('#epic-float-over-button').fadeIn().on('click', function() {

            // Get random link via AJAX

            $.ajax({

                url: epicFloatOverData.ajaxUrl,

                type: 'POST',

                data: {

                    action: 'get_float_over_link',

                    nonce: epicFloatOverData.nonce

                },

                success: function(response) {

                    if (response.success && response.data.link) {

                        // Hide the float over

                        $('#epic-float-over').fadeOut(400, function() {

                            $('body').removeClass('epic-no-scroll');

                            // Restore scroll position

                            window.scrollTo(0, scrollPosition);

                            floatOverShown = false;

                        });

                        

                        // Open the link in new tab

                        if (response.data.link !== '#') {

                            window.open(response.data.link, '_blank');

                        }

                    } else {

                        // If no link available, just hide the overlay

                        $('#epic-float-over').fadeOut(400, function() {

                            $('body').removeClass('epic-no-scroll');

                            window.scrollTo(0, scrollPosition);

                            floatOverShown = false;

                        });

                    }

                },

                error: function() {

                    // On error, just hide the overlay

                    $('#epic-float-over').fadeOut(400, function() {

                        $('body').removeClass('epic-no-scroll');

                        window.scrollTo(0, scrollPosition);

                        floatOverShown = false;

                    });

                }

            });

        });

    }, delay);



    // Prevent scrolling while overlay is shown

    $(window).on('scroll', function() {

        if (floatOverShown) {

            window.scrollTo(0, scrollPosition);

        }

    });



    // Handle escape key to close overlay (optional feature)

    $(document).on('keydown', function(e) {

        if (e.keyCode === 27 && floatOverShown) { // Escape key

            $('#epic-float-over').fadeOut(400, function() {

                $('body').removeClass('epic-no-scroll');

                window.scrollTo(0, scrollPosition);

                floatOverShown = false;

            });

        }

    });

});

