<?php

/**

 * Admin dashboard page for Epic Membership Plugin

 */



if (!defined('ABSPATH')) {

    exit;

}



// Initialize managers

$users_manager = new Epic_Membership_Users();

$tiers_manager = new Epic_Membership_Tiers();

$database = new Epic_Membership_Database();



// Get statistics

$stats = $users_manager->get_membership_statistics();



// Get recent activity

global $wpdb;

$memberships_table = $database->get_table('user_memberships');

$tiers_table = $database->get_table('tiers');

$access_logs_table = $database->get_table('access_logs');



$recent_memberships = $wpdb->get_results("

    SELECT m.*, u.display_name, u.user_email, t.name as tier_name, t.slug as tier_slug

    FROM $memberships_table m

    LEFT JOIN {$wpdb->users} u ON m.user_id = u.ID

    LEFT JOIN $tiers_table t ON m.tier_id = t.id

    ORDER BY m.created_at DESC

    LIMIT 10

");



$recent_access = $wpdb->get_results("

    SELECT l.*, p.post_title, u.display_name

    FROM $access_logs_table l

    LEFT JOIN {$wpdb->posts} p ON l.post_id = p.ID

    LEFT JOIN {$wpdb->users} u ON l.user_id = u.ID

    ORDER BY l.accessed_at DESC

    LIMIT 10

");



// Get expiring memberships

$expiring_memberships = $wpdb->get_results("

    SELECT m.*, u.display_name, u.user_email, t.name as tier_name

    FROM $memberships_table m

    LEFT JOIN {$wpdb->users} u ON m.user_id = u.ID

    LEFT JOIN $tiers_table t ON m.tier_id = t.id

    WHERE m.is_active = 1 

    AND m.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)

    ORDER BY m.end_date ASC

    LIMIT 10

");



?>



<div class="wrap">

    <h1><?php _e('Epic Membership Dashboard', 'epic-membership'); ?></h1>

    

    <!-- Statistics Overview -->

    <div class="epic-membership-dashboard-stats">

        <div class="stats-grid">

            <div class="stat-card">

                <div class="stat-icon">👥</div>

                <div class="stat-content">

                    <div class="stat-number"><?php echo number_format($stats['total_users']); ?></div>

                    <div class="stat-label"><?php _e('Total Users', 'epic-membership'); ?></div>

                </div>

            </div>

            

            <div class="stat-card active">

                <div class="stat-icon">✅</div>

                <div class="stat-content">

                    <div class="stat-number"><?php echo number_format($stats['active_memberships']); ?></div>

                    <div class="stat-label"><?php _e('Active Memberships', 'epic-membership'); ?></div>

                </div>

            </div>

            

            <div class="stat-card expired">

                <div class="stat-icon">❌</div>

                <div class="stat-content">

                    <div class="stat-number"><?php echo number_format($stats['expired_memberships']); ?></div>

                    <div class="stat-label"><?php _e('Expired Memberships', 'epic-membership'); ?></div>

                </div>

            </div>

            

            <div class="stat-card warning">

                <div class="stat-icon">⚠️</div>

                <div class="stat-content">

                    <div class="stat-number"><?php echo number_format($stats['expiring_soon']); ?></div>

                    <div class="stat-label"><?php _e('Expiring Soon', 'epic-membership'); ?></div>

                </div>

            </div>

        </div>

    </div>

    

    <!-- Tier Breakdown Chart -->

    <div class="epic-membership-dashboard-section">

        <h2><?php _e('Membership Tier Breakdown', 'epic-membership'); ?></h2>

        <div class="tier-breakdown-container">

            <div class="tier-breakdown-chart">

                <canvas id="tierBreakdownChart" width="400" height="200"></canvas>

            </div>

            <div class="tier-breakdown-legend">

                <?php foreach ($stats['tier_breakdown'] as $tier): ?>

                    <div class="tier-legend-item">

                        <span class="tier-color tier-level-<?php echo esc_attr($tier->level); ?>"></span>

                        <span class="tier-name"><?php echo esc_html($tier->name); ?></span>

                        <span class="tier-count"><?php echo intval($tier->member_count); ?></span>

                    </div>

                <?php endforeach; ?>

            </div>

        </div>

    </div>

    

    <!-- Quick Actions -->

    <div class="epic-membership-dashboard-section">

        <h2><?php _e('Quick Actions', 'epic-membership'); ?></h2>

        <div class="quick-actions-grid">

            <a href="<?php echo admin_url('admin.php?page=epic-membership-tiers&action=add'); ?>" class="quick-action-card">

                <div class="action-icon">➕</div>

                <div class="action-title"><?php _e('Add New Tier', 'epic-membership'); ?></div>

                <div class="action-description"><?php _e('Create a new membership tier', 'epic-membership'); ?></div>

            </a>

            

            <a href="<?php echo admin_url('admin.php?page=epic-membership-users'); ?>" class="quick-action-card">

                <div class="action-icon">👤</div>

                <div class="action-title"><?php _e('Manage Users', 'epic-membership'); ?></div>

                <div class="action-description"><?php _e('Update user memberships', 'epic-membership'); ?></div>

            </a>

            

            <a href="<?php echo admin_url('admin.php?page=epic-membership-content'); ?>" class="quick-action-card">

                <div class="action-icon">📄</div>

                <div class="action-title"><?php _e('Protected Content', 'epic-membership'); ?></div>

                <div class="action-description"><?php _e('Manage content access', 'epic-membership'); ?></div>

            </a>

            

            <a href="<?php echo admin_url('admin.php?page=epic-membership-settings'); ?>" class="quick-action-card">

                <div class="action-icon">⚙️</div>

                <div class="action-title"><?php _e('Settings', 'epic-membership'); ?></div>

                <div class="action-description"><?php _e('Configure plugin settings', 'epic-membership'); ?></div>

            </a>

        </div>

    </div>

    

    <!-- Two Column Layout -->

    <div class="epic-membership-dashboard-columns">

        <!-- Left Column -->

        <div class="dashboard-column-left">

            <!-- Recent Memberships -->

            <div class="epic-membership-dashboard-section">

                <h2><?php _e('Recent Memberships', 'epic-membership'); ?></h2>

                <?php if (empty($recent_memberships)): ?>

                    <p><?php _e('No recent membership activity.', 'epic-membership'); ?></p>

                <?php else: ?>

                    <div class="recent-memberships-list">

                        <?php foreach ($recent_memberships as $membership): ?>

                            <div class="membership-item <?php echo $membership->is_active ? 'active' : 'inactive'; ?>">

                                <div class="membership-user">

                                    <strong><?php echo esc_html($membership->display_name); ?></strong>

                                    <div class="user-email"><?php echo esc_html($membership->user_email); ?></div>

                                </div>

                                <div class="membership-tier">

                                    <span class="tier-badge tier-<?php echo esc_attr($membership->tier_slug); ?>">

                                        <?php echo esc_html($membership->tier_name); ?>

                                    </span>

                                </div>

                                <div class="membership-date">

                                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($membership->created_at))); ?>

                                </div>

                            </div>

                        <?php endforeach; ?>

                    </div>

                    <div class="section-footer">

                        <a href="<?php echo admin_url('admin.php?page=epic-membership-users'); ?>" class="button">

                            <?php _e('View All Users', 'epic-membership'); ?>

                        </a>

                    </div>

                <?php endif; ?>

            </div>

            

            <!-- Expiring Memberships -->

            <?php if (!empty($expiring_memberships)): ?>

                <div class="epic-membership-dashboard-section warning">

                    <h2><?php _e('Expiring Memberships', 'epic-membership'); ?></h2>

                    <div class="expiring-memberships-list">

                        <?php foreach ($expiring_memberships as $membership): ?>

                            <div class="membership-item expiring">

                                <div class="membership-user">

                                    <strong><?php echo esc_html($membership->display_name); ?></strong>

                                    <div class="user-email"><?php echo esc_html($membership->user_email); ?></div>

                                </div>

                                <div class="membership-tier">

                                    <span class="tier-badge"><?php echo esc_html($membership->tier_name); ?></span>

                                </div>

                                <div class="membership-expiry">

                                    <?php 

                                    $days_left = ceil((strtotime($membership->end_date) - time()) / (24 * 60 * 60));

                                    printf(_n('%d day left', '%d days left', $days_left, 'epic-membership'), $days_left);

                                    ?>

                                </div>

                            </div>

                        <?php endforeach; ?>

                    </div>

                    <div class="section-footer">

                        <a href="<?php echo admin_url('admin.php?page=epic-membership-users&status=expiring'); ?>" class="button">

                            <?php _e('Manage Expiring', 'epic-membership'); ?>

                        </a>

                    </div>

                </div>

            <?php endif; ?>

        </div>

        

        <!-- Right Column -->

        <div class="dashboard-column-right">

            <!-- Recent Access Activity -->

            <div class="epic-membership-dashboard-section">

                <h2><?php _e('Recent Access Activity', 'epic-membership'); ?></h2>

                <?php if (empty($recent_access)): ?>

                    <p><?php _e('No recent access activity.', 'epic-membership'); ?></p>

                <?php else: ?>

                    <div class="recent-access-list">

                        <?php foreach ($recent_access as $access): ?>

                            <div class="access-item <?php echo $access->access_granted ? 'granted' : 'denied'; ?>">

                                <div class="access-icon">

                                    <?php echo $access->access_granted ? '✅' : '❌'; ?>

                                </div>

                                <div class="access-details">

                                    <div class="access-content">

                                        <strong><?php echo esc_html($access->post_title ?: 'Unknown Content'); ?></strong>

                                    </div>

                                    <div class="access-user">

                                        <?php echo esc_html($access->display_name ?: 'Guest'); ?>

                                    </div>

                                    <div class="access-time">

                                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($access->accessed_at))); ?>

                                    </div>

                                </div>

                            </div>

                        <?php endforeach; ?>

                    </div>

                    <div class="section-footer">

                        <a href="<?php echo admin_url('admin.php?page=epic-membership-analytics'); ?>" class="button">

                            <?php _e('View Analytics', 'epic-membership'); ?>

                        </a>

                    </div>

                <?php endif; ?>

            </div>

            

            <!-- System Status -->

            <div class="epic-membership-dashboard-section">

                <h2><?php _e('System Status', 'epic-membership'); ?></h2>

                <div class="system-status-list">

                    <div class="status-item">

                        <span class="status-label"><?php _e('Plugin Version:', 'epic-membership'); ?></span>

                        <span class="status-value"><?php echo EPIC_MEMBERSHIP_VERSION; ?></span>

                    </div>

                    <div class="status-item">

                        <span class="status-label"><?php _e('Database Version:', 'epic-membership'); ?></span>

                        <span class="status-value"><?php echo get_option('epic_membership_db_version', '1.0.0'); ?></span>

                    </div>

                    <div class="status-item">

                        <span class="status-label"><?php _e('Timezone:', 'epic-membership'); ?></span>

                        <span class="status-value"><?php echo wp_timezone_string(); ?></span>

                    </div>

                    <div class="status-item">

                        <span class="status-label"><?php _e('Cron Status:', 'epic-membership'); ?></span>

                        <span class="status-value">

                            <?php echo wp_next_scheduled('epic_membership_check_expired_memberships') ? 

                                '<span class="status-ok">Active</span>' : 

                                '<span class="status-error">Inactive</span>'; ?>

                        </span>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>



<style>

.epic-membership-dashboard-stats {

    margin: 20px 0;

}



.stats-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    gap: 20px;

    margin-bottom: 30px;

}



.stat-card {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    display: flex;

    align-items: center;

    gap: 15px;

    box-shadow: 0 1px 1px rgba(0,0,0,0.04);

}



.stat-card.active {

    border-color: #00a32a;

    background: #f6fff6;

}



.stat-card.expired {

    border-color: #d63638;

    background: #fff6f6;

}



.stat-card.warning {

    border-color: #dba617;

    background: #fcf9e8;

}



.stat-icon {

    font-size: 32px;

}



.stat-number {

    font-size: 32px;

    font-weight: bold;

    color: #1d2327;

    line-height: 1;

}



.stat-label {

    font-size: 13px;

    color: #646970;

    margin-top: 5px;

}



.epic-membership-dashboard-section {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    margin: 20px 0;

    box-shadow: 0 1px 1px rgba(0,0,0,0.04);

}



.epic-membership-dashboard-section h2 {

    margin: 0;

    padding: 15px 20px;

    border-bottom: 1px solid #c3c4c7;

    font-size: 16px;

    font-weight: 600;

}



.epic-membership-dashboard-section.warning h2 {

    background: #fcf9e8;

    color: #646970;

}



.tier-breakdown-container {

    display: flex;

    padding: 20px;

    gap: 20px;

}



.tier-breakdown-chart {

    flex: 1;

}



.tier-breakdown-legend {

    flex: 0 0 200px;

}



.tier-legend-item {

    display: flex;

    align-items: center;

    gap: 10px;

    margin-bottom: 10px;

}



.tier-color {

    width: 16px;

    height: 16px;

    border-radius: 50%;

}



.tier-color.tier-level-0 { background: #e0e0e0; }

.tier-color.tier-level-10 { background: #00a32a; }

.tier-color.tier-level-20 { background: #0073aa; }



.quick-actions-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    gap: 15px;

    padding: 20px;

}



.quick-action-card {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    text-decoration: none;

    color: inherit;

    transition: all 0.2s ease;

    text-align: center;

}



.quick-action-card:hover {

    border-color: #0073aa;

    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    color: inherit;

    text-decoration: none;

}



.action-icon {

    font-size: 32px;

    margin-bottom: 10px;

}



.action-title {

    font-weight: 600;

    margin-bottom: 5px;

}



.action-description {

    font-size: 13px;

    color: #646970;

}



.epic-membership-dashboard-columns {

    display: grid;

    grid-template-columns: 1fr 1fr;

    gap: 20px;

}



.membership-item, .access-item {

    display: flex;

    align-items: center;

    gap: 15px;

    padding: 15px 20px;

    border-bottom: 1px solid #f0f0f1;

}



.membership-item:last-child, .access-item:last-child {

    border-bottom: none;

}



.membership-item.inactive {

    opacity: 0.6;

}



.tier-badge {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

    background: #f0f0f1;

    color: #646970;

}



.user-email {

    font-size: 12px;

    color: #646970;

}



.section-footer {

    padding: 15px 20px;

    border-top: 1px solid #f0f0f1;

    text-align: center;

}



.access-item.granted .access-icon {

    color: #00a32a;

}



.access-item.denied .access-icon {

    color: #d63638;

}



.access-details {

    flex: 1;

}



.access-content {

    font-weight: 600;

}



.access-user, .access-time {

    font-size: 12px;

    color: #646970;

}



.system-status-list {

    padding: 20px;

}



.status-item {

    display: flex;

    justify-content: space-between;

    padding: 10px 0;

    border-bottom: 1px solid #f0f0f1;

}



.status-item:last-child {

    border-bottom: none;

}



.status-ok {

    color: #00a32a;

    font-weight: 600;

}



.status-error {

    color: #d63638;

    font-weight: 600;

}



@media (max-width: 768px) {

    .epic-membership-dashboard-columns {

        grid-template-columns: 1fr;

    }

    

    .tier-breakdown-container {

        flex-direction: column;

    }

    

    .stats-grid {

        grid-template-columns: 1fr;

    }

}

</style>



<script>

// Simple chart for tier breakdown (you can replace with Chart.js or similar)

document.addEventListener('DOMContentLoaded', function() {

    var canvas = document.getElementById('tierBreakdownChart');

    if (canvas) {

        var ctx = canvas.getContext('2d');

        

        // Sample data - replace with actual PHP data

        var tierData = <?php echo json_encode($stats['tier_breakdown']); ?>;

        

        // Simple pie chart implementation

        var total = tierData.reduce(function(sum, tier) {

            return sum + parseInt(tier.member_count);

        }, 0);

        

        if (total > 0) {

            var centerX = canvas.width / 2;

            var centerY = canvas.height / 2;

            var radius = Math.min(centerX, centerY) - 10;

            var currentAngle = 0;

            

            var colors = ['#e0e0e0', '#00a32a', '#0073aa', '#d63638', '#dba617'];

            

            tierData.forEach(function(tier, index) {

                var sliceAngle = (parseInt(tier.member_count) / total) * 2 * Math.PI;

                

                ctx.beginPath();

                ctx.moveTo(centerX, centerY);

                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);

                ctx.closePath();

                ctx.fillStyle = colors[index % colors.length];

                ctx.fill();

                

                currentAngle += sliceAngle;

            });

        } else {

            ctx.fillStyle = '#f0f0f1';

            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#646970';

            ctx.font = '16px Arial';

            ctx.textAlign = 'center';

            ctx.fillText('No data available', canvas.width / 2, canvas.height / 2);

        }

    }

});

</script>

