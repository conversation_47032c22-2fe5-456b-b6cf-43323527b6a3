<?php

/**

 * Admin page for managing membership tiers

 */



if (!defined('ABSPATH')) {

    exit;

}



// Initialize tiers manager

$tiers_manager = new Epic_Membership_Tiers();



// Handle actions

$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';

$tier_id = isset($_GET['tier_id']) ? intval($_GET['tier_id']) : 0;



// Get tier data for editing

$tier_data = null;

if ($action === 'edit' && $tier_id) {

    $tier_data = $tiers_manager->get_tier($tier_id);

    if (!$tier_data) {

        $action = 'list';

    }

}



?>



<div class="wrap">

    <h1 class="wp-heading-inline">

        <?php _e('Membership Tiers', 'epic-membership'); ?>

    </h1>

    

    <?php if ($action === 'list'): ?>

        <a href="<?php echo esc_url(add_query_arg('action', 'add')); ?>" class="page-title-action">

            <?php _e('Add New Tier', 'epic-membership'); ?>

        </a>

    <?php endif; ?>

    

    <hr class="wp-header-end">



    <?php if ($action === 'list'): ?>

        <!-- Tiers List -->

        <div class="epic-membership-tiers-list">

            <?php

            $tiers = $tiers_manager->get_all_tiers();

            if (empty($tiers)):

            ?>

                <div class="notice notice-info">

                    <p><?php _e('No membership tiers found. Create your first tier to get started.', 'epic-membership'); ?></p>

                </div>

            <?php else: ?>

                <table class="wp-list-table widefat fixed striped">

                    <thead>

                        <tr>

                            <th scope="col" class="manage-column column-name column-primary">

                                <?php _e('Name', 'epic-membership'); ?>

                            </th>

                            <th scope="col" class="manage-column column-level">

                                <?php _e('Level', 'epic-membership'); ?>

                            </th>

                            <th scope="col" class="manage-column column-price">

                                <?php _e('Price', 'epic-membership'); ?>

                            </th>

                            <th scope="col" class="manage-column column-duration">

                                <?php _e('Duration', 'epic-membership'); ?>

                            </th>

                            <th scope="col" class="manage-column column-members">

                                <?php _e('Active Members', 'epic-membership'); ?>

                            </th>

                            <th scope="col" class="manage-column column-status">

                                <?php _e('Status', 'epic-membership'); ?>

                            </th>

                        </tr>

                    </thead>

                    <tbody>

                        <?php foreach ($tiers as $tier): 

                            $stats = $tiers_manager->get_tier_stats($tier->id);

                        ?>

                            <tr class="<?php echo $tier->is_active ? '' : 'inactive'; ?>">

                                <td class="column-name column-primary">

                                    <strong>

                                        <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'tier_id' => $tier->id))); ?>">

                                            <?php echo esc_html($tier->name); ?>

                                        </a>

                                    </strong>

                                    <div class="row-actions">

                                        <span class="edit">

                                            <a href="<?php echo esc_url(add_query_arg(array('action' => 'edit', 'tier_id' => $tier->id))); ?>">

                                                <?php _e('Edit', 'epic-membership'); ?>

                                            </a>

                                        </span>

                                        <?php if ($tier->slug !== 'free'): // Don't allow deleting free tier ?>

                                            | <span class="delete">

                                                <a href="<?php echo esc_url(wp_nonce_url(

                                                    admin_url('admin-post.php?action=epic_membership_delete_tier&tier_id=' . $tier->id),

                                                    'epic_membership_delete_tier_' . $tier->id

                                                )); ?>" 

                                                   onclick="return confirm('<?php esc_attr_e('Are you sure you want to delete this tier?', 'epic-membership'); ?>')">

                                                    <?php _e('Delete', 'epic-membership'); ?>

                                                </a>

                                            </span>

                                        <?php endif; ?>

                                    </div>

                                    <button type="button" class="toggle-row">

                                        <span class="screen-reader-text"><?php _e('Show more details', 'epic-membership'); ?></span>

                                    </button>

                                </td>

                                <td class="column-level" data-colname="<?php esc_attr_e('Level', 'epic-membership'); ?>">

                                    <span class="tier-level-badge level-<?php echo esc_attr($tier->level); ?>">

                                        <?php echo esc_html($tier->level); ?>

                                    </span>

                                </td>

                                <td class="column-price" data-colname="<?php esc_attr_e('Price', 'epic-membership'); ?>">

                                    <?php if ($tier->price > 0): ?>

                                        <?php

                                        $currency = $tier->currency ?? 'USD';

                                        $currency_symbol = $currency === 'IDR' ? 'Rp ' : '$';

                                        $formatted_price = $currency === 'IDR' ? number_format($tier->price, 0, ',', '.') : number_format($tier->price, 2);

                                        ?>

                                        <span class="tier-price"><?php echo esc_html($currency_symbol . $formatted_price); ?></span>

                                        <small class="currency-code" style="color: #666; margin-left: 5px;"><?php echo esc_html($currency); ?></small>

                                    <?php else: ?>

                                        <span class="free-tier"><?php _e('Free', 'epic-membership'); ?></span>

                                    <?php endif; ?>

                                </td>

                                <td class="column-duration" data-colname="<?php esc_attr_e('Duration', 'epic-membership'); ?>">

                                    <?php if ($tier->duration_days): ?>

                                        <?php printf(_n('%d day', '%d days', $tier->duration_days, 'epic-membership'), $tier->duration_days); ?>

                                    <?php else: ?>

                                        <?php _e('Lifetime', 'epic-membership'); ?>

                                    <?php endif; ?>

                                </td>

                                <td class="column-members" data-colname="<?php esc_attr_e('Active Members', 'epic-membership'); ?>">

                                    <a href="<?php echo esc_url(add_query_arg(array('page' => 'epic-membership-users', 'tier_id' => $tier->id), admin_url('admin.php'))); ?>">

                                        <?php echo intval($stats['active_members']); ?>

                                    </a>

                                </td>

                                <td class="column-status" data-colname="<?php esc_attr_e('Status', 'epic-membership'); ?>">

                                    <button type="button" 

                                            class="button button-small toggle-tier-status" 

                                            data-tier-id="<?php echo esc_attr($tier->id); ?>"

                                            data-current-status="<?php echo esc_attr($tier->is_active); ?>">

                                        <?php if ($tier->is_active): ?>

                                            <span class="status-active"><?php _e('Active', 'epic-membership'); ?></span>

                                        <?php else: ?>

                                            <span class="status-inactive"><?php _e('Inactive', 'epic-membership'); ?></span>

                                        <?php endif; ?>

                                    </button>

                                </td>

                            </tr>

                        <?php endforeach; ?>

                    </tbody>

                </table>

            <?php endif; ?>

        </div>



    <?php elseif (in_array($action, array('add', 'edit'))): ?>

        <!-- Add/Edit Tier Form -->

        <div class="epic-membership-tier-form">

            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>">

                <?php wp_nonce_field('epic_membership_save_tier'); ?>

                <input type="hidden" name="action" value="epic_membership_save_tier">

                <?php if ($tier_data): ?>

                    <input type="hidden" name="id" value="<?php echo esc_attr($tier_data->id); ?>">

                <?php endif; ?>



                <table class="form-table" role="presentation">

                    <tbody>

                        <tr>

                            <th scope="row">

                                <label for="tier_name"><?php _e('Tier Name', 'epic-membership'); ?> <span class="required">*</span></label>

                            </th>

                            <td>

                                <input type="text" 

                                       name="name" 

                                       id="tier_name" 

                                       value="<?php echo esc_attr($tier_data->name ?? ''); ?>" 

                                       class="regular-text" 

                                       required>

                                <p class="description"><?php _e('The display name for this membership tier.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="tier_slug"><?php _e('Slug', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="text" 

                                       name="slug" 

                                       id="tier_slug" 

                                       value="<?php echo esc_attr($tier_data->slug ?? ''); ?>" 

                                       class="regular-text">

                                <p class="description"><?php _e('URL-friendly version of the name. Leave empty to auto-generate.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="tier_description"><?php _e('Description', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <textarea name="description" 

                                          id="tier_description" 

                                          rows="4" 

                                          class="large-text"><?php echo esc_textarea($tier_data->description ?? ''); ?></textarea>

                                <p class="description"><?php _e('Brief description of what this tier includes.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="tier_level"><?php _e('Access Level', 'epic-membership'); ?> <span class="required">*</span></label>

                            </th>

                            <td>

                                <input type="number" 

                                       name="level" 

                                       id="tier_level" 

                                       value="<?php echo esc_attr($tier_data->level ?? 0); ?>" 

                                       min="0" 

                                       max="100" 

                                       class="small-text" 

                                       required>

                                <p class="description"><?php _e('Higher levels can access lower level content. 0 = Free, 10 = Premium, 20 = VIP, etc.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="tier_price"><?php _e('Price', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <div style="display: flex; gap: 10px; align-items: center;">

                                    <input type="number"

                                           name="price"

                                           id="tier_price"

                                           value="<?php echo esc_attr($tier_data->price ?? 0); ?>"

                                           min="0"

                                           step="0.01"

                                           class="small-text">

                                    <select name="currency" id="tier_currency" class="regular-text" style="max-width: 200px;">

                                        <?php

                                        $currencies = array(

                                            'USD' => 'US Dollar (USD)',

                                            'EUR' => 'Euro (EUR)',

                                            'GBP' => 'British Pound (GBP)',

                                            'CAD' => 'Canadian Dollar (CAD)',

                                            'AUD' => 'Australian Dollar (AUD)',

                                            'JPY' => 'Japanese Yen (JPY)',

                                            'IDR' => 'Indonesian Rupiah (IDR)',

                                            'SGD' => 'Singapore Dollar (SGD)',

                                            'MYR' => 'Malaysian Ringgit (MYR)',

                                            'THB' => 'Thai Baht (THB)',

                                            'PHP' => 'Philippine Peso (PHP)',

                                            'VND' => 'Vietnamese Dong (VND)'

                                        );



                                        $selected_currency = $tier_data->currency ?? get_option('epic_membership_default_currency', 'USD');



                                        foreach ($currencies as $code => $name) {

                                            echo '<option value="' . esc_attr($code) . '"' . selected($selected_currency, $code, false) . '>' . esc_html($name) . '</option>';

                                        }

                                        ?>

                                    </select>

                                </div>

                                <p class="description"><?php _e('Price and currency for this membership tier. Set price to 0 for free tiers.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row">

                                <label for="tier_duration"><?php _e('Duration (Days)', 'epic-membership'); ?></label>

                            </th>

                            <td>

                                <input type="number" 

                                       name="duration_days" 

                                       id="tier_duration" 

                                       value="<?php echo esc_attr($tier_data->duration_days ?? ''); ?>" 

                                       min="1" 

                                       class="small-text">

                                <p class="description"><?php _e('How many days this membership lasts. Leave empty for lifetime access.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row"><?php _e('Capabilities', 'epic-membership'); ?></th>

                            <td>

                                <?php

                                $available_capabilities = $tiers_manager->get_available_capabilities();

                                $tier_capabilities = $tier_data ? json_decode($tier_data->capabilities, true) : array();

                                ?>

                                <fieldset>

                                    <?php foreach ($available_capabilities as $cap_key => $cap_label): ?>

                                        <label>

                                            <input type="checkbox" 

                                                   name="capabilities[]" 

                                                   value="<?php echo esc_attr($cap_key); ?>"

                                                   <?php checked(in_array($cap_key, $tier_capabilities)); ?>>

                                            <?php echo esc_html($cap_label); ?>

                                        </label><br>

                                    <?php endforeach; ?>

                                </fieldset>

                                <p class="description"><?php _e('Select the capabilities this tier provides.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                        

                        <tr>

                            <th scope="row"><?php _e('Status', 'epic-membership'); ?></th>

                            <td>

                                <label>

                                    <input type="checkbox" 

                                           name="is_active" 

                                           value="1" 

                                           <?php checked($tier_data->is_active ?? 1); ?>>

                                    <?php _e('Active', 'epic-membership'); ?>

                                </label>

                                <p class="description"><?php _e('Inactive tiers cannot be assigned to users.', 'epic-membership'); ?></p>

                            </td>

                        </tr>

                    </tbody>

                </table>



                <p class="submit">

                    <input type="submit" 

                           name="submit" 

                           id="submit" 

                           class="button button-primary" 

                           value="<?php echo $tier_data ? esc_attr__('Update Tier', 'epic-membership') : esc_attr__('Create Tier', 'epic-membership'); ?>">

                    <a href="<?php echo esc_url(remove_query_arg(array('action', 'tier_id'))); ?>" class="button">

                        <?php _e('Cancel', 'epic-membership'); ?>

                    </a>

                </p>

            </form>

        </div>

    <?php endif; ?>

</div>



<style>

.tier-level-badge {

    display: inline-block;

    padding: 2px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-align: center;

    min-width: 20px;

}



.tier-level-badge.level-0 {

    background: #e0e0e0;

    color: #666;

}



.tier-level-badge.level-10 {

    background: #d4edda;

    color: #155724;

}



.tier-level-badge.level-20 {

    background: #d1ecf1;

    color: #0c5460;

}



.free-tier {

    color: #666;

    font-style: italic;

}



.status-active {

    color: #00a32a;

}



.status-inactive {

    color: #d63638;

}



.required {

    color: #d63638;

}



tr.inactive {

    opacity: 0.6;

}

</style>



<script>

jQuery(document).ready(function($) {

    // Handle tier status toggle

    $('.toggle-tier-status').on('click', function() {

        var $button = $(this);

        var tierId = $button.data('tier-id');

        var currentStatus = $button.data('current-status');

        

        $.ajax({

            url: ajaxurl,

            type: 'POST',

            data: {

                action: 'epic_membership_toggle_tier_status',

                tier_id: tierId,

                nonce: '<?php echo wp_create_nonce('epic_membership_admin_nonce'); ?>'

            },

            beforeSend: function() {

                $button.prop('disabled', true);

            },

            success: function(response) {

                if (response.success) {

                    location.reload();

                } else {

                    alert('Error: ' + response.data);

                }

            },

            error: function() {

                alert('An error occurred. Please try again.');

            },

            complete: function() {

                $button.prop('disabled', false);

            }

        });

    });

    

    // Auto-generate slug from name

    $('#tier_name').on('input', function() {

        var name = $(this).val();

        var slug = name.toLowerCase()

                      .replace(/[^a-z0-9]+/g, '-')

                      .replace(/^-+|-+$/g, '');

        $('#tier_slug').val(slug);

    });

});

</script>

