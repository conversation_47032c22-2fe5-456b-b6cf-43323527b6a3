<?php

/**

 * PayPal Payment Gateway for Epic Membership Plugin

 * Handles PayPal Standard Checkout integration

 */



if (!defined('ABSPATH')) {

    exit;

}



class Epic_Membership_PayPal_Gateway {

    

    /**

     * Gateway ID

     */

    const GATEWAY_ID = 'paypal';

    

    /**

     * PayPal API endpoints

     */

    const SANDBOX_API_URL = 'https://api-m.sandbox.paypal.com';

    const LIVE_API_URL = 'https://api-m.paypal.com';

    

    /**

     * Database instance

     */

    private $database;

    

    /**

     * Gateway settings

     */

    private $settings;

    

    /**

     * API client

     */

    private $api_client;

    

    /**

     * Constructor

     */

    public function __construct() {

        $this->database = new Epic_Membership_Database();

        $this->load_settings();

        $this->init_hooks();

    }

    

    /**

     * Initialize WordPress hooks

     */

    private function init_hooks() {

        // AJAX handlers for payment processing

        add_action('wp_ajax_epic_membership_create_paypal_order', array($this, 'ajax_create_paypal_order'));

        add_action('wp_ajax_epic_membership_capture_paypal_order', array($this, 'ajax_capture_paypal_order'));

        add_action('wp_ajax_nopriv_epic_membership_paypal_webhook', array($this, 'handle_webhook'));

        add_action('wp_ajax_epic_membership_paypal_webhook', array($this, 'handle_webhook'));

        

        // Payment return handlers

        add_action('init', array($this, 'handle_payment_return'));

        add_action('init', array($this, 'handle_payment_cancel'));

        

        // Enqueue PayPal SDK

        add_action('wp_enqueue_scripts', array($this, 'enqueue_paypal_sdk'));

    }

    

    /**

     * Load gateway settings

     */

    private function load_settings() {

        $this->settings = array(

            'enabled' => get_option('epic_membership_paypal_enabled', false),

            'sandbox_mode' => get_option('epic_membership_paypal_sandbox_mode', true),

            'client_id' => get_option('epic_membership_paypal_client_id', ''),

            'client_secret' => get_option('epic_membership_paypal_client_secret', ''),

            'sandbox_client_id' => get_option('epic_membership_paypal_sandbox_client_id', ''),

            'sandbox_client_secret' => get_option('epic_membership_paypal_sandbox_client_secret', ''),

            'webhook_id' => get_option('epic_membership_paypal_webhook_id', ''),

            'currency' => get_option('epic_membership_paypal_currency', 'USD'),

            'debug_mode' => get_option('epic_membership_paypal_debug_mode', false)

        );

    }

    

    /**

     * Get API URL based on sandbox mode

     */

    private function get_api_url() {

        return $this->settings['sandbox_mode'] ? self::SANDBOX_API_URL : self::LIVE_API_URL;

    }

    

    /**

     * Get client credentials based on sandbox mode

     */

    private function get_client_credentials() {

        if ($this->settings['sandbox_mode']) {

            return array(

                'client_id' => $this->settings['sandbox_client_id'],

                'client_secret' => $this->settings['sandbox_client_secret']

            );

        } else {

            return array(

                'client_id' => $this->settings['client_id'],

                'client_secret' => $this->settings['client_secret']

            );

        }

    }

    

    /**

     * Get access token from PayPal

     */

    private function get_access_token() {

        $credentials = $this->get_client_credentials();

        

        if (empty($credentials['client_id']) || empty($credentials['client_secret'])) {

            throw new Exception(__('PayPal credentials not configured', 'epic-membership'));

        }

        

        $api_url = $this->get_api_url();

        $auth_url = $api_url . '/v1/oauth2/token';

        

        $args = array(

            'method' => 'POST',

            'headers' => array(

                'Accept' => 'application/json',

                'Accept-Language' => 'en_US',

                'Authorization' => 'Basic ' . base64_encode($credentials['client_id'] . ':' . $credentials['client_secret'])

            ),

            'body' => 'grant_type=client_credentials',

            'timeout' => 30

        );

        

        $response = wp_remote_request($auth_url, $args);

        

        if (is_wp_error($response)) {

            throw new Exception(__('PayPal API connection failed: ', 'epic-membership') . $response->get_error_message());

        }

        

        $body = wp_remote_retrieve_body($response);

        $data = json_decode($body, true);

        

        if (!isset($data['access_token'])) {

            throw new Exception(__('Failed to get PayPal access token', 'epic-membership'));

        }

        

        return $data['access_token'];

    }

    

    /**

     * Make API request to PayPal

     */

    private function api_request($endpoint, $method = 'GET', $data = null) {

        try {

            $access_token = $this->get_access_token();

            $api_url = $this->get_api_url();

            $url = $api_url . $endpoint;

            

            $args = array(

                'method' => $method,

                'headers' => array(

                    'Content-Type' => 'application/json',

                    'Authorization' => 'Bearer ' . $access_token,

                    'PayPal-Request-Id' => wp_generate_uuid4()

                ),

                'timeout' => 30

            );

            

            if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {

                $args['body'] = json_encode($data);

            }

            

            $response = wp_remote_request($url, $args);

            

            if (is_wp_error($response)) {

                throw new Exception(__('PayPal API request failed: ', 'epic-membership') . $response->get_error_message());

            }

            

            $response_code = wp_remote_retrieve_response_code($response);

            $body = wp_remote_retrieve_body($response);

            $data = json_decode($body, true);

            

            if ($response_code >= 400) {

                $error_message = isset($data['message']) ? $data['message'] : __('PayPal API error', 'epic-membership');

                throw new Exception($error_message);

            }

            

            return $data;

            

        } catch (Exception $e) {

            $this->log_error('API Request Error: ' . $e->getMessage());

            throw $e;

        }

    }

    

    /**

     * Create PayPal order

     */

    public function create_order($tier_id, $user_id) {

        try {

            // Get tier information

            global $wpdb;

            $tiers_table = $this->database->get_table('tiers');

            $tier = $wpdb->get_row($wpdb->prepare(

                "SELECT * FROM $tiers_table WHERE id = %d AND is_active = 1",

                $tier_id

            ));

            

            if (!$tier) {

                throw new Exception(__('Invalid membership tier', 'epic-membership'));

            }

            

            // Prepare order data

            $order_data = array(

                'intent' => 'CAPTURE',

                'purchase_units' => array(

                    array(

                        'reference_id' => 'EPIC_MEMBERSHIP_' . $tier_id . '_' . $user_id . '_' . time(),

                        'amount' => array(

                            'currency_code' => $this->settings['currency'],

                            'value' => number_format($tier->price, 2, '.', '')

                        ),

                        'description' => sprintf(

                            __('%s Membership - %s', 'epic-membership'),

                            $tier->name,

                            get_bloginfo('name')

                        )

                    )

                ),

                'application_context' => array(

                    'brand_name' => get_bloginfo('name'),

                    'locale' => 'en-US',

                    'landing_page' => 'BILLING',

                    'shipping_preference' => 'NO_SHIPPING',

                    'user_action' => 'PAY_NOW',

                    'return_url' => add_query_arg(array(

                        'epic_paypal_return' => '1',

                        'tier_id' => $tier_id,

                        'user_id' => $user_id

                    ), home_url()),

                    'cancel_url' => add_query_arg(array(

                        'epic_paypal_cancel' => '1',

                        'tier_id' => $tier_id

                    ), home_url())

                )

            );

            

            // Create order via PayPal API

            $response = $this->api_request('/v2/checkout/orders', 'POST', $order_data);

            

            if (!isset($response['id'])) {

                throw new Exception(__('Failed to create PayPal order', 'epic-membership'));

            }

            

            // Store transaction record

            $this->create_transaction_record($user_id, $tier_id, $response);

            

            return $response;

            

        } catch (Exception $e) {

            $this->log_error('Create Order Error: ' . $e->getMessage());

            throw $e;

        }

    }

    

    /**

     * Capture PayPal order

     */

    public function capture_order($order_id) {

        try {

            $response = $this->api_request('/v2/checkout/orders/' . $order_id . '/capture', 'POST');

            

            if (!isset($response['status'])) {

                throw new Exception(__('Failed to capture PayPal order', 'epic-membership'));

            }

            

            // Update transaction record

            $this->update_transaction_record($order_id, $response);

            

            return $response;

            

        } catch (Exception $e) {

            $this->log_error('Capture Order Error: ' . $e->getMessage());

            throw $e;

        }

    }

    

    /**

     * Create transaction record

     */

    private function create_transaction_record($user_id, $tier_id, $paypal_response) {

        global $wpdb;



        $transactions_table = $this->database->get_table('payment_transactions');



        // Log the full PayPal response for debugging

        $this->log_debug('PayPal Order Response: ' . json_encode($paypal_response));



        // Validate required data

        if (empty($paypal_response['id'])) {

            $this->log_error('Transaction creation failed: Missing PayPal order ID');

            throw new Exception('Missing PayPal order ID');

        }



        // Get tier information to extract amount and currency

        $tiers_table = $this->database->get_table('tiers');

        $tier = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $tiers_table WHERE id = %d",

            $tier_id

        ));



        if (!$tier) {

            $this->log_error('Transaction creation failed: Tier not found');

            throw new Exception('Tier not found');

        }



        // Extract amount and currency from PayPal response or use tier data as fallback

        $amount = null;

        $currency = $this->settings['currency'];



        // Try to get amount from PayPal response (different possible structures)

        if (isset($paypal_response['purchase_units'][0]['amount']['value'])) {

            $amount = floatval($paypal_response['purchase_units'][0]['amount']['value']);

            $currency = sanitize_text_field($paypal_response['purchase_units'][0]['amount']['currency_code']);

        } elseif (isset($paypal_response['purchase_units'][0]['amount'])) {

            // Log the structure we found

            $this->log_debug('PayPal amount structure: ' . json_encode($paypal_response['purchase_units'][0]['amount']));

        }



        // Fallback to tier price if amount not found in PayPal response

        if ($amount === null) {

            $amount = floatval($tier->price);

            $this->log_debug('Using tier price as fallback: ' . $amount);

        }



        $transaction_data = array(

            'user_id' => intval($user_id),

            'tier_id' => intval($tier_id),

            'payment_gateway' => self::GATEWAY_ID,

            'transaction_id' => sanitize_text_field($paypal_response['id']),

            'paypal_order_id' => sanitize_text_field($paypal_response['id']),

            'amount' => $amount,

            'currency' => $currency,

            'payment_status' => 'pending',

            'gateway_status' => sanitize_text_field($paypal_response['status'] ?? 'CREATED'),

            'transaction_data' => json_encode($paypal_response)

        );



        $this->log_debug('Attempting to create transaction record: ' . json_encode($transaction_data));



        $result = $wpdb->insert($transactions_table, $transaction_data);



        if ($result === false) {

            $error_message = 'Failed to create transaction record. Database error: ' . $wpdb->last_error;

            $this->log_error($error_message);

            throw new Exception($error_message);

        }



        $insert_id = $wpdb->insert_id;

        $this->log_debug('Transaction record created successfully with ID: ' . $insert_id);



        return $result;

    }

    

    /**

     * Update transaction record

     */

    private function update_transaction_record($order_id, $paypal_response) {

        global $wpdb;



        $transactions_table = $this->database->get_table('payment_transactions');



        $update_data = array(

            'gateway_status' => $paypal_response['status'],

            'transaction_data' => json_encode($paypal_response),

            'processed_at' => current_time('mysql')

        );



        // Extract payment details if available

        if (isset($paypal_response['purchase_units'][0]['payments']['captures'][0])) {

            $capture = $paypal_response['purchase_units'][0]['payments']['captures'][0];

            $update_data['paypal_payment_id'] = $capture['id'];

            $update_data['payment_status'] = strtolower($capture['status']);



            if (isset($paypal_response['payer'])) {

                $update_data['payer_email'] = $paypal_response['payer']['email_address'] ?? null;

                $update_data['payer_id'] = $paypal_response['payer']['payer_id'] ?? null;

            }

        }



        return $wpdb->update(

            $transactions_table,

            $update_data,

            array('paypal_order_id' => $order_id)

        );

    }



    /**

     * AJAX handler for creating PayPal order

     */

    public function ajax_create_paypal_order() {

        try {

            // Verify nonce

            if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {

                throw new Exception(__('Security check failed', 'epic-membership'));

            }



            // Check if user is logged in

            if (!is_user_logged_in()) {

                throw new Exception(__('Please log in to purchase membership', 'epic-membership'));

            }



            $tier_id = intval($_POST['tier_id']);

            $user_id = get_current_user_id();



            if (!$tier_id) {

                throw new Exception(__('Invalid membership tier', 'epic-membership'));

            }



            // Check if PayPal is enabled

            if (!$this->settings['enabled']) {

                throw new Exception(__('PayPal payments are currently disabled', 'epic-membership'));

            }



            $order = $this->create_order($tier_id, $user_id);



            wp_send_json_success(array(

                'order_id' => $order['id'],

                'approval_url' => $this->get_approval_url($order)

            ));



        } catch (Exception $e) {

            wp_send_json_error($e->getMessage());

        }

    }



    /**

     * AJAX handler for capturing PayPal order

     */

    public function ajax_capture_paypal_order() {

        try {

            // Verify nonce

            if (!wp_verify_nonce($_POST['nonce'], 'epic_membership_nonce')) {

                throw new Exception(__('Security check failed', 'epic-membership'));

            }



            $order_id = sanitize_text_field($_POST['order_id']);



            if (!$order_id) {

                throw new Exception(__('Invalid order ID', 'epic-membership'));

            }



            $response = $this->capture_order($order_id);



            // Process membership activation if payment successful

            if ($response['status'] === 'COMPLETED') {

                $this->process_successful_payment($order_id);

            }



            wp_send_json_success($response);



        } catch (Exception $e) {

            wp_send_json_error($e->getMessage());

        }

    }



    /**

     * Get approval URL from PayPal order response

     */

    private function get_approval_url($order) {

        if (isset($order['links'])) {

            foreach ($order['links'] as $link) {

                if ($link['rel'] === 'approve') {

                    return $link['href'];

                }

            }

        }

        return null;

    }



    /**

     * Process successful payment

     */

    private function process_successful_payment($order_id) {

        global $wpdb;



        $transactions_table = $this->database->get_table('payment_transactions');



        // Get transaction record

        $transaction = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $transactions_table WHERE paypal_order_id = %s",

            $order_id

        ));



        if (!$transaction) {

            throw new Exception(__('Transaction record not found', 'epic-membership'));

        }



        // Get tier information

        $tiers_table = $this->database->get_table('tiers');

        $tier = $wpdb->get_row($wpdb->prepare(

            "SELECT * FROM $tiers_table WHERE id = %d",

            $transaction->tier_id

        ));



        if (!$tier) {

            throw new Exception(__('Membership tier not found', 'epic-membership'));

        }



        // Create or update membership

        $membership_id = $this->create_membership($transaction->user_id, $tier);



        // Update transaction with membership ID

        $wpdb->update(

            $transactions_table,

            array(

                'membership_id' => $membership_id,

                'payment_status' => 'completed'

            ),

            array('id' => $transaction->id)

        );



        // Send confirmation email (if enabled)

        $this->send_payment_confirmation($transaction, $tier);



        return $membership_id;

    }



    /**

     * Create membership for user

     */

    private function create_membership($user_id, $tier) {

        global $wpdb;



        $memberships_table = $this->database->get_table('user_memberships');



        // Deactivate existing memberships

        $wpdb->update(

            $memberships_table,

            array('is_active' => 0),

            array('user_id' => $user_id, 'is_active' => 1)

        );



        // Calculate end date

        $start_date = current_time('mysql');

        $end_date = null;



        if ($tier->duration_days) {

            $end_date = date('Y-m-d H:i:s', strtotime($start_date . " + {$tier->duration_days} days"));

        }



        // Create new membership

        $membership_data = array(

            'user_id' => $user_id,

            'tier_id' => $tier->id,

            'start_date' => $start_date,

            'end_date' => $end_date,

            'is_active' => 1,

            'payment_status' => 'completed',

            'notes' => sprintf(__('Purchased via PayPal on %s', 'epic-membership'), current_time('Y-m-d H:i:s'))

        );



        $wpdb->insert($memberships_table, $membership_data);



        return $wpdb->insert_id;

    }



    /**

     * Send payment confirmation email

     */

    private function send_payment_confirmation($transaction, $tier) {

        $user = get_user_by('id', $transaction->user_id);



        if (!$user) {

            return false;

        }



        $subject = sprintf(__('[%s] Payment Confirmation - %s Membership', 'epic-membership'),

            get_bloginfo('name'), $tier->name);



        $message = sprintf(

            __("Hello %s,\n\nThank you for your payment! Your %s membership has been activated.\n\nTransaction Details:\n- Amount: %s %s\n- Transaction ID: %s\n- Date: %s\n\nYou can now access all premium content.\n\nThank you for your business!\n\n%s", 'epic-membership'),

            $user->display_name,

            $tier->name,

            $transaction->amount,

            $transaction->currency,

            $transaction->transaction_id,

            $transaction->created_at,

            get_bloginfo('name')

        );



        return wp_mail($user->user_email, $subject, $message);

    }



    /**

     * Handle payment return from PayPal

     */

    public function handle_payment_return() {

        if (!isset($_GET['epic_paypal_return'])) {

            return;

        }



        $order_id = sanitize_text_field($_GET['token'] ?? '');

        $tier_id = intval($_GET['tier_id'] ?? 0);

        $user_id = intval($_GET['user_id'] ?? 0);



        if (!$order_id || !$tier_id || !$user_id) {

            wp_redirect(add_query_arg('payment_error', 'invalid_params', home_url()));

            exit;

        }



        try {

            // Capture the payment

            $response = $this->capture_order($order_id);



            if ($response['status'] === 'COMPLETED') {

                $this->process_successful_payment($order_id);

                wp_redirect(add_query_arg('payment_success', '1', home_url()));

            } else {

                wp_redirect(add_query_arg('payment_error', 'payment_failed', home_url()));

            }



        } catch (Exception $e) {

            $this->log_error('Payment return error: ' . $e->getMessage());

            wp_redirect(add_query_arg('payment_error', 'processing_failed', home_url()));

        }



        exit;

    }



    /**

     * Handle payment cancellation

     */

    public function handle_payment_cancel() {

        if (!isset($_GET['epic_paypal_cancel'])) {

            return;

        }



        wp_redirect(add_query_arg('payment_cancelled', '1', home_url()));

        exit;

    }



    /**

     * Handle PayPal webhooks

     */

    public function handle_webhook() {

        try {

            $raw_body = file_get_contents('php://input');

            $webhook_data = json_decode($raw_body, true);



            if (!$webhook_data) {

                throw new Exception('Invalid webhook data');

            }



            // Verify webhook signature (if webhook ID is configured)

            if (!empty($this->settings['webhook_id'])) {

                $this->verify_webhook_signature($raw_body, $_SERVER);

            }



            // Process webhook event

            $this->process_webhook_event($webhook_data);



            wp_send_json_success('Webhook processed');



        } catch (Exception $e) {

            $this->log_error('Webhook error: ' . $e->getMessage());

            wp_send_json_error($e->getMessage());

        }

    }



    /**

     * Process webhook event

     */

    private function process_webhook_event($webhook_data) {

        $event_type = $webhook_data['event_type'] ?? '';



        switch ($event_type) {

            case 'CHECKOUT.ORDER.APPROVED':

            case 'PAYMENT.CAPTURE.COMPLETED':

                $this->handle_payment_completed_webhook($webhook_data);

                break;



            case 'PAYMENT.CAPTURE.DENIED':

            case 'PAYMENT.CAPTURE.DECLINED':

                $this->handle_payment_failed_webhook($webhook_data);

                break;



            default:

                $this->log_error('Unhandled webhook event: ' . $event_type);

                break;

        }

    }



    /**

     * Handle payment completed webhook

     */

    private function handle_payment_completed_webhook($webhook_data) {

        $order_id = $webhook_data['resource']['supplementary_data']['related_ids']['order_id'] ?? null;



        if (!$order_id) {

            throw new Exception('Order ID not found in webhook data');

        }



        // Update transaction record

        global $wpdb;

        $transactions_table = $this->database->get_table('payment_transactions');



        $wpdb->update(

            $transactions_table,

            array(

                'payment_status' => 'completed',

                'webhook_data' => json_encode($webhook_data),

                'processed_at' => current_time('mysql')

            ),

            array('paypal_order_id' => $order_id)

        );



        // Process membership activation if not already done

        try {

            $this->process_successful_payment($order_id);

        } catch (Exception $e) {

            // Membership might already be activated, log but don't fail

            $this->log_error('Webhook membership processing: ' . $e->getMessage());

        }

    }



    /**

     * Handle payment failed webhook

     */

    private function handle_payment_failed_webhook($webhook_data) {

        $order_id = $webhook_data['resource']['supplementary_data']['related_ids']['order_id'] ?? null;



        if (!$order_id) {

            throw new Exception('Order ID not found in webhook data');

        }



        // Update transaction record

        global $wpdb;

        $transactions_table = $this->database->get_table('payment_transactions');



        $wpdb->update(

            $transactions_table,

            array(

                'payment_status' => 'failed',

                'webhook_data' => json_encode($webhook_data),

                'processed_at' => current_time('mysql')

            ),

            array('paypal_order_id' => $order_id)

        );

    }



    /**

     * Enqueue PayPal SDK

     */

    public function enqueue_paypal_sdk() {

        if (!$this->settings['enabled']) {

            return;

        }



        $credentials = $this->get_client_credentials();



        if (empty($credentials['client_id'])) {

            return;

        }



        $sdk_url = add_query_arg(array(

            'client-id' => $credentials['client_id'],

            'currency' => $this->settings['currency'],

            'intent' => 'capture'

        ), 'https://www.paypal.com/sdk/js');



        wp_enqueue_script(

            'paypal-sdk',

            $sdk_url,

            array(),

            null,

            true

        );



        // Enqueue our PayPal integration script

        wp_enqueue_script(

            'epic-membership-paypal',

            EPIC_MEMBERSHIP_PLUGIN_URL . 'assets/js/paypal-integration.js',

            array('jquery', 'paypal-sdk'),

            EPIC_MEMBERSHIP_VERSION,

            true

        );



        wp_localize_script('epic-membership-paypal', 'epicMembershipPayPal', array(

            'ajaxUrl' => admin_url('admin-ajax.php'),

            'nonce' => wp_create_nonce('epic_membership_nonce'),

            'currency' => $this->settings['currency'],

            'strings' => array(

                'processing' => __('Processing payment...', 'epic-membership'),

                'error' => __('Payment failed. Please try again.', 'epic-membership'),

                'success' => __('Payment successful! Redirecting...', 'epic-membership')

            )

        ));

    }



    /**

     * Verify webhook signature

     */

    private function verify_webhook_signature($raw_body, $headers) {

        // PayPal webhook signature verification

        // This is a simplified version - in production, implement full verification

        $webhook_id = $this->settings['webhook_id'];



        if (empty($webhook_id)) {

            throw new Exception('Webhook ID not configured');

        }



        // For now, just log that verification should be implemented

        $this->log_error('Webhook signature verification should be implemented for production use');



        return true;

    }



    /**

     * Log error message

     */

    private function log_error($message) {

        if ($this->settings['debug_mode'] || (defined('WP_DEBUG') && WP_DEBUG)) {

            error_log('Epic Membership PayPal Gateway ERROR: ' . $message);

        }

    }



    /**

     * Log debug message

     */

    private function log_debug($message) {

        if ($this->settings['debug_mode'] || (defined('WP_DEBUG') && WP_DEBUG)) {

            error_log('Epic Membership PayPal Gateway DEBUG: ' . $message);

        }

    }

}

