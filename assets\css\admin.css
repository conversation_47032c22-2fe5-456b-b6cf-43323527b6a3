/**

 * Epic Membership Plugin - Admin Styles

 */



/* Dashboard Styles */

.epic-membership-dashboard-stats .stats-grid {

    display: grid;

    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    gap: 20px;

    margin-bottom: 30px;

}



.epic-membership-dashboard-stats .stat-card {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    display: flex;

    align-items: center;

    gap: 15px;

    box-shadow: 0 1px 1px rgba(0,0,0,0.04);

}



/* Meta Box Styles */

.epic-membership-meta-box .form-table {

    margin-top: 10px;

}



.epic-membership-meta-box .form-table td {

    padding: 8px 0;

}



.epic-membership-conditional-field {

    border-top: 1px solid #ddd;

    margin-top: 15px;

    padding-top: 15px;

}



.epic-membership-preview-section {

    margin-top: 20px;

    padding-top: 15px;

    border-top: 1px solid #ddd;

}



.epic-membership-preview-content {

    background: #f9f9f9;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 15px;

    min-height: 60px;

    font-style: italic;

    color: #666;

}



.epic-membership-preview-content.has-content {

    font-style: normal;

    color: #333;

}



/* Tier Management Styles */

.tier-level-badge {

    display: inline-block;

    padding: 2px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-align: center;

    min-width: 20px;

}



.tier-level-badge.level-0 {

    background: #e0e0e0;

    color: #666;

}



.tier-level-badge.level-10 {

    background: #d4edda;

    color: #155724;

}



.tier-level-badge.level-20 {

    background: #d1ecf1;

    color: #0c5460;

}



.free-tier {

    color: #666;

    font-style: italic;

}



.status-active {

    color: #00a32a;

}



.status-inactive {

    color: #d63638;

}



.required {

    color: #d63638;

}



tr.inactive {

    opacity: 0.6;

}



/* User Management Styles */

.epic-membership-stats-cards {

    display: flex;

    gap: 20px;

    margin: 20px 0;

}



.stats-card {

    background: white;

    border: 1px solid #c3c4c7;

    border-radius: 4px;

    padding: 20px;

    text-align: center;

    flex: 1;

    box-shadow: 0 1px 1px rgba(0,0,0,0.04);

}



.stats-card.warning {

    border-color: #dba617;

    background: #fcf9e8;

}



.stats-number {

    font-size: 32px;

    font-weight: bold;

    color: #1d2327;

    line-height: 1;

}



.stats-label {

    font-size: 13px;

    color: #646970;

    margin-top: 5px;

    text-transform: uppercase;

    letter-spacing: 0.5px;

}



.membership-tier {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

}



.membership-tier.tier-level-0 {

    background: #e0e0e0;

    color: #666;

}



.membership-tier.tier-level-10 {

    background: #d4edda;

    color: #155724;

}



.membership-tier.tier-level-20 {

    background: #d1ecf1;

    color: #0c5460;

}



.no-membership {

    color: #646970;

    font-style: italic;

}



.status-badge {

    display: inline-block;

    padding: 3px 8px;

    border-radius: 3px;

    font-size: 11px;

    font-weight: bold;

    text-transform: uppercase;

}



.status-badge.active {

    background: #d4edda;

    color: #155724;

}



.status-badge.expired {

    background: #f8d7da;

    color: #721c24;

}



.expiring-soon {

    color: #856404;

    font-weight: bold;

}



.expiring-indicator {

    color: #856404;

    font-size: 14px;

}



.lifetime {

    color: #0073aa;

    font-weight: bold;

}



.user-details {

    font-size: 12px;

    color: #646970;

    margin-top: 5px;

}



.user-details div {

    margin: 2px 0;

}



.na {

    color: #646970;

}



/* Modal Styles */

.epic-membership-modal {

    position: fixed;

    top: 0;

    left: 0;

    width: 100%;

    height: 100%;

    background: transparent; /* No background overlay */

    z-index: 100000;

    display: flex;

    align-items: center;

    justify-content: center;

}



.modal-content {

    background: white;

    border-radius: 4px;

    width: 90%;

    max-width: 600px;

    max-height: 90%;

    overflow-y: auto;

}



.modal-header {

    padding: 20px;

    border-bottom: 1px solid #ddd;

    display: flex;

    justify-content: space-between;

    align-items: center;

}



.modal-header h3 {

    margin: 0;

}



.modal-close {

    background: none;

    border: none;

    font-size: 24px;

    cursor: pointer;

    color: #666;

}



.modal-body {

    padding: 20px;

}



.modal-actions {

    margin-top: 20px;

    text-align: right;

}



.modal-actions .button {

    margin-left: 10px;

}



/* Bulk Actions */

#bulk-tier-options,

#bulk-extend-options {

    display: inline-block;

    margin-left: 10px;

}



#bulk-tier-options select,

#bulk-tier-options input,

#bulk-extend-options input {

    margin-right: 5px;

}



/* User Profile Membership Fields */

.membership-status-display {

    background: #f9f9f9;

    border: 1px solid #ddd;

    border-radius: 4px;

    padding: 15px;

    margin-bottom: 10px;

}



.membership-details {

    font-size: 13px;

    line-height: 1.5;

}



.membership-details div {

    margin: 5px 0;

}



.status-indicator.expired {

    color: #d63638;

    font-weight: bold;

}



.status-indicator.expiring {

    color: #dba617;

    font-weight: bold;

}



.membership-quick-actions {

    margin-bottom: 10px;

}



.membership-quick-actions .button {

    margin-right: 10px;

    margin-bottom: 5px;

}



/* Loading States */

.epic-membership-loading {

    opacity: 0.6;

    pointer-events: none;

}



.epic-membership-spinner {

    display: inline-block;

    width: 20px;

    height: 20px;

    border: 2px solid #f3f3f3;

    border-top: 2px solid #0073aa;

    border-radius: 50%;

    animation: epic-membership-spin 1s linear infinite;

}



@keyframes epic-membership-spin {

    0% { transform: rotate(0deg); }

    100% { transform: rotate(360deg); }

}



/* Responsive Design */

@media (max-width: 768px) {

    .epic-membership-stats-cards {

        flex-direction: column;

    }

    

    .stats-card {

        margin-bottom: 10px;

    }

    

    .modal-content {

        width: 95%;

        margin: 20px;

    }

    

    .modal-header {

        padding: 15px;

    }

    

    .modal-body {

        padding: 15px;

    }

}



/* Accessibility */

.epic-membership-sr-only {

    position: absolute;

    width: 1px;

    height: 1px;

    padding: 0;

    margin: -1px;

    overflow: hidden;

    clip: rect(0, 0, 0, 0);

    white-space: nowrap;

    border: 0;

}



/* Print Styles */

@media print {

    .epic-membership-modal,

    .modal-overlay,

    .button,

    .quick-actions-grid {

        display: none !important;

    }

}

