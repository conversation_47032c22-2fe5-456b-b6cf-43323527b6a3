/**

 * Admin JavaScript for Epic Membership Ad Units Management

 * Version: 1.0.2 - Updated to fix caching issues

 */



jQuery(document).ready(function($) {

    'use strict';



    // Confirm script is loaded (only in debug mode)

    if (window.console && typeof epicMembershipAdUnits !== 'undefined' && epicMembershipAdUnits.debug) {

        console.log('Epic Membership Ad Units script loaded successfully');

        console.log('Epic Membership Ad Units JS Version: 1.0.2 - Cache busting enabled');

        console.log('epicMembershipAdUnits available:', typeof epicMembershipAdUnits !== 'undefined');

        console.log('Button element found:', $('#add-new-ad-unit').length);

        console.log('Modal element found:', $('#ad-unit-modal').length);



        // Debug information

        if (typeof epicMembershipAdUnits !== 'undefined') {

            console.log('AJAX URL:', epicMembershipAdUnits.ajaxUrl);

            console.log('Nonce:', epicMembershipAdUnits.nonce);

            console.log('Strings:', epicMembershipAdUnits.strings);

        }

    }





    

    // Initialize sortable ad units

    if ($('#ad-units-sortable').length) {

        $('#ad-units-sortable').sortable({

            handle: '.ad-unit-drag-handle',

            placeholder: 'ad-unit-placeholder',

            update: function(event, ui) {

                var orderedIds = [];

                $('#ad-units-sortable .ad-unit-card').each(function() {

                    orderedIds.push($(this).data('ad-unit-id'));

                });

                

                // Save new order via AJAX

                $.post(epicMembershipAdUnits.ajaxUrl, {

                    action: 'epic_membership_reorder_ad_units',

                    nonce: epicMembershipAdUnits.nonce,

                    ordered_ids: orderedIds

                }, function(response) {

                    if (response.success) {

                        showNotice(response.data, 'success');

                    } else {

                        showNotice(response.data || epicMembershipAdUnits.strings.error, 'error');

                    }

                });

            }

        });

    }

    

    // Add new ad unit button

    $('#add-new-ad-unit').on('click', function() {

        console.log('Add New Ad Unit button clicked');

        console.log('Opening modal directly...');

        openAdUnitModal();

    });

    

    // Edit ad unit buttons

    $(document).on('click', '.edit-ad-unit', function() {

        var adUnitId = $(this).data('ad-unit-id');

        loadAdUnitForEdit(adUnitId);

    });

    

    // Delete ad unit buttons

    $(document).on('click', '.delete-ad-unit', function() {

        var adUnitId = $(this).data('ad-unit-id');

        var adUnitName = $(this).closest('.ad-unit-card').find('.ad-unit-name').text();

        

        if (confirm(epicMembershipAdUnits.strings.confirmDelete + '\n\n"' + adUnitName + '"')) {

            deleteAdUnit(adUnitId);

        }

    });

    

    // Toggle ad unit status

    $(document).on('change', '.ad-unit-toggle', function() {

        var adUnitId = $(this).data('ad-unit-id');

        var $toggle = $(this);

        

        $.post(epicMembershipAdUnits.ajaxUrl, {

            action: 'epic_membership_toggle_ad_unit',

            nonce: epicMembershipAdUnits.nonce,

            ad_unit_id: adUnitId

        }, function(response) {

            if (response.success) {

                showNotice(response.data.message, 'success');

                // Update toggle state

                $toggle.prop('checked', response.data.new_status == 1);

            } else {

                // Revert toggle state

                $toggle.prop('checked', !$toggle.prop('checked'));

                showNotice(response.data || epicMembershipAdUnits.strings.error, 'error');

            }

        });

    });

    

    // Modal close buttons

    $('.epic-modal-close, #cancel-ad-unit').on('click', function() {

        closeAdUnitModal();

    });

    

    // Save ad unit button

    $('#save-ad-unit').on('click', function() {

        saveAdUnit();

    });

    

    // Close modal when clicking outside

    $(document).on('click', '.epic-modal', function(e) {

        if (e.target === this) {

            closeAdUnitModal();

        }

    });

    

    /**

     * Open ad unit modal for new ad unit

     */

    function openAdUnitModal() {

        console.log('openAdUnitModal function called');

        $('#modal-title').text('Add New Ad Unit');

        $('#ad-unit-form')[0].reset();

        $('#ad-unit-id').val('');

        $('#ad-unit-modal').show();

        console.log('Modal should now be visible');

    }

    

    /**

     * Load ad unit data for editing

     */

    function loadAdUnitForEdit(adUnitId) {

        // Show loading state

        $('#modal-title').text('Loading...');

        $('#ad-unit-modal').show();



        // Fetch ad unit data via AJAX

        $.post(epicMembershipAdUnits.ajaxUrl, {

            action: 'epic_membership_get_ad_unit',

            nonce: epicMembershipAdUnits.nonce,

            ad_unit_id: adUnitId

        }, function(response) {

            if (response.success) {

                var adUnit = response.data;



                // Populate form

                $('#modal-title').text('Edit Ad Unit');

                $('#ad-unit-id').val(adUnit.id);

                $('#ad-unit-name').val(adUnit.name);

                $('#ad-unit-type').val(adUnit.ad_type);

                $('#ad-unit-placement').val(adUnit.placement);

                $('#ad-unit-code').val(adUnit.ad_code);

                $('#ad-unit-active').prop('checked', adUnit.is_active == 1);

            } else {

                showNotice(response.data || epicMembershipAdUnits.strings.error, 'error');

                closeAdUnitModal();

            }

        }).fail(function() {

            showNotice(epicMembershipAdUnits.strings.error, 'error');

            closeAdUnitModal();

        });

    }

    

    /**

     * Close ad unit modal

     */

    function closeAdUnitModal() {

        $('#ad-unit-modal').hide();

        $('#ad-unit-form')[0].reset();

    }

    

    /**

     * Save ad unit

     */

    function saveAdUnit() {

        var $button = $('#save-ad-unit');

        var originalText = $button.text();



        // Validate form

        if (!$('#ad-unit-name').val() || !$('#ad-unit-type').val() ||

            !$('#ad-unit-placement').val() || !$('#ad-unit-code').val()) {

            showNotice('Please fill in all required fields.', 'error');

            return;

        }



        // Show loading state

        $button.text(epicMembershipAdUnits.strings.saving).prop('disabled', true);



        var adCode = $('#ad-unit-code').val();

        var shouldEncode = false;



        // Check if ad code contains potentially problematic content for WAF

        var wafTriggers = [

            '<script',

            'eval(',

            'document.write',

            'innerHTML',

            'javascript:',

            'vbscript:',

            'onload=',

            'onerror=',

            'onclick='

        ];



        var adCodeLower = adCode.toLowerCase();

        for (var i = 0; i < wafTriggers.length; i++) {

            if (adCodeLower.indexOf(wafTriggers[i]) !== -1) {

                shouldEncode = true;

                console.log('Epic Membership: Encoding ad code due to WAF trigger: ' + wafTriggers[i]);

                break;

            }

        }



        // Prepare form data

        var formData = {

            action: 'epic_membership_save_ad_unit',

            nonce: epicMembershipAdUnits.nonce,

            ad_unit_id: $('#ad-unit-id').val(),

            name: $('#ad-unit-name').val(),

            ad_type: $('#ad-unit-type').val(),

            placement: $('#ad-unit-placement').val(),

            ad_code: shouldEncode ? btoa(adCode) : adCode,

            ad_code_encoded: shouldEncode ? '1' : '0',

            is_active: $('#ad-unit-active').prop('checked') ? 1 : 0

        };



        if (shouldEncode) {

            console.log('Epic Membership: Ad code encoded to bypass WAF');

        }



        // Save via AJAX

        $.post(epicMembershipAdUnits.ajaxUrl, formData)

            .done(function(response) {

                if (response.success) {

                    showNotice(response.data.message, 'success');

                    closeAdUnitModal();

                    // Reload page to show updated ad units

                    location.reload();

                } else {

                    showNotice(response.data || epicMembershipAdUnits.strings.error, 'error');

                }

            })

            .fail(function(xhr, status, error) {

                var errorMessage = 'Request failed';

                console.error('AJAX Error Details:', {

                    status: xhr.status,

                    statusText: xhr.statusText,

                    responseText: xhr.responseText,

                    error: error,

                    url: epicMembershipAdUnits.ajaxUrl,

                    wasEncoded: shouldEncode

                });



                // If we get a 403 and haven't tried encoding yet, try with encoding

                if (xhr.status === 403 && !shouldEncode) {

                    console.log('Epic Membership: 403 error detected, retrying with encoded ad code');

                    formData.ad_code = btoa($('#ad-unit-code').val());

                    formData.ad_code_encoded = '1';



                    // Retry the request

                    $.post(epicMembershipAdUnits.ajaxUrl, formData)

                        .done(function(response) {

                            if (response.success) {

                                showNotice(response.data.message + ' (Encoded for security)', 'success');

                                closeAdUnitModal();

                                location.reload();

                            } else {

                                showNotice(response.data || epicMembershipAdUnits.strings.error, 'error');

                            }

                        })

                        .fail(function(xhr2, status2, error2) {

                            console.error('AJAX Retry Failed:', {

                                status: xhr2.status,

                                statusText: xhr2.statusText,

                                responseText: xhr2.responseText,

                                error: error2

                            });

                            handleAjaxError(xhr2);

                        })

                        .always(function() {

                            $button.text(originalText).prop('disabled', false);

                        });

                    return; // Don't execute the .always() below for the first request

                }



                handleAjaxError(xhr);



                if (xhr.responseText) {

                    try {

                        var errorResponse = JSON.parse(xhr.responseText);

                        errorMessage = errorResponse.data || errorMessage;

                        console.error('Parsed error response:', errorResponse);

                    } catch (e) {

                        console.error('Failed to parse error response:', e);

                        errorMessage = 'Server error occurred (Status: ' + xhr.status + ')';

                        // Include first 200 characters of response for debugging

                        if (xhr.responseText.length > 0) {

                            console.error('Raw response:', xhr.responseText.substring(0, 200));

                        }

                    }

                } else {

                    errorMessage = 'No response from server (Status: ' + xhr.status + ')';

                }

                showNotice(errorMessage, 'error');

            })

            .always(function() {

                $button.text(originalText).prop('disabled', false);

            });

    }



    /**

     * Handle AJAX errors with detailed messaging

     */

    function handleAjaxError(xhr) {

        // Show specific error messages based on status

        if (xhr.status === 403) {

            console.error('Epic Membership: 403 Forbidden - This is likely due to WAF/Security plugin blocking the request');

            console.error('Epic Membership: Common causes: ModSecurity, Cloudflare WAF, Wordfence, or hosting security');

            console.error('Epic Membership: Try contacting your hosting provider or disabling security rules temporarily');



            // Show user-friendly guidance

            var guidance = 'Ad unit save blocked by security system.\n\n';

            guidance += 'QUICK FIXES:\n';

            guidance += '1. Run diagnostic: Click the diagnostic link above or run runWAFDiagnostic() in console\n';

            guidance += '2. Contact hosting provider about WAF/ModSecurity rules\n';

            guidance += '3. Temporarily disable security plugins\n';

            guidance += '4. Check Cloudflare WAF settings if using Cloudflare\n\n';

            guidance += 'The plugin tried to encode your ad code automatically but it was still blocked.\n';

            guidance += 'This is typically a server-level security restriction.';



            if (confirm(guidance + '\n\nWould you like to run the diagnostic tool now?')) {

                runWAFDiagnostic();

            }

        } else if (xhr.status === 500) {

            console.error('Epic Membership: 500 Server Error - Check server error logs');

        } else if (xhr.status === 0) {

            console.error('Epic Membership: Network error or CORS issue');

        }

    }



    /**

     * Delete ad unit

     */

    function deleteAdUnit(adUnitId) {

        $.post(epicMembershipAdUnits.ajaxUrl, {

            action: 'epic_membership_delete_ad_unit',

            nonce: epicMembershipAdUnits.nonce,

            ad_unit_id: adUnitId

        }, function(response) {

            if (response.success) {

                showNotice(response.data, 'success');

                // Remove the ad unit card

                $('.ad-unit-card[data-ad-unit-id="' + adUnitId + '"]').fadeOut(function() {

                    $(this).remove();

                    

                    // Show "no ad units" message if all are deleted

                    if ($('.ad-unit-card').length === 0) {

                        location.reload();

                    }

                });

            } else {

                showNotice(response.data || epicMembershipAdUnits.strings.error, 'error');

            }

        });

    }

    

    /**

     * Show admin notice

     */

    function showNotice(message, type) {

        var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');

        $('.wrap h1').after($notice);

        

        // Auto-dismiss success notices

        if (type === 'success') {

            setTimeout(function() {

                $notice.fadeOut();

            }, 3000);

        }

        

        // Scroll to top to show notice

        $('html, body').animate({ scrollTop: 0 }, 300);

    }

    

    // Ad type descriptions

    var adTypeDescriptions = {

        'popunder': 'High revenue ads that open in a new window/tab behind the current page.',

        'social_bar': 'Non-intrusive ads displayed as a bar at the bottom of the page.',

        'banner': 'Traditional display advertisements shown in designated areas.',

        'native': 'Ads that blend naturally with your site content.',

        'in_page_push': 'Push notification style ads displayed within the page.'

    };

    

    // Show ad type description on selection

    $('#ad-unit-type').on('change', function() {

        var selectedType = $(this).val();

        var $description = $(this).siblings('.description');

        

        if (selectedType && adTypeDescriptions[selectedType]) {

            $description.html(adTypeDescriptions[selectedType]);

        } else {

            $description.html('Choose the type of Adsterra ad format.');

        }

    });



    // WAF Diagnostic functionality

    $('#run-waf-diagnostic').on('click', function(e) {

        e.preventDefault();

        runWAFDiagnostic();

    });



    function runWAFDiagnostic() {

        var testAdCode = $('#ad-unit-code').val() || '';



        console.log('=== Running WAF Diagnostic ===');



        $.post(epicMembershipAdUnits.ajaxUrl, {

            action: 'epic_membership_waf_diagnostic',

            nonce: epicMembershipAdUnits.nonce,

            test_ad_code: testAdCode

        })

        .done(function(response) {

            if (response.success) {

                console.log('=== WAF Diagnostic Results ===');

                var results = response.data;



                for (var testName in results) {

                    var test = results[testName];

                    console.log(test.test + ': ' + test.status.toUpperCase());

                    console.log('  Message: ' + test.message);

                    if (test.data) {

                        console.log('  Data:', test.data);

                    }

                    console.log('---');

                }



                // Show user-friendly summary

                showWAFDiagnosticSummary(results);

            } else {

                console.error('WAF Diagnostic failed:', response.data);

                alert('Diagnostic failed: ' + (response.data || 'Unknown error'));

            }

        })

        .fail(function(xhr, status, error) {

            console.error('WAF Diagnostic AJAX failed:', {

                status: xhr.status,

                statusText: xhr.statusText,

                responseText: xhr.responseText,

                error: error

            });

            alert('Diagnostic request failed. Status: ' + xhr.status);

        });

    }



    function showWAFDiagnosticSummary(results) {

        var summary = 'WAF Diagnostic Summary:\n\n';

        var hasWarnings = false;



        for (var testName in results) {

            var test = results[testName];

            summary += test.test + ': ' + test.status.toUpperCase() + '\n';

            summary += test.message + '\n\n';



            if (test.status === 'warning') {

                hasWarnings = true;

            }

        }



        if (hasWarnings) {

            summary += 'RECOMMENDATIONS:\n';

            summary += '1. Contact your hosting provider about WAF/ModSecurity rules\n';

            summary += '2. Temporarily disable security plugins to test\n';

            summary += '3. Try encoding your ad code (the system will do this automatically)\n';

            summary += '4. Check server error logs for more details\n';

        } else {

            summary += 'No obvious issues detected. The 403 error might be intermittent or server-specific.';

        }



        alert(summary);

    }



    // Global function for testing AJAX connection (accessible from browser console)

    window.testAdUnitsAjax = function() {

        console.log('=== Testing Ad Units AJAX Connection ===');

        console.log('AJAX URL:', epicMembershipAdUnits.ajaxUrl);

        console.log('Nonce:', epicMembershipAdUnits.nonce);



        // Test with a simple action that should work

        $.post(epicMembershipAdUnits.ajaxUrl, {

            action: 'epic_membership_get_ad_unit',

            nonce: epicMembershipAdUnits.nonce,

            ad_unit_id: 999999 // Non-existent ID to test error handling

        })

        .done(function(response) {

            console.log('AJAX Test Success:', response);

        })

        .fail(function(xhr, status, error) {

            console.log('AJAX Test Failed:', {

                status: xhr.status,

                statusText: xhr.statusText,

                responseText: xhr.responseText,

                error: error

            });

        });

    };



    // Global function for WAF diagnostic (accessible from browser console)

    window.runWAFDiagnostic = runWAFDiagnostic;



    // Add version info to console for debugging

    console.log('Epic Membership Ad Units JS Version: 1.0.1 - Cache busting enabled');

});

